using System.Net.Security;
using System.Security.Authentication;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Liam.TcpClient.Models;
using Liam.TcpClient.Security;

namespace Liam.TcpClient.Tests.Security;

/// <summary>
/// SSL/TLS集成测试
/// </summary>
public class SslIntegrationTests
{
    private readonly Mock<ILogger<CertificateValidator>> _mockLogger;

    public SslIntegrationTests()
    {
        _mockLogger = new Mock<ILogger<CertificateValidator>>();
    }

    [Fact]
    public void CertificateValidator_StrictMode_ShouldRejectInvalidCertificate()
    {
        // Arrange
        var sslConfig = new SslConfig
        {
            ValidationMode = CertificateValidationMode.Strict
        };
        var validator = new CertificateValidator(sslConfig, _mockLogger.Object);

        // 创建一个自签名证书用于测试
        var cert = CreateSelfSignedCertificate();
        var chain = new X509Chain();
        var sslPolicyErrors = SslPolicyErrors.RemoteCertificateChainErrors;

        // Act
        var result = validator.ValidateRemoteCertificate(this, cert, chain, sslPolicyErrors);

        // Assert
        result.Should().BeFalse("严格模式应该拒绝有错误的证书");
    }

    [Fact]
    public void CertificateValidator_StrictMode_ShouldAcceptValidCertificate()
    {
        // Arrange
        var sslConfig = new SslConfig
        {
            ValidationMode = CertificateValidationMode.Strict
        };
        var validator = new CertificateValidator(sslConfig, _mockLogger.Object);

        var cert = CreateSelfSignedCertificate();
        var chain = new X509Chain();
        var sslPolicyErrors = SslPolicyErrors.None;

        // Act
        var result = validator.ValidateRemoteCertificate(this, cert, chain, sslPolicyErrors);

        // Assert
        result.Should().BeTrue("严格模式应该接受没有错误的证书");
    }

    [Fact]
    public void CertificateValidator_DevelopmentMode_ShouldAllowSelfSignedCertificates()
    {
        // Arrange
        var sslConfig = new SslConfig
        {
            ValidationMode = CertificateValidationMode.Development,
            AllowSelfSignedCertificates = true
        };
        var validator = new CertificateValidator(sslConfig, _mockLogger.Object);

        var cert = CreateSelfSignedCertificate();
        var chain = new X509Chain();
        var sslPolicyErrors = SslPolicyErrors.RemoteCertificateChainErrors;

        // Act
        var result = validator.ValidateRemoteCertificate(this, cert, chain, sslPolicyErrors);

        // Assert
        result.Should().BeTrue("开发模式应该允许自签名证书");
    }

    [Fact]
    public void CertificateValidator_DevelopmentMode_ShouldAllowNameMismatch()
    {
        // Arrange
        var sslConfig = new SslConfig
        {
            ValidationMode = CertificateValidationMode.Development,
            AllowNameMismatch = true
        };
        var validator = new CertificateValidator(sslConfig, _mockLogger.Object);

        var cert = CreateSelfSignedCertificate();
        var chain = new X509Chain();
        var sslPolicyErrors = SslPolicyErrors.RemoteCertificateNameMismatch;

        // Act
        var result = validator.ValidateRemoteCertificate(this, cert, chain, sslPolicyErrors);

        // Assert
        result.Should().BeTrue("开发模式应该允许名称不匹配");
    }

    [Fact]
    public void CertificateValidator_ThumbprintMode_ShouldValidateByThumbprint()
    {
        // Arrange
        var cert = CreateSelfSignedCertificate();
        var thumbprint = GetCertificateThumbprint(cert);
        
        var sslConfig = new SslConfig
        {
            ValidationMode = CertificateValidationMode.Thumbprint,
            TrustedCertificateThumbprints = { thumbprint }
        };
        var validator = new CertificateValidator(sslConfig, _mockLogger.Object);

        var chain = new X509Chain();
        var sslPolicyErrors = SslPolicyErrors.RemoteCertificateChainErrors;

        // Act
        var result = validator.ValidateRemoteCertificate(this, cert, chain, sslPolicyErrors);

        // Assert
        result.Should().BeTrue("指纹模式应该接受匹配的证书指纹");
    }

    [Fact]
    public void CertificateValidator_ThumbprintMode_ShouldRejectUnknownThumbprint()
    {
        // Arrange
        var sslConfig = new SslConfig
        {
            ValidationMode = CertificateValidationMode.Thumbprint,
            TrustedCertificateThumbprints = { "UNKNOWN_THUMBPRINT" }
        };
        var validator = new CertificateValidator(sslConfig, _mockLogger.Object);

        var cert = CreateSelfSignedCertificate();
        var chain = new X509Chain();
        var sslPolicyErrors = SslPolicyErrors.None;

        // Act
        var result = validator.ValidateRemoteCertificate(this, cert, chain, sslPolicyErrors);

        // Assert
        result.Should().BeFalse("指纹模式应该拒绝未知的证书指纹");
    }

    [Fact]
    public void CertificateValidator_CustomMode_ShouldUseCustomCallback()
    {
        // Arrange
        var customCallbackCalled = false;
        var sslConfig = new SslConfig
        {
            ValidationMode = CertificateValidationMode.Custom,
            RemoteCertificateValidationCallback = (context, cert, chain, errors) =>
            {
                customCallbackCalled = true;
                return true;
            }
        };
        var validator = new CertificateValidator(sslConfig, _mockLogger.Object);

        var cert = CreateSelfSignedCertificate();
        var chain = new X509Chain();
        var sslPolicyErrors = SslPolicyErrors.RemoteCertificateChainErrors;

        // Act
        var result = validator.ValidateRemoteCertificate(this, cert, chain, sslPolicyErrors);

        // Assert
        result.Should().BeTrue("自定义模式应该使用自定义回调");
        customCallbackCalled.Should().BeTrue("自定义回调应该被调用");
    }

    [Fact]
    public void CertificateValidator_ShouldHandleNullCertificate()
    {
        // Arrange
        var sslConfig = new SslConfig
        {
            ValidationMode = CertificateValidationMode.Strict
        };
        var validator = new CertificateValidator(sslConfig, _mockLogger.Object);

        // Act
        var result = validator.ValidateRemoteCertificate(this, null, null, SslPolicyErrors.None);

        // Assert
        result.Should().BeFalse("应该拒绝空证书");
    }

    [Fact]
    public void CertificateValidator_ShouldHandleExceptionInCustomCallback()
    {
        // Arrange
        var sslConfig = new SslConfig
        {
            ValidationMode = CertificateValidationMode.Custom,
            RemoteCertificateValidationCallback = (context, cert, chain, errors) =>
            {
                throw new InvalidOperationException("测试异常");
            }
        };
        var validator = new CertificateValidator(sslConfig, _mockLogger.Object);

        var cert = CreateSelfSignedCertificate();
        var chain = new X509Chain();

        // Act
        var result = validator.ValidateRemoteCertificate(this, cert, chain, SslPolicyErrors.None);

        // Assert
        result.Should().BeFalse("异常情况下应该返回false");
    }

    [Theory]
    [InlineData(SslProtocols.Tls12)]
    [InlineData(SslProtocols.Tls13)]
    [InlineData(SslProtocols.None)] // 让系统选择
    public void SslConfig_ShouldSupportDifferentProtocols(SslProtocols protocol)
    {
        // Arrange & Act
        var sslConfig = new SslConfig
        {
            SslProtocols = protocol,
            ValidationMode = CertificateValidationMode.Development
        };

        // Assert
        sslConfig.SslProtocols.Should().Be(protocol);
        sslConfig.ValidationMode.Should().Be(CertificateValidationMode.Development);
    }

    [Fact]
    public void SslConfig_ShouldValidateConfiguration()
    {
        // Arrange
        var sslConfig = new SslConfig
        {
            ValidationMode = CertificateValidationMode.Thumbprint,
            TrustedCertificateThumbprints = { }, // 空列表
            HandshakeTimeoutSeconds = 30,
            CheckCertificateRevocation = true
        };

        // Act & Assert
        sslConfig.HandshakeTimeoutSeconds.Should().Be(30);
        sslConfig.CheckCertificateRevocation.Should().BeTrue();
        sslConfig.TrustedCertificateThumbprints.Should().BeEmpty();
    }

    /// <summary>
    /// 创建自签名证书用于测试
    /// </summary>
    private static X509Certificate2 CreateSelfSignedCertificate()
    {
        // 创建一个简单的测试证书
        var distinguishedName = new X500DistinguishedName("CN=Test Certificate");
        using var rsa = System.Security.Cryptography.RSA.Create(2048);
        var request = new CertificateRequest(distinguishedName, rsa, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
        
        var certificate = request.CreateSelfSigned(DateTimeOffset.Now, DateTimeOffset.Now.AddYears(1));
        return certificate;
    }

    /// <summary>
    /// 获取证书SHA-256指纹
    /// </summary>
    private static string GetCertificateThumbprint(X509Certificate2 certificate)
    {
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hash = sha256.ComputeHash(certificate.RawData);
        return Convert.ToHexString(hash);
    }
}
