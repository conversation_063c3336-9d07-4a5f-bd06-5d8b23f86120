using Microsoft.Extensions.Logging;
using Liam.TcpClient.Models;
using Liam.TcpClient.Services;
using Liam.TcpClient.Extensions;

namespace Liam.Examples;

/// <summary>
/// TCP客户端使用示例
/// </summary>
public class TcpClientExample
{
    /// <summary>
    /// 基本TCP客户端示例
    /// </summary>
    public static async Task BasicClientExample()
    {
        Console.WriteLine("=== 基本TCP客户端示例 ===");

        // 创建日志工厂
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        // 创建配置
        var config = new TcpClientConfig
        {
            Host = "localhost",
            Port = 8080,
            EnableHeartbeat = true,
            EnableAutoReconnect = true,
            ClientId = "client-001",
            ClientName = "ExampleClient"
        };

        // 创建客户端组件
        var logger = loggerFactory.CreateLogger<TcpClient>();
        var connectionManagerLogger = loggerFactory.CreateLogger<ConnectionManager>();
        var messageHandlerLogger = loggerFactory.CreateLogger<MessageHandler>();
        var heartbeatManagerLogger = loggerFactory.CreateLogger<HeartbeatManager>();

        var connectionManager = new ConnectionManager(connectionManagerLogger);
        var messageHandler = new MessageHandler(messageHandlerLogger);
        var heartbeatManager = new HeartbeatManager(heartbeatManagerLogger, messageHandler);

        // 创建TCP客户端
        using var client = new TcpClient(config, logger, connectionManager, messageHandler, heartbeatManager);

        // 订阅事件
        client.Connected += (sender, e) =>
        {
            Console.WriteLine($"✅ 连接成功: {e.ConnectionInfo.RemoteEndPoint}");
            Console.WriteLine($"   是否重连: {e.IsReconnection}");
        };

        client.Disconnected += (sender, e) =>
        {
            Console.WriteLine($"❌ 连接断开: {e.Reason}");
            Console.WriteLine($"   是否意外: {e.IsUnexpected}");
        };

        client.MessageReceived += (sender, e) =>
        {
            Console.WriteLine($"📨 收到消息: {e.Message.GetText()}");
        };

        client.Error += (sender, e) =>
        {
            Console.WriteLine($"⚠️ 发生错误: {e.Message}");
        };

        try
        {
            // 连接到服务器
            Console.WriteLine("正在连接到服务器...");
            if (await client.ConnectAsync())
            {
                Console.WriteLine("连接成功！");

                // 发送消息
                await client.SendTextAsync("Hello, Server!");
                Console.WriteLine("已发送: Hello, Server!");

                // 等待响应
                var response = await client.ReceiveTextAsync(TimeSpan.FromSeconds(5));
                if (response != null)
                {
                    Console.WriteLine($"收到响应: {response}");
                }

                // 发送JSON对象
                var data = new { Name = "张三", Age = 30, Message = "测试消息" };
                await client.SendJsonAsync(data);
                Console.WriteLine("已发送JSON对象");

                // 显示统计信息
                var statistics = client.GetStatistics();
                Console.WriteLine($"\n📊 统计信息:");
                Console.WriteLine($"   发送字节: {statistics.TotalBytesSent}");
                Console.WriteLine($"   接收字节: {statistics.TotalBytesReceived}");
                Console.WriteLine($"   连接质量: {client.GetConnectionQuality():F1}%");

                // 等待一段时间
                await Task.Delay(2000);
            }
            else
            {
                Console.WriteLine("连接失败！");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"发生异常: {ex.Message}");
        }
    }

    /// <summary>
    /// SSL客户端示例
    /// </summary>
    public static async Task SslClientExample()
    {
        Console.WriteLine("\n=== SSL TCP客户端示例 ===");

        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        // 创建SSL配置
        var config = TcpClientConfig.CreateSslConfig("secure-server.com", 443, "secure-server.com");
        config.ClientId = "ssl-client-001";

        var logger = loggerFactory.CreateLogger<TcpClient>();
        var connectionManagerLogger = loggerFactory.CreateLogger<ConnectionManager>();
        var messageHandlerLogger = loggerFactory.CreateLogger<MessageHandler>();
        var heartbeatManagerLogger = loggerFactory.CreateLogger<HeartbeatManager>();

        var connectionManager = new ConnectionManager(connectionManagerLogger);
        var messageHandler = new MessageHandler(messageHandlerLogger);
        var heartbeatManager = new HeartbeatManager(heartbeatManagerLogger, messageHandler);

        using var client = new TcpClient(config, logger, connectionManager, messageHandler, heartbeatManager);

        try
        {
            Console.WriteLine("正在建立SSL连接...");
            if (await client.ConnectAsync())
            {
                Console.WriteLine("SSL连接成功！");
                
                // 显示SSL信息
                var sslInfo = client.ConnectionInfo?.SslInfo;
                if (sslInfo != null)
                {
                    Console.WriteLine($"🔒 SSL信息:");
                    Console.WriteLine($"   协议: {sslInfo.Protocol}");
                    Console.WriteLine($"   加密算法: {sslInfo.CipherAlgorithm}");
                    Console.WriteLine($"   已认证: {sslInfo.IsAuthenticated}");
                    Console.WriteLine($"   已加密: {sslInfo.IsEncrypted}");
                }

                await client.SendTextAsync("Secure Hello!");
                Console.WriteLine("已通过SSL发送消息");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"SSL连接失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 心跳和重连示例
    /// </summary>
    public static async Task HeartbeatAndReconnectExample()
    {
        Console.WriteLine("\n=== 心跳和重连示例 ===");

        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        var config = new TcpClientConfig
        {
            Host = "localhost",
            Port = 8080,
            EnableHeartbeat = true,
            HeartbeatIntervalSeconds = 10,
            HeartbeatTimeoutSeconds = 5,
            EnableAutoReconnect = true,
            ReconnectIntervalSeconds = 3,
            MaxReconnectAttempts = 5
        };

        var logger = loggerFactory.CreateLogger<TcpClient>();
        var connectionManagerLogger = loggerFactory.CreateLogger<ConnectionManager>();
        var messageHandlerLogger = loggerFactory.CreateLogger<MessageHandler>();
        var heartbeatManagerLogger = loggerFactory.CreateLogger<HeartbeatManager>();

        var connectionManager = new ConnectionManager(connectionManagerLogger);
        var messageHandler = new MessageHandler(messageHandlerLogger);
        var heartbeatManager = new HeartbeatManager(heartbeatManagerLogger, messageHandler);

        using var client = new TcpClient(config, logger, connectionManager, messageHandler, heartbeatManager);

        // 订阅心跳事件
        client.Heartbeat += (sender, e) =>
        {
            switch (e.Type)
            {
                case Liam.TcpClient.Events.HeartbeatType.Request:
                    Console.WriteLine("💓 发送心跳请求");
                    break;
                case Liam.TcpClient.Events.HeartbeatType.Response:
                    Console.WriteLine($"💓 收到心跳响应，延迟: {e.ResponseTime:F2}ms");
                    break;
                case Liam.TcpClient.Events.HeartbeatType.Timeout:
                    Console.WriteLine("💔 心跳超时");
                    break;
            }
        };

        client.ConnectionStateChanged += (sender, e) =>
        {
            Console.WriteLine($"🔄 连接状态变更: {e.OldState} -> {e.NewState}");
        };

        try
        {
            if (await client.ConnectAsync())
            {
                Console.WriteLine("连接成功，心跳和重连机制已启动");
                
                // 运行一段时间观察心跳
                await Task.Delay(30000);
                
                // 测试连接健康状态
                var isHealthy = await client.CheckHealthAsync();
                Console.WriteLine($"连接健康状态: {(isHealthy ? "健康" : "异常")}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"示例执行失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 批量操作示例
    /// </summary>
    public static async Task BatchOperationExample()
    {
        Console.WriteLine("\n=== 批量操作示例 ===");

        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        var config = TcpClientConfig.CreateDefault("localhost", 8080);
        
        var logger = loggerFactory.CreateLogger<TcpClient>();
        var connectionManagerLogger = loggerFactory.CreateLogger<ConnectionManager>();
        var messageHandlerLogger = loggerFactory.CreateLogger<MessageHandler>();
        var heartbeatManagerLogger = loggerFactory.CreateLogger<HeartbeatManager>();

        var connectionManager = new ConnectionManager(connectionManagerLogger);
        var messageHandler = new MessageHandler(messageHandlerLogger);
        var heartbeatManager = new HeartbeatManager(heartbeatManagerLogger, messageHandler);

        using var client = new TcpClient(config, logger, connectionManager, messageHandler, heartbeatManager);

        try
        {
            if (await client.ConnectAsync())
            {
                // 批量发送文本
                var texts = new[] { "消息1", "消息2", "消息3", "消息4", "消息5" };
                var successCount = await client.SendBatchTextAsync(texts);
                Console.WriteLine($"批量发送文本: {successCount}/{texts.Length} 成功");

                // 批量发送消息
                var messages = texts.Select(text => TcpMessage.CreateTextMessage(text)).ToArray();
                successCount = await client.SendBatchAsync(messages);
                Console.WriteLine($"批量发送消息: {successCount}/{messages.Length} 成功");

                // 显示最终统计
                var statistics = client.GetStatistics();
                Console.WriteLine($"\n📊 最终统计:");
                Console.WriteLine($"   总发送消息: {statistics.TotalMessagesSent}");
                Console.WriteLine($"   总发送字节: {statistics.TotalBytesSent}");
                Console.WriteLine($"   平均发送速度: {statistics.AverageSendRate:F2} bytes/s");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"批量操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 诊断报告示例
    /// </summary>
    public static async Task DiagnosticExample()
    {
        Console.WriteLine("\n=== 诊断报告示例 ===");

        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        var config = TcpClientConfig.CreateDefault("localhost", 8080);
        
        var logger = loggerFactory.CreateLogger<TcpClient>();
        var connectionManagerLogger = loggerFactory.CreateLogger<ConnectionManager>();
        var messageHandlerLogger = loggerFactory.CreateLogger<MessageHandler>();
        var heartbeatManagerLogger = loggerFactory.CreateLogger<HeartbeatManager>();

        var connectionManager = new ConnectionManager(connectionManagerLogger);
        var messageHandler = new MessageHandler(messageHandlerLogger);
        var heartbeatManager = new HeartbeatManager(heartbeatManagerLogger, messageHandler);

        using var client = new TcpClient(config, logger, connectionManager, messageHandler, heartbeatManager);

        try
        {
            if (await client.ConnectAsync())
            {
                // 执行一些操作
                await client.SendTextAsync("测试消息");
                await Task.Delay(1000);

                // 生成诊断报告
                var report = await client.CreateDiagnosticReportAsync();
                Console.WriteLine("📋 诊断报告:");
                Console.WriteLine(report);

                // 显示连接摘要
                var summary = client.GetConnectionSummary();
                Console.WriteLine("📝 连接摘要:");
                Console.WriteLine(summary);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"诊断示例失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 运行所有示例
    /// </summary>
    public static async Task RunAllExamples()
    {
        Console.WriteLine("🚀 Liam.TcpClient 示例程序");
        Console.WriteLine("================================");

        try
        {
            await BasicClientExample();
            await Task.Delay(1000);

            // 注意：SSL示例需要真实的SSL服务器
            // await SslClientExample();
            // await Task.Delay(1000);

            await HeartbeatAndReconnectExample();
            await Task.Delay(1000);

            await BatchOperationExample();
            await Task.Delay(1000);

            await DiagnosticExample();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"示例执行失败: {ex.Message}");
        }

        Console.WriteLine("\n✅ 所有示例执行完成");
    }
}
