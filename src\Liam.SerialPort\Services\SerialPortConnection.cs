using System.IO.Ports;
using Microsoft.Extensions.Logging;
using Liam.SerialPort.Constants;
using Liam.SerialPort.Events;
using Liam.SerialPort.Exceptions;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;

namespace Liam.SerialPort.Services;

/// <summary>
/// 串口连接管理服务实现
/// </summary>
public class SerialPortConnection : ISerialPortConnection
{
    private readonly ILogger<SerialPortConnection> _logger;
    private readonly object _lock = new();
    private readonly ConnectionStatistics _statistics = new();
    
    private System.IO.Ports.SerialPort? _serialPort;
    private ConnectionStatus _status = ConnectionStatus.Disconnected;
    private SerialPortSettings? _settings;
    private SerialPortInfo? _portInfo;
    private Timer? _reconnectTimer;
    private Timer? _heartbeatTimer;
    private int _reconnectAttempts;
    private bool _disposed;

    /// <summary>
    /// 获取当前连接状态
    /// </summary>
    public ConnectionStatus Status => _status;

    /// <summary>
    /// 获取当前串口设置
    /// </summary>
    public SerialPortSettings? Settings => _settings?.Clone();

    /// <summary>
    /// 获取当前连接的串口信息
    /// </summary>
    public SerialPortInfo? PortInfo => _portInfo?.Clone();

    /// <summary>
    /// 获取是否已连接
    /// </summary>
    public bool IsConnected => _status == ConnectionStatus.Connected && _serialPort?.IsOpen == true;

    /// <summary>
    /// 获取连接建立时间
    /// </summary>
    public DateTime? ConnectedAt => _statistics.ConnectedAt;

    /// <summary>
    /// 获取最后活动时间
    /// </summary>
    public DateTime? LastActivity => _statistics.LastActivity;

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    public event EventHandler<ConnectionStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 连接错误事件
    /// </summary>
    public event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public SerialPortConnection(ILogger<SerialPortConnection> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 建立串口连接
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="settings">串口设置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否成功</returns>
    public async Task<bool> ConnectAsync(string portName, SerialPortSettings settings, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(portName))
            throw new ArgumentException("串口名称不能为空", nameof(portName));

        if (settings == null)
            throw new ArgumentNullException(nameof(settings));

        var portInfo = new SerialPortInfo { PortName = portName };
        return await ConnectAsync(portInfo, settings, cancellationToken);
    }

    /// <summary>
    /// 建立串口连接
    /// </summary>
    /// <param name="portInfo">串口信息</param>
    /// <param name="settings">串口设置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否成功</returns>
    public async Task<bool> ConnectAsync(SerialPortInfo portInfo, SerialPortSettings settings, CancellationToken cancellationToken = default)
    {
        if (portInfo == null)
            throw new ArgumentNullException(nameof(portInfo));

        if (settings == null)
            throw new ArgumentNullException(nameof(settings));

        // 验证设置
        var validation = settings.Validate();
        if (!validation.IsValid)
        {
            var errorMessage = string.Join("; ", validation.Errors);
            throw new SerialPortConfigurationException(portInfo.PortName, "Settings", settings, $"串口配置无效: {errorMessage}");
        }

        lock (_lock)
        {
            if (_status == ConnectionStatus.Connected || _status == ConnectionStatus.Connecting)
            {
                _logger.LogWarning("串口 {PortName} 已连接或正在连接中", portInfo.PortName);
                return _status == ConnectionStatus.Connected;
            }

            ChangeStatus(ConnectionStatus.Connecting, "开始连接");
        }

        try
        {
            _logger.LogInformation("开始连接串口 {PortName}，配置：{Settings}", portInfo.PortName, settings);

            // 创建串口对象
            _serialPort = new System.IO.Ports.SerialPort(portInfo.PortName)
            {
                BaudRate = settings.BaudRate,
                DataBits = settings.DataBits,
                StopBits = settings.StopBits,
                Parity = settings.Parity,
                Handshake = settings.Handshake,
                ReadTimeout = settings.ReadTimeout,
                WriteTimeout = settings.WriteTimeout,
                ReceivedBytesThreshold = 1,
                ReadBufferSize = settings.ReceiveBufferSize,
                WriteBufferSize = settings.SendBufferSize,
                DtrEnable = settings.DtrEnable,
                RtsEnable = settings.RtsEnable,
                NewLine = settings.NewLine,
                Encoding = settings.Encoding,
                DiscardNull = settings.DiscardNull
            };

            // 使用超时机制连接
            var connectTask = Task.Run(() =>
            {
                _serialPort.Open();
                return true;
            }, cancellationToken);

            var timeoutTask = Task.Delay(settings.ConnectionTimeout, cancellationToken);
            var completedTask = await Task.WhenAny(connectTask, timeoutTask);

            if (completedTask == timeoutTask)
            {
                throw new SerialPortTimeoutException(portInfo.PortName, TimeSpan.FromMilliseconds(settings.ConnectionTimeout), "连接");
            }

            var success = await connectTask;
            if (success && _serialPort.IsOpen)
            {
                _portInfo = portInfo;
                _settings = settings;
                _reconnectAttempts = 0;

                // 记录连接统计
                _statistics.RecordConnection();

                // 启动心跳检测
                if (settings.HeartbeatInterval > 0)
                {
                    StartHeartbeat();
                }

                ChangeStatus(ConnectionStatus.Connected, "连接成功");
                _logger.LogInformation("串口 {PortName} 连接成功", portInfo.PortName);
                return true;
            }
            else
            {
                throw new SerialPortConnectionException(portInfo.PortName, "串口连接失败");
            }
        }
        catch (OperationCanceledException)
        {
            ChangeStatus(ConnectionStatus.Disconnected, "连接被取消");
            _logger.LogWarning("串口 {PortName} 连接被取消", portInfo.PortName);
            return false;
        }
        catch (Exception ex)
        {
            var errorMessage = $"连接串口 {portInfo.PortName} 时发生错误: {ex.Message}";
            _statistics.RecordError(errorMessage);
            
            ChangeStatus(ConnectionStatus.Error, errorMessage);
            OnErrorOccurred(new SerialPortErrorEventArgs(portInfo.PortName, SerialPortErrorType.Connection, errorMessage, ex, true));
            
            _logger.LogError(ex, "连接串口 {PortName} 失败", portInfo.PortName);

            // 清理资源
            CleanupSerialPort();
            
            // 如果启用自动重连，则启动重连
            if (settings.AutoReconnect)
            {
                StartAutoReconnect();
            }

            return false;
        }
    }

    /// <summary>
    /// 断开串口连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task DisconnectAsync(CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            if (_status == ConnectionStatus.Disconnected || _status == ConnectionStatus.Disconnecting)
            {
                return;
            }

            ChangeStatus(ConnectionStatus.Disconnecting, "开始断开连接");
        }

        try
        {
            _logger.LogInformation("开始断开串口 {PortName} 连接", _portInfo?.PortName ?? "Unknown");

            // 停止定时器
            StopTimers();

            // 关闭串口
            await Task.Run(() =>
            {
                CleanupSerialPort();
            }, cancellationToken);

            ChangeStatus(ConnectionStatus.Disconnected, "断开连接成功");
            _logger.LogInformation("串口 {PortName} 连接已断开", _portInfo?.PortName ?? "Unknown");
        }
        catch (Exception ex)
        {
            var errorMessage = $"断开串口连接时发生错误: {ex.Message}";
            _statistics.RecordError(errorMessage);
            
            ChangeStatus(ConnectionStatus.Error, errorMessage);
            OnErrorOccurred(new SerialPortErrorEventArgs(_portInfo?.PortName ?? "Unknown", SerialPortErrorType.Connection, errorMessage, ex));
            
            _logger.LogError(ex, "断开串口 {PortName} 连接失败", _portInfo?.PortName ?? "Unknown");
        }
    }

    /// <summary>
    /// 重新连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重连是否成功</returns>
    public async Task<bool> ReconnectAsync(CancellationToken cancellationToken = default)
    {
        if (_portInfo == null || _settings == null)
        {
            _logger.LogWarning("无法重连：缺少端口信息或设置");
            return false;
        }

        _logger.LogInformation("开始重连串口 {PortName}", _portInfo.PortName);

        // 先断开现有连接
        await DisconnectAsync(cancellationToken);

        // 等待一段时间后重连
        await Task.Delay(_settings.RetryInterval, cancellationToken);

        // 重新连接
        return await ConnectAsync(_portInfo, _settings, cancellationToken);
    }

    /// <summary>
    /// 测试连接是否正常
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否正常</returns>
    public async Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
            return false;

        try
        {
            // 简单的连接测试：检查串口是否仍然打开
            await Task.Run(() =>
            {
                if (_serialPort?.IsOpen != true)
                    throw new SerialPortConnectionException(_portInfo?.PortName ?? "Unknown", "串口连接已断开");
                
                // 可以添加更多的连接测试逻辑，比如发送心跳数据
                return true;
            }, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "串口 {PortName} 连接测试失败", _portInfo?.PortName ?? "Unknown");
            return false;
        }
    }

    /// <summary>
    /// 获取连接统计信息
    /// </summary>
    /// <returns>连接统计信息</returns>
    public ConnectionStatistics GetStatistics()
    {
        return new ConnectionStatistics
        {
            ConnectedAt = _statistics.ConnectedAt,
            LastActivity = _statistics.LastActivity,
            BytesSent = _statistics.BytesSent,
            BytesReceived = _statistics.BytesReceived,
            MessagesSent = _statistics.MessagesSent,
            MessagesReceived = _statistics.MessagesReceived,
            ConnectionAttempts = _statistics.ConnectionAttempts,
            ReconnectionCount = _statistics.ReconnectionCount,
            ErrorCount = _statistics.ErrorCount,
            LastErrorAt = _statistics.LastErrorAt,
            LastError = _statistics.LastError
        };
    }

    /// <summary>
    /// 获取串口对象（内部使用）
    /// </summary>
    /// <returns>串口对象</returns>
    internal System.IO.Ports.SerialPort? GetSerialPort()
    {
        return _serialPort;
    }

    /// <summary>
    /// 改变连接状态
    /// </summary>
    /// <param name="newStatus">新状态</param>
    /// <param name="reason">状态变化原因</param>
    private void ChangeStatus(ConnectionStatus newStatus, string? reason = null)
    {
        var oldStatus = _status;
        _status = newStatus;

        if (oldStatus != newStatus)
        {
            OnStatusChanged(new ConnectionStatusChangedEventArgs(_portInfo?.PortName ?? "Unknown", oldStatus, newStatus, reason));
        }
    }

    /// <summary>
    /// 触发状态变化事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnStatusChanged(ConnectionStatusChangedEventArgs e)
    {
        try
        {
            StatusChanged?.Invoke(this, e);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "触发状态变化事件时发生错误");
        }
    }

    /// <summary>
    /// 触发错误事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnErrorOccurred(SerialPortErrorEventArgs e)
    {
        try
        {
            ErrorOccurred?.Invoke(this, e);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "触发错误事件时发生错误");
        }
    }

    /// <summary>
    /// 启动自动重连
    /// </summary>
    private void StartAutoReconnect()
    {
        if (_settings?.AutoReconnect != true || _disposed)
            return;

        if (_settings.MaxAutoReconnectAttempts > 0 && _reconnectAttempts >= _settings.MaxAutoReconnectAttempts)
        {
            _logger.LogWarning("已达到最大自动重连次数 {MaxAttempts}，停止自动重连", _settings.MaxAutoReconnectAttempts);
            return;
        }

        _reconnectAttempts++;
        _statistics.RecordReconnection();

        _logger.LogInformation("启动自动重连，第 {Attempt} 次尝试，{Interval}ms 后开始", _reconnectAttempts, _settings.AutoReconnectInterval);

        _reconnectTimer?.Dispose();
        _reconnectTimer = new Timer(async _ =>
        {
            try
            {
                ChangeStatus(ConnectionStatus.Reconnecting, $"第 {_reconnectAttempts} 次自动重连");
                var success = await ReconnectAsync();

                if (!success)
                {
                    StartAutoReconnect(); // 继续尝试重连
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "自动重连时发生错误");
                StartAutoReconnect(); // 继续尝试重连
            }
        }, null, _settings?.AutoReconnectInterval ?? 5000, Timeout.Infinite);
    }

    /// <summary>
    /// 启动心跳检测
    /// </summary>
    private void StartHeartbeat()
    {
        if (_settings?.HeartbeatInterval <= 0 || _disposed)
            return;

        _heartbeatTimer?.Dispose();
        _heartbeatTimer = new Timer(async _ =>
        {
            try
            {
                if (!IsConnected)
                    return;

                // 发送心跳数据
                if (_settings?.HeartbeatData?.Length > 0)
                {
                    await Task.Run(() =>
                    {
                        _serialPort?.Write(_settings.HeartbeatData, 0, _settings.HeartbeatData.Length);
                    });
                }

                // 测试连接
                var isConnected = await TestConnectionAsync();
                if (!isConnected)
                {
                    _logger.LogWarning("心跳检测失败，连接可能已断开");
                    ChangeStatus(ConnectionStatus.Error, "心跳检测失败");

                    if (_settings?.AutoReconnect == true)
                    {
                        StartAutoReconnect();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "心跳检测时发生错误");
            }
        }, null, _settings?.HeartbeatInterval ?? 30000, _settings?.HeartbeatInterval ?? 30000);
    }

    /// <summary>
    /// 停止所有定时器
    /// </summary>
    private void StopTimers()
    {
        _reconnectTimer?.Dispose();
        _reconnectTimer = null;
        
        _heartbeatTimer?.Dispose();
        _heartbeatTimer = null;
    }

    /// <summary>
    /// 清理串口资源
    /// </summary>
    private void CleanupSerialPort()
    {
        try
        {
            if (_serialPort != null)
            {
                if (_serialPort.IsOpen)
                {
                    _serialPort.Close();
                }
                _serialPort.Dispose();
                _serialPort = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理串口资源时发生错误");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            DisconnectAsync().Wait(5000);
            StopTimers();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放SerialPortConnection资源时发生错误");
        }
        finally
        {
            _disposed = true;
        }
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        if (_disposed)
            return;

        try
        {
            await DisconnectAsync();
            StopTimers();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "异步释放SerialPortConnection资源时发生错误");
        }
        finally
        {
            _disposed = true;
        }
    }
}
