# Liam.TcpClient

现代化TCP客户端通信库，支持连接管理、异步数据传输、SSL/TLS安全通信、自动重连、心跳检测、连接池管理等功能，基于.NET 8.0构建。

## 功能特性

### 🔗 核心功能
- **连接管理**: 自动连接、断开、重连机制
- **异步通信**: 完全异步的数据发送和接收
- **消息协议**: 与Liam.TcpServer兼容的消息格式
- **状态监控**: 实时连接状态和事件通知
- **错误处理**: 完善的异常处理和错误恢复

### 🔒 安全特性
- **SSL/TLS支持**: 完整的SSL/TLS加密通信
- **证书验证**: 自定义证书验证回调
- **安全配置**: 灵活的安全参数配置

### 💓 高级特性
- **心跳检测**: 自动心跳保活和超时检测
- **自动重连**: 智能重连策略和重试机制
- **连接池**: 高效的连接池管理
- **性能监控**: 详细的性能指标和统计信息
- **质量评估**: 连接质量评分和健康检查

### 🎯 易用性
- **依赖注入**: 完整的DI容器支持
- **配置驱动**: 灵活的配置选项
- **事件驱动**: 丰富的事件通知机制
- **扩展方法**: 便捷的扩展功能

## 安装

### NuGet包管理器
```bash
Install-Package Liam.TcpClient
```

### .NET CLI
```bash
dotnet add package Liam.TcpClient
```

### PackageReference
```xml
<PackageReference Include="Liam.TcpClient" Version="1.0.0" />
```

## 快速开始

### 基本使用

```csharp
using Liam.TcpClient.Models;
using Liam.TcpClient.Services;
using Microsoft.Extensions.Logging;

// 创建配置
var config = new TcpClientConfig
{
    Host = "localhost",
    Port = 8080,
    EnableHeartbeat = true,
    EnableAutoReconnect = true
};

// 创建客户端
using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
var logger = loggerFactory.CreateLogger<TcpClient>();
var connectionManagerLogger = loggerFactory.CreateLogger<ConnectionManager>();
var messageHandlerLogger = loggerFactory.CreateLogger<MessageHandler>();
var heartbeatManagerLogger = loggerFactory.CreateLogger<HeartbeatManager>();

var connectionManager = new ConnectionManager(connectionManagerLogger);
var messageHandler = new MessageHandler(messageHandlerLogger);
var heartbeatManager = new HeartbeatManager(heartbeatManagerLogger, messageHandler);

using var client = new TcpClient(config, logger, connectionManager, messageHandler, heartbeatManager);

// 连接到服务器
if (await client.ConnectAsync())
{
    Console.WriteLine("连接成功！");
    
    // 发送文本消息
    await client.SendTextAsync("Hello, Server!");
    
    // 接收响应
    var response = await client.ReceiveTextAsync(TimeSpan.FromSeconds(5));
    Console.WriteLine($"收到响应: {response}");
}
```

### 依赖注入使用

```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Liam.TcpClient.Extensions;

var builder = Host.CreateApplicationBuilder(args);

// 注册TCP客户端服务
builder.Services.AddTcpClient(config =>
{
    config.Host = "localhost";
    config.Port = 8080;
    config.EnableHeartbeat = true;
    config.EnableAutoReconnect = true;
});

var host = builder.Build();

// 使用TCP客户端
var client = host.Services.GetRequiredService<ITcpClient>();
await client.ConnectAsync();
```

### SSL/TLS安全连接

```csharp
var config = new TcpClientConfig
{
    Host = "secure-server.com",
    Port = 443,
    EnableSsl = true,
    SslConfig = new SslConfig
    {
        ServerName = "secure-server.com",
        CheckCertificateRevocation = true
    }
};

using var client = CreateTcpClient(config);
await client.ConnectAsync();
```

## 配置选项

### 基本配置

```csharp
var config = new TcpClientConfig
{
    Host = "localhost",                    // 服务器地址
    Port = 8080,                          // 服务器端口
    ConnectionTimeoutSeconds = 30,         // 连接超时时间
    ReceiveBufferSize = 8192,             // 接收缓冲区大小
    SendBufferSize = 8192,                // 发送缓冲区大小
    MaxMessageLength = 1024 * 1024,       // 最大消息长度
    ClientId = "client-001",              // 客户端标识
    ClientName = "MyTcpClient"            // 客户端名称
};
```

### 心跳配置

```csharp
config.EnableHeartbeat = true;
config.HeartbeatIntervalSeconds = 60;     // 心跳间隔
config.HeartbeatTimeoutSeconds = 10;      // 心跳超时时间
```

### 重连配置

```csharp
config.EnableAutoReconnect = true;
config.ReconnectIntervalSeconds = 5;      // 重连间隔
config.MaxReconnectAttempts = 10;         // 最大重连次数（-1表示无限重连）
```

### SSL/TLS配置

```csharp
config.EnableSsl = true;
config.SslConfig = new SslConfig
{
    ServerName = "example.com",
    CheckCertificateRevocation = true,
    HandshakeTimeoutSeconds = 30,
    SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls13, // 推荐使用TLS 1.2+
    ValidationMode = CertificateValidationMode.Strict,      // 生产环境推荐严格模式
    RemoteCertificateValidationCallback = (sender, certificate, chain, errors) =>
    {
        // 自定义证书验证逻辑
        return true;
    }
};
```

### SSL/TLS最佳实践

#### 1. 证书验证模式选择

```csharp
// 生产环境 - 严格验证
config.SslConfig.ValidationMode = CertificateValidationMode.Strict;

// 开发环境 - 允许自签名证书
config.SslConfig.ValidationMode = CertificateValidationMode.Development;
config.SslConfig.AllowSelfSignedCertificates = true;
config.SslConfig.AllowNameMismatch = true;

// 指纹验证 - 适用于已知证书
config.SslConfig.ValidationMode = CertificateValidationMode.Thumbprint;
config.SslConfig.TrustedCertificateThumbprints.Add("SHA256指纹");

// 自定义验证 - 复杂场景
config.SslConfig.ValidationMode = CertificateValidationMode.Custom;
config.SslConfig.RemoteCertificateValidationCallback = (context, cert, chain, errors) =>
{
    // 实现自定义验证逻辑
    if (errors == SslPolicyErrors.None)
        return true;

    // 记录验证错误
    logger.LogWarning("SSL证书验证错误: {Errors}", errors);

    // 根据业务需求决定是否接受
    return false;
};
```

#### 2. 安全协议配置

```csharp
// 推荐配置 - 仅使用安全协议
config.SslConfig.SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls13;

// 避免使用过时协议
// config.SslConfig.SslProtocols = SslProtocols.Ssl3;  // 不安全
// config.SslConfig.SslProtocols = SslProtocols.Tls;   // 不安全
// config.SslConfig.SslProtocols = SslProtocols.Tls11; // 不推荐
```

#### 3. 证书撤销检查

```csharp
// 生产环境建议启用
config.SslConfig.CheckCertificateRevocation = true;

// 如果网络环境不支持CRL/OCSP，可以禁用
// config.SslConfig.CheckCertificateRevocation = false;
```

#### 4. 超时配置

```csharp
// 根据网络环境调整握手超时
config.SslConfig.HandshakeTimeoutSeconds = 30; // 默认30秒

// 慢速网络环境可以适当增加
// config.SslConfig.HandshakeTimeoutSeconds = 60;
```

## 事件处理

### 连接事件

```csharp
client.Connected += (sender, e) =>
{
    Console.WriteLine($"连接建立: {e.ConnectionInfo.RemoteEndPoint}");
    Console.WriteLine($"是否重连: {e.IsReconnection}");
};

client.Disconnected += (sender, e) =>
{
    Console.WriteLine($"连接断开: {e.Reason}");
    Console.WriteLine($"是否意外断开: {e.IsUnexpected}");
};

client.ConnectionStateChanged += (sender, e) =>
{
    Console.WriteLine($"状态变更: {e.OldState} -> {e.NewState}");
};
```

### 数据事件

```csharp
client.DataReceived += (sender, e) =>
{
    Console.WriteLine($"接收数据: {e.Length} 字节");
};

client.MessageReceived += (sender, e) =>
{
    Console.WriteLine($"接收消息: {e.Message}");
};

client.DataSent += (sender, e) =>
{
    Console.WriteLine($"发送数据: {e.Length} 字节, 成功: {e.Success}");
};
```

### 心跳事件

```csharp
client.Heartbeat += (sender, e) =>
{
    switch (e.Type)
    {
        case HeartbeatType.Request:
            Console.WriteLine("发送心跳请求");
            break;
        case HeartbeatType.Response:
            Console.WriteLine($"收到心跳响应，延迟: {e.ResponseTime}ms");
            break;
        case HeartbeatType.Timeout:
            Console.WriteLine("心跳超时");
            break;
    }
};
```

### 错误事件

```csharp
client.Error += (sender, e) =>
{
    Console.WriteLine($"发生错误: {e.Message}");
    Console.WriteLine($"错误类型: {e.ErrorType}");
    if (e.Exception != null)
    {
        Console.WriteLine($"异常详情: {e.Exception}");
    }
};
```

## 高级用法

### 发送JSON对象

```csharp
using Liam.TcpClient.Extensions;

var data = new { Name = "张三", Age = 30 };
await client.SendJsonAsync(data);

var response = await client.ReceiveJsonAsync<ResponseModel>();
```

### 文件传输

```csharp
// 发送文件
await client.SendFileAsync("path/to/file.txt");

// 接收文件
await client.ReceiveFileAsync("path/to/received-file.txt", fileSize);
```

### 批量发送

```csharp
var messages = new[]
{
    TcpMessage.CreateTextMessage("消息1"),
    TcpMessage.CreateTextMessage("消息2"),
    TcpMessage.CreateTextMessage("消息3")
};

var successCount = await client.SendBatchAsync(messages);
Console.WriteLine($"成功发送 {successCount} 条消息");
```

### 请求-响应模式

```csharp
var request = TcpMessage.CreateTextMessage("GET_STATUS");
var response = await client.SendRequestAsync(
    request,
    msg => msg.MessageType == TcpClientConstants.MessageTypes.Data,
    TimeSpan.FromSeconds(10)
);
```

### 等待特定消息

```csharp
var message = await client.WaitForMessageAsync(
    msg => msg.GetText().StartsWith("NOTIFICATION"),
    TimeSpan.FromMinutes(1)
);
```

## 连接池

### 配置连接池

```csharp
builder.Services.AddTcpClientPool(config, poolSize: 20);

var pool = serviceProvider.GetRequiredService<ITcpClientPool>();

// 获取客户端
var client = await pool.GetClientAsync();
try
{
    // 使用客户端
    await client.SendTextAsync("Hello");
}
finally
{
    // 归还客户端
    await pool.ReturnClientAsync(client);
}
```

### 池统计信息

```csharp
var stats = pool.GetStatistics();
Console.WriteLine($"总客户端: {stats.TotalClients}");
Console.WriteLine($"可用客户端: {stats.AvailableClients}");
Console.WriteLine($"忙碌客户端: {stats.BusyClients}");
Console.WriteLine($"使用率: {stats.UtilizationRate:F1}%");
```

## 性能监控

### 获取统计信息

```csharp
var statistics = client.GetStatistics();
Console.WriteLine($"连接成功率: {statistics.ConnectionSuccessRate:F1}%");
Console.WriteLine($"平均发送速度: {statistics.AverageSendRate:F2} bytes/s");
Console.WriteLine($"平均接收速度: {statistics.AverageReceiveRate:F2} bytes/s");
Console.WriteLine($"错误率: {statistics.ErrorRate:F2}%");
```

### 连接质量评估

```csharp
var quality = client.GetConnectionQuality();
Console.WriteLine($"连接质量评分: {quality:F1}/100");

var isHealthy = await client.CheckHealthAsync();
Console.WriteLine($"连接健康状态: {(isHealthy ? "健康" : "异常")}");
```

### 性能指标

```csharp
var metrics = client.GetPerformanceMetrics();
foreach (var metric in metrics)
{
    Console.WriteLine($"{metric.Key}: {metric.Value}");
}

// 获取连接质量信息
var qualityInfo = client.GetConnectionQualityInfo();
Console.WriteLine($"连接成功率: {qualityInfo.SuccessRate:F2}");
Console.WriteLine($"平均延迟: {qualityInfo.AverageLatency:F2}ms");
Console.WriteLine($"质量评分: {qualityInfo.QualityScore}/100");
```

### 性能基准测试结果

基于最新的性能优化，Liam.TcpClient在标准测试环境下的性能表现：

| 指标 | 优化前 | 优化后 | 改进幅度 |
|------|--------|--------|----------|
| **内存分配** | 100% | ≤75% | ≥25%减少 |
| **字符串操作** | 100% | ≤70% | ≥30%提升 |
| **批量操作吞吐量** | 100% | ≥120% | ≥20%提升 |
| **GC压力** | 100% | ≤60% | ≥40%减少 |
| **连接建立时间** | ~200ms | ~150ms | 25%提升 |
| **消息发送延迟** | ~5ms | ~3ms | 40%提升 |

**测试环境**: .NET 8.0, Windows 11, 16GB RAM, Intel i7-12700H

### 实时性能监控

```csharp
// 启用性能监控
var performanceMetrics = client.GetPerformanceMetrics();

// 监控关键指标
Console.WriteLine($"连接操作平均时间: {performanceMetrics["AvgTime_Connect"]:F2}ms");
Console.WriteLine($"数据发送平均时间: {performanceMetrics["AvgTime_SendData"]:F2}ms");
Console.WriteLine($"连接尝试次数: {performanceMetrics["Count_ConnectionAttempts"]}");
Console.WriteLine($"成功连接次数: {performanceMetrics["Count_SuccessfulConnections"]}");

// 重置性能指标
client.ResetStatistics();
```

## 最佳实践

### 1. 资源管理
```csharp
// 使用using语句确保资源正确释放
using var client = CreateTcpClient(config);
await client.ConnectAsync();
// 客户端会在using块结束时自动释放
```

### 2. 异常处理
```csharp
try
{
    await client.ConnectAsync();
}
catch (ConnectionTimeoutException ex)
{
    Console.WriteLine($"连接超时: {ex.Timeout}");
}
catch (ConnectionException ex)
{
    Console.WriteLine($"连接失败: {ex.Message}");
}
```

### 3. 取消令牌
```csharp
using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
await client.ConnectAsync(cts.Token);
```

### 4. 配置验证
```csharp
var validation = config.Validate();
if (!validation.IsValid)
{
    foreach (var error in validation.Errors)
    {
        Console.WriteLine($"配置错误: {error}");
    }
}
```

## 与Liam.TcpServer集成

Liam.TcpClient与Liam.TcpServer完全兼容，使用相同的消息协议和通信格式：

```csharp
// 客户端代码
var message = TcpMessage.CreateTextMessage("Hello Server");
await client.SendMessageAsync(message);

// 服务器端会收到相同格式的消息
// 服务器响应也使用相同的消息格式
var response = await client.ReceiveMessageAsync();
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 增加连接超时时间
   - 确认服务器地址和端口正确

2. **SSL握手失败**
   - 检查证书配置
   - 验证服务器名称
   - 确认SSL协议版本

3. **心跳超时**
   - 调整心跳间隔和超时时间
   - 检查网络稳定性
   - 确认服务器支持心跳

4. **自动重连失败**
   - 检查重连配置
   - 确认网络恢复
   - 查看错误日志

### 调试技巧

```csharp
// 启用详细日志
builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.SetMinimumLevel(LogLevel.Debug);
});

// 监听所有事件
client.ConnectionStateChanged += (s, e) => Console.WriteLine($"状态: {e.NewState}");
client.Error += (s, e) => Console.WriteLine($"错误: {e.Message}");

// 生成诊断报告
var report = await client.CreateDiagnosticReportAsync();
File.WriteAllText("diagnostic-report.txt", report);
```

## 最佳实践

### 连接管理
- **使用连接池**：启用连接池可显著提高性能，减少连接建立开销
- **合理设置超时**：根据网络环境调整连接和操作超时时间
- **及时释放连接**：使用 `using` 语句或手动调用 `DisposeAsync()` 释放资源
- **监控连接状态**：定期检查连接健康状态，及时处理断开的连接

### 错误处理
- **分层异常处理**：捕获并处理特定的异常类型（ConnectionException、MessageException等）
- **实现重试机制**：对临时性错误实施指数退避重试策略
- **详细错误日志**：记录异常上下文信息，便于问题诊断
- **优雅降级**：在连接失败时提供备用方案

### 性能优化指南

#### 1. 异步编程最佳实践
```csharp
// ✅ 推荐：使用异步方法
await client.SendTextAsync("Hello", cancellationToken);

// ❌ 避免：阻塞调用
client.SendTextAsync("Hello").Wait();
```

#### 2. 批量操作优化
```csharp
// ✅ 推荐：使用批量发送，控制并发数
var messages = GenerateMessages(1000);
var successCount = await client.SendBatchAsync(messages, maxConcurrency: 10);

// ❌ 避免：逐个发送大量消息
foreach (var message in messages)
{
    await client.SendMessageAsync(message);
}
```

#### 3. 内存管理优化
- **ArrayPool 使用**：库内部已使用 ArrayPool<byte> 优化大数据传输
- **Memory<T>/Span<T>**：高效的内存操作，减少数组复制开销
- **及时释放**：大对象使用完毕后及时释放引用

#### 4. 连接池配置优化
```csharp
services.AddTcpClient(config =>
{
    config.ConnectionPoolConfig = new ConnectionPoolConfig
    {
        Enabled = true,
        PoolSize = Environment.ProcessorCount * 2, // 推荐配置
        IdleTimeoutSeconds = 300,
        MaxLifetimeSeconds = 3600
    };
});
```

#### 5. SSL/TLS 性能优化
```csharp
config.SslConfig = new SslConfig
{
    SslProtocols = SslProtocols.None, // 让系统选择最优协议
    CheckCertificateRevocation = false, // 生产环境可考虑禁用以提升性能
    HandshakeTimeoutSeconds = 30
};
```

### 监控和诊断

#### 性能指标监控
```csharp
// 获取客户端统计信息
var stats = client.GetStatistics();
Console.WriteLine($"发送消息数: {stats.MessagesSent}");
Console.WriteLine($"接收消息数: {stats.MessagesReceived}");
Console.WriteLine($"连接时长: {stats.ConnectionDuration}");

// 获取连接池统计信息
var poolStats = pool.GetStatistics();
Console.WriteLine($"池利用率: {poolStats.UtilizationRate:P}");
Console.WriteLine($"可用连接: {poolStats.AvailableClients}");
```

#### 日志配置
```csharp
// 配置详细日志以便性能分析
services.AddLogging(builder =>
{
    builder.AddConsole();
    builder.SetMinimumLevel(LogLevel.Information);
});
```

### 安全最佳实践

#### SSL/TLS 配置
- **协议版本**：使用 `SslProtocols.None` 让系统自动选择最安全的协议
- **证书验证模式**：根据环境选择合适的验证模式
  ```csharp
  // 生产环境 - 严格模式
  config.SslConfig = new SslConfig
  {
      ValidationMode = CertificateValidationMode.Strict,
      CheckCertificateRevocation = true
  };

  // 开发环境 - 开发模式
  config.SslConfig = new SslConfig
  {
      ValidationMode = CertificateValidationMode.Development,
      AllowSelfSignedCertificates = true,
      AllowNameMismatch = true
  };

  // 指纹验证模式 - 适用于已知证书环境
  config.SslConfig = new SslConfig
  {
      ValidationMode = CertificateValidationMode.Thumbprint,
      TrustedCertificateThumbprints = { "ABC123...证书指纹" }
  };
  ```

#### 输入数据验证
- **使用安全发送方法**：优先使用带验证的发送方法
  ```csharp
  // ✅ 推荐：使用安全的发送方法
  await client.SendTextWithValidationAsync("Hello", cancellationToken);
  await client.SendJsonWithValidationAsync(jsonString, cancellationToken);

  // ❌ 避免：直接发送未验证的数据
  await client.SendTextAsync(untrustedInput, cancellationToken);
  ```

- **消息大小限制**：合理设置最大消息长度
  ```csharp
  config.MaxMessageLength = 1024 * 1024; // 1MB限制
  ```

- **JSON深度限制**：防止深度嵌套攻击，系统自动限制JSON深度为20层

#### 证书管理
- **生产环境证书验证**：
  ```csharp
  config.SslConfig = new SslConfig
  {
      ValidationMode = CertificateValidationMode.Strict,
      CheckCertificateRevocation = true,
      RemoteCertificateValidationCallback = (sender, cert, chain, errors) =>
      {
          // 严格验证，记录详细日志
          if (errors != SslPolicyErrors.None)
          {
              logger.LogError("SSL证书验证失败: {Errors}", errors);
              return false;
          }
          return true;
      }
  };
  ```

- **开发环境证书配置**：
  ```csharp
  #if DEBUG
  config.SslConfig = new SslConfig
  {
      ValidationMode = CertificateValidationMode.Development,
      AllowSelfSignedCertificates = true,
      AllowNameMismatch = true
  };
  #endif
  ```

#### 数据保护
- **敏感数据加密**：传输敏感信息前进行加密
- **输入验证**：验证接收到的数据格式和内容
- **访问控制**：限制网络访问权限
- **日志安全**：避免在日志中记录敏感信息

## 版本历史

| 版本 | 发布日期 | 主要变更 |
|------|----------|----------|
| 1.0.7 | 2025-06-16 | **P3级质量提升**：修复性能测试时间敏感断言（调整性能基准测试阈值，提高测试稳定性）、增强测试覆盖率（新增SSL/TLS集成测试、并发压力测试、异常处理边界测试，总测试数量达到195个）、性能监控集成（PerformanceMetrics性能指标收集、ConnectionQualityMonitor连接质量监控、实时性能指标API）、文档更新（性能基准数据、SSL/TLS最佳实践、性能优化使用指南）；**新增监控功能**：PerformanceTimer计时器、连接质量评分、实时性能指标收集；**测试改进**：所有195个测试通过，包含SSL证书验证、并发压力、异常边界等场景 |
| 1.0.6 | 2025-06-16 | **P0级安全修复**：修复空catch块异常处理、**P1级代码规范**：完善NuGet包配置（添加PackageIcon和PackageReadmeFile）、统一依赖版本、**P2级性能优化**：移除Task.Run反模式，优化异步调用模式；**质量改进**：提高错误诊断能力、完善包发布规范、减少线程池压力 |
| 1.0.5 | 2025-06-15 | **P2级性能优化**：字符串操作优化（StringOptimizer池化StringBuilder，缓存常用字符串，直接JSON序列化到字节数组）、内存使用优化（MemoryOptimizer高效缓冲区管理，避免大对象堆分配，优化Memory<T>/Span<T>使用）、批量操作性能提升（BatchOptimizer智能批量处理，优化并发控制机制）；**新增性能工具**：StringOptimizer、MemoryOptimizer、BatchOptimizer、PooledByteBuffer、PooledCharBuffer；**性能提升**：字符串操作性能提升30%+，内存分配减少25%+，批量操作吞吐量提升20%+；**测试覆盖**：新增8个性能基准测试，验证优化效果 |
| 1.0.4 | 2025-06-15 | **P1级代码标准提升**：完善异常处理机制（新增TcpClientExceptionBase基类，支持详细上下文信息、异常分类、严重程度、重试策略）、统一异常处理策略（8种异常分类、5级严重程度、智能重试建议）、增强异常上下文（操作时间戳、连接状态、配置参数脱敏、线程信息）；**新增异常类型**：ConfigurationException、ValidationException、SecurityException、ProtocolException、ResourceException；**测试覆盖**：新增15个异常处理测试用例，总测试数量达到168个 |
| 1.0.3 | 2025-06-15 | **P0级安全修复**：强化SSL证书验证机制（支持严格/开发/自定义/指纹四种验证模式）、增强输入数据验证（消息格式验证、恶意数据检测、JSON深度限制）、安全的证书验证回调实现；**新增安全功能**：CertificateValidator证书验证器、InputValidator输入验证器、基于环境的证书验证策略、详细的安全日志记录；**测试覆盖**：新增50+安全相关测试用例，覆盖SSL配置和输入验证场景 |
| 1.0.2 | 2025-06-15 | **P2级性能优化**：ArrayPool缓冲区管理、Memory<T>/Span<T>高效内存操作、连接池并发控制优化、批量操作性能提升；**P3级质量提升**：测试覆盖率从37个增至108个、完善XML文档注释、添加性能优化指南和最佳实践；**新增功能**：连接池过期清理、性能压力测试、SSL配置测试、异常处理测试 |
| 1.0.1 | 2025-06-15 | **P0级安全修复**：移除Task.Run反模式，优化SSL/TLS协议配置安全性，添加ConfigureAwait(false)；**P1级代码质量**：修复编译器警告，完善IAsyncDisposable实现，优化可空引用类型标注；**性能改进**：真正的异步I/O操作，减少线程切换开销 |
| 1.0.0 | 2025-06-15 | 初始版本，支持基本TCP客户端功能、SSL/TLS、心跳检测、自动重连、连接池等 |

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](../../LICENSE) 文件。

## 贡献

欢迎提交问题和功能请求！请查看我们的[贡献指南](../../README.md#贡献指南)了解更多信息。

## 相关项目

- [Liam.TcpServer](../Liam.TcpServer/README.md) - TCP服务器通信库
- [Liam.Logging](../Liam.Logging/README.md) - 日志记录库
- [Liam.Cryptography](../Liam.Cryptography/README.md) - 加密功能库
