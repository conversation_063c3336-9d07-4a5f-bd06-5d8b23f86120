using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Liam.TcpClient.Services;

namespace Liam.TcpClient.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加TCP客户端服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">客户端配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpClient(this IServiceCollection services, TcpClientConfig configuration)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configuration);

        // 验证配置
        var validation = configuration.Validate();
        if (!validation.IsValid)
        {
            throw new ArgumentException($"配置无效：{string.Join(", ", validation.Errors)}", nameof(configuration));
        }

        // 注册配置
        services.TryAddSingleton(configuration);

        // 注册核心服务
        services.TryAddTransient<IConnectionManager, ConnectionManager>();
        services.TryAddTransient<IMessageHandler, MessageHandler>();
        services.TryAddTransient<IHeartbeatManager, HeartbeatManager>();

        // 注册TCP客户端
        services.TryAddTransient<ITcpClient, Services.TcpClient>();

        return services;
    }

    /// <summary>
    /// 添加TCP客户端服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpClient(this IServiceCollection services, Action<TcpClientConfig> configureOptions)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configureOptions);

        var configuration = new TcpClientConfig();
        configureOptions(configuration);

        return services.AddTcpClient(configuration);
    }

    /// <summary>
    /// 添加TCP客户端服务（使用默认配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="host">服务器主机</param>
    /// <param name="port">服务器端口</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpClient(this IServiceCollection services, string host, int port)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(host);

        var configuration = TcpClientConfig.CreateDefault(host, port);
        return services.AddTcpClient(configuration);
    }

    /// <summary>
    /// 添加SSL TCP客户端服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="host">服务器主机</param>
    /// <param name="port">服务器端口</param>
    /// <param name="serverName">SSL服务器名称</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSslTcpClient(this IServiceCollection services, string host, int port, string? serverName = null)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(host);

        var configuration = TcpClientConfig.CreateSslConfig(host, port, serverName);
        return services.AddTcpClient(configuration);
    }

    /// <summary>
    /// 添加命名TCP客户端服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="name">客户端名称</param>
    /// <param name="configuration">客户端配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddNamedTcpClient(this IServiceCollection services, string name, TcpClientConfig configuration)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(name);
        ArgumentNullException.ThrowIfNull(configuration);

        // 验证配置
        var validation = configuration.Validate();
        if (!validation.IsValid)
        {
            throw new ArgumentException($"配置无效：{string.Join(", ", validation.Errors)}", nameof(configuration));
        }

        // 注册命名配置
        services.Configure<TcpClientOptions>(name, options =>
        {
            options.Configuration = configuration;
        });

        // 注册命名TCP客户端工厂
        services.TryAddSingleton<ITcpClientFactory, TcpClientFactory>();

        return services;
    }

    /// <summary>
    /// 添加命名TCP客户端服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="name">客户端名称</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddNamedTcpClient(this IServiceCollection services, string name, Action<TcpClientConfig> configureOptions)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(name);
        ArgumentNullException.ThrowIfNull(configureOptions);

        var configuration = new TcpClientConfig();
        configureOptions(configuration);

        return services.AddNamedTcpClient(name, configuration);
    }

    /// <summary>
    /// 添加TCP客户端池
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="poolSize">连接池大小</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpClientPool(this IServiceCollection services, TcpClientConfig configuration, int poolSize = 10)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configuration);

        if (poolSize <= 0)
        {
            throw new ArgumentException("连接池大小必须大于0", nameof(poolSize));
        }

        // 启用连接池配置
        configuration.ConnectionPoolConfig = new ConnectionPoolConfig
        {
            Enabled = true,
            PoolSize = poolSize
        };

        // 注册连接池服务
        services.TryAddSingleton<ITcpClientPool, TcpClientPool>();

        return services.AddTcpClient(configuration);
    }
}

/// <summary>
/// TCP客户端选项
/// </summary>
public class TcpClientOptions
{
    /// <summary>
    /// 客户端配置
    /// </summary>
    public TcpClientConfig? Configuration { get; set; }
}

/// <summary>
/// TCP客户端工厂接口
/// </summary>
public interface ITcpClientFactory
{
    /// <summary>
    /// 创建命名TCP客户端
    /// </summary>
    /// <param name="name">客户端名称</param>
    /// <returns>TCP客户端</returns>
    ITcpClient CreateClient(string name);

    /// <summary>
    /// 创建TCP客户端
    /// </summary>
    /// <param name="configuration">客户端配置</param>
    /// <returns>TCP客户端</returns>
    ITcpClient CreateClient(TcpClientConfig configuration);
}

/// <summary>
/// TCP客户端工厂实现
/// </summary>
public class TcpClientFactory : ITcpClientFactory
{
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// 初始化TCP客户端工厂
    /// </summary>
    /// <param name="serviceProvider">服务提供程序</param>
    public TcpClientFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
    }

    /// <summary>
    /// 创建命名TCP客户端
    /// </summary>
    /// <param name="name">客户端名称</param>
    /// <returns>TCP客户端</returns>
    public ITcpClient CreateClient(string name)
    {
        ArgumentNullException.ThrowIfNull(name);

        // 获取命名配置
        var options = _serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptionsSnapshot<TcpClientOptions>>();
        var configuration = options.Get(name).Configuration;

        if (configuration == null)
        {
            throw new InvalidOperationException($"未找到名为 '{name}' 的TCP客户端配置");
        }

        return CreateClient(configuration);
    }

    /// <summary>
    /// 创建TCP客户端
    /// </summary>
    /// <param name="configuration">客户端配置</param>
    /// <returns>TCP客户端</returns>
    public ITcpClient CreateClient(TcpClientConfig configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);

        var logger = _serviceProvider.GetRequiredService<ILogger<Services.TcpClient>>();
        var connectionManagerLogger = _serviceProvider.GetRequiredService<ILogger<ConnectionManager>>();
        var messageHandlerLogger = _serviceProvider.GetRequiredService<ILogger<MessageHandler>>();
        var heartbeatManagerLogger = _serviceProvider.GetRequiredService<ILogger<HeartbeatManager>>();

        var connectionManager = new ConnectionManager(connectionManagerLogger);
        var messageHandler = new MessageHandler(messageHandlerLogger);
        var heartbeatManager = new HeartbeatManager(heartbeatManagerLogger, messageHandler);

        return new Services.TcpClient(configuration, logger, connectionManager, messageHandler, heartbeatManager);
    }
}

/// <summary>
/// TCP客户端池接口
/// </summary>
public interface ITcpClientPool : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 获取客户端
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>TCP客户端</returns>
    Task<ITcpClient> GetClientAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 归还客户端
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <returns>归还任务</returns>
    Task ReturnClientAsync(ITcpClient client);

    /// <summary>
    /// 获取池统计信息
    /// </summary>
    /// <returns>池统计信息</returns>
    PoolStatistics GetStatistics();
}

/// <summary>
/// TCP客户端池实现
/// </summary>
public class TcpClientPool : ITcpClientPool
{
    private readonly ITcpClientFactory _clientFactory;
    private readonly TcpClientConfig _configuration;
    private readonly ILogger<TcpClientPool> _logger;
    private readonly Queue<PooledClient> _availableClients = new();
    private readonly HashSet<ITcpClient> _allClients = new();
    private readonly SemaphoreSlim _semaphore;
    private readonly SemaphoreSlim _creationSemaphore; // 限制并发创建
    private readonly Timer _cleanupTimer;
    private readonly object _lockObject = new();
    private readonly int _maxPoolSize;
    private readonly int _idleTimeoutSeconds;
    private readonly int _maxLifetimeSeconds;
    private bool _disposed;

    /// <summary>
    /// 初始化TCP客户端池
    /// </summary>
    /// <param name="clientFactory">客户端工厂</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="logger">日志记录器</param>
    public TcpClientPool(ITcpClientFactory clientFactory, TcpClientConfig configuration, ILogger<TcpClientPool> logger)
    {
        _clientFactory = clientFactory ?? throw new ArgumentNullException(nameof(clientFactory));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _maxPoolSize = configuration.ConnectionPoolConfig?.PoolSize ?? 10;
        _idleTimeoutSeconds = configuration.ConnectionPoolConfig?.IdleTimeoutSeconds ?? 300;
        _maxLifetimeSeconds = configuration.ConnectionPoolConfig?.MaxLifetimeSeconds ?? 3600;

        _semaphore = new SemaphoreSlim(_maxPoolSize, _maxPoolSize);
        _creationSemaphore = new SemaphoreSlim(Environment.ProcessorCount, Environment.ProcessorCount); // 限制并发创建数量

        // 启动清理定时器，每分钟清理一次过期连接
        _cleanupTimer = new Timer(CleanupExpiredClients, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

        _logger.LogInformation("TCP客户端池已初始化，池大小：{PoolSize}，空闲超时：{IdleTimeout}秒，最大生存时间：{MaxLifetime}秒",
            _maxPoolSize, _idleTimeoutSeconds, _maxLifetimeSeconds);
    }

    /// <summary>
    /// 池化客户端包装器
    /// </summary>
    private class PooledClient
    {
        public ITcpClient Client { get; }
        public DateTime CreatedAt { get; }
        public DateTime LastUsedAt { get; set; }

        public PooledClient(ITcpClient client)
        {
            Client = client;
            CreatedAt = DateTime.UtcNow;
            LastUsedAt = DateTime.UtcNow;
        }

        public bool IsExpired(int idleTimeoutSeconds, int maxLifetimeSeconds)
        {
            var now = DateTime.UtcNow;
            var idleTime = now - LastUsedAt;
            var lifetime = now - CreatedAt;

            return idleTime.TotalSeconds > idleTimeoutSeconds ||
                   lifetime.TotalSeconds > maxLifetimeSeconds ||
                   !Client.IsConnected;
        }
    }

    /// <summary>
    /// 获取客户端
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>TCP客户端</returns>
    public async Task<ITcpClient> GetClientAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(TcpClientPool));
        }

        // 等待池中有可用位置
        await _semaphore.WaitAsync(cancellationToken).ConfigureAwait(false);

        try
        {
            // 尝试从池中获取可用客户端
            PooledClient? pooledClient = null;
            lock (_lockObject)
            {
                while (_availableClients.Count > 0)
                {
                    var candidate = _availableClients.Dequeue();
                    if (!candidate.IsExpired(_idleTimeoutSeconds, _maxLifetimeSeconds))
                    {
                        candidate.LastUsedAt = DateTime.UtcNow;
                        pooledClient = candidate;
                        break;
                    }
                    else
                    {
                        // 客户端已过期，清理
                        _allClients.Remove(candidate.Client);
                        candidate.Client.Dispose();
                        _logger.LogDebug("清理过期客户端：{ClientId}", GetClientId(candidate.Client));
                    }
                }
            }

            if (pooledClient != null)
            {
                return pooledClient.Client;
            }

            // 需要创建新客户端，使用创建信号量限制并发创建
            await _creationSemaphore.WaitAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                var newClient = _clientFactory.CreateClient(_configuration);
                var newPooledClient = new PooledClient(newClient);

                lock (_lockObject)
                {
                    _allClients.Add(newClient);
                }

                _logger.LogDebug("创建新客户端：{ClientId}", GetClientId(newClient));
                return newClient;
            }
            finally
            {
                _creationSemaphore.Release();
            }
        }
        catch
        {
            _semaphore.Release(); // 发生异常时释放信号量
            throw;
        }
    }

    /// <summary>
    /// 归还客户端
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <returns>归还任务</returns>
    public Task ReturnClientAsync(ITcpClient client)
    {
        ArgumentNullException.ThrowIfNull(client);

        if (_disposed)
        {
            client.Dispose();
            return Task.CompletedTask;
        }

        bool shouldReleaseSemaphore = true;

        lock (_lockObject)
        {
            if (_allClients.Contains(client))
            {
                if (client.IsConnected)
                {
                    // 客户端仍然有效，归还到池中
                    var pooledClient = new PooledClient(client);
                    _availableClients.Enqueue(pooledClient);
                    _logger.LogDebug("客户端已归还到池：{ClientId}", GetClientId(client));
                }
                else
                {
                    // 客户端已断开，清理
                    _allClients.Remove(client);
                    client.Dispose();
                    _logger.LogDebug("清理无效客户端：{ClientId}", GetClientId(client));
                }
            }
            else
            {
                // 客户端不属于此池，不释放信号量
                client.Dispose();
                shouldReleaseSemaphore = false;
                _logger.LogWarning("尝试归还不属于此池的客户端：{ClientId}", GetClientId(client));
            }
        }

        if (shouldReleaseSemaphore)
        {
            _semaphore.Release();
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 清理过期客户端（定时器回调）
    /// </summary>
    /// <param name="state">状态对象</param>
    private void CleanupExpiredClients(object? state)
    {
        if (_disposed)
        {
            return;
        }

        var expiredClients = new List<PooledClient>();

        lock (_lockObject)
        {
            var remainingClients = new Queue<PooledClient>();

            while (_availableClients.Count > 0)
            {
                var pooledClient = _availableClients.Dequeue();
                if (pooledClient.IsExpired(_idleTimeoutSeconds, _maxLifetimeSeconds))
                {
                    expiredClients.Add(pooledClient);
                    _allClients.Remove(pooledClient.Client);
                }
                else
                {
                    remainingClients.Enqueue(pooledClient);
                }
            }

            // 重新入队未过期的客户端
            while (remainingClients.Count > 0)
            {
                _availableClients.Enqueue(remainingClients.Dequeue());
            }
        }

        // 在锁外释放过期客户端
        foreach (var expiredClient in expiredClients)
        {
            try
            {
                expiredClient.Client.Dispose();
                _semaphore.Release(); // 释放信号量
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "清理过期客户端时发生异常：{ClientId}", GetClientId(expiredClient.Client));
            }
        }

        if (expiredClients.Count > 0)
        {
            _logger.LogInformation("清理了 {Count} 个过期客户端", expiredClients.Count);
        }
    }

    /// <summary>
    /// 获取客户端标识符
    /// </summary>
    /// <param name="client">客户端</param>
    /// <returns>客户端标识符</returns>
    private string GetClientId(ITcpClient client)
    {
        return client.Configuration.ClientId ?? client.GetHashCode().ToString("X8");
    }

    /// <summary>
    /// 获取池统计信息
    /// </summary>
    /// <returns>池统计信息</returns>
    public PoolStatistics GetStatistics()
    {
        lock (_lockObject)
        {
            return new PoolStatistics
            {
                TotalClients = _allClients.Count,
                AvailableClients = _availableClients.Count,
                BusyClients = _allClients.Count - _availableClients.Count,
                MaxPoolSize = _configuration.ConnectionPoolConfig?.PoolSize ?? 10
            };
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        // 停止清理定时器
        _cleanupTimer?.Dispose();

        lock (_lockObject)
        {
            foreach (var client in _allClients)
            {
                client.Dispose();
            }
            _allClients.Clear();
            _availableClients.Clear();
        }

        _semaphore.Dispose();
        _creationSemaphore.Dispose();
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        // 停止清理定时器
        _cleanupTimer?.Dispose();

        List<ITcpClient> clients;
        lock (_lockObject)
        {
            clients = new List<ITcpClient>(_allClients);
            _allClients.Clear();
            _availableClients.Clear();
        }

        foreach (var client in clients)
        {
            await client.DisposeAsync().ConfigureAwait(false);
        }

        _semaphore.Dispose();
        _creationSemaphore.Dispose();
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// 池统计信息
/// </summary>
public class PoolStatistics
{
    /// <summary>
    /// 总客户端数
    /// </summary>
    public int TotalClients { get; set; }

    /// <summary>
    /// 可用客户端数
    /// </summary>
    public int AvailableClients { get; set; }

    /// <summary>
    /// 忙碌客户端数
    /// </summary>
    public int BusyClients { get; set; }

    /// <summary>
    /// 最大池大小
    /// </summary>
    public int MaxPoolSize { get; set; }

    /// <summary>
    /// 池使用率
    /// </summary>
    public double UtilizationRate => MaxPoolSize > 0 ? (double)BusyClients / MaxPoolSize * 100 : 0;
}
