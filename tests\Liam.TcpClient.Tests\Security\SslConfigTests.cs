using FluentAssertions;
using Liam.TcpClient.Models;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;
using Xunit;

namespace Liam.TcpClient.Tests.Security;

/// <summary>
/// SSL配置测试
/// </summary>
public class SslConfigTests
{
    [Fact]
    public void SslConfig_ShouldHaveDefaultValues()
    {
        // Act
        var config = new SslConfig();

        // Assert
        config.ServerName.Should().BeNull();
        config.ClientCertificate.Should().BeNull();
        config.CheckCertificateRevocation.Should().BeTrue();
        config.RemoteCertificateValidationCallback.Should().BeNull();
        config.LocalCertificateSelectionCallback.Should().BeNull();
        config.HandshakeTimeoutSeconds.Should().Be(30);
        config.SslProtocols.Should().Be(SslProtocols.None);
    }

    [Fact]
    public void SslConfig_ShouldAllowSettingServerName()
    {
        // Arrange
        var config = new SslConfig();
        var serverName = "example.com";

        // Act
        config.ServerName = serverName;

        // Assert
        config.ServerName.Should().Be(serverName);
    }

    [Fact]
    public void SslConfig_ShouldAllowSettingClientCertificate()
    {
        // Arrange
        var config = new SslConfig();
        var certificate = new X509Certificate2(Array.Empty<byte>());

        // Act
        config.ClientCertificate = certificate;

        // Assert
        config.ClientCertificate.Should().Be(certificate);
    }

    [Fact]
    public void SslConfig_ShouldAllowDisablingCertificateRevocationCheck()
    {
        // Arrange
        var config = new SslConfig();

        // Act
        config.CheckCertificateRevocation = false;

        // Assert
        config.CheckCertificateRevocation.Should().BeFalse();
    }

    [Fact]
    public void SslConfig_ShouldAllowSettingHandshakeTimeout()
    {
        // Arrange
        var config = new SslConfig();
        var timeout = 60;

        // Act
        config.HandshakeTimeoutSeconds = timeout;

        // Assert
        config.HandshakeTimeoutSeconds.Should().Be(timeout);
    }

    [Fact]
    public void SslConfig_ShouldAllowSettingSslProtocols()
    {
        // Arrange
        var config = new SslConfig();
        var protocols = SslProtocols.Tls12 | SslProtocols.Tls13;

        // Act
        config.SslProtocols = protocols;

        // Assert
        config.SslProtocols.Should().Be(protocols);
    }

    [Fact]
    public void SslConfig_Validate_ShouldReturnValid_WhenConfigurationIsCorrect()
    {
        // Arrange
        var config = new SslConfig
        {
            ServerName = "example.com",
            HandshakeTimeoutSeconds = 30,
            CheckCertificateRevocation = true
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void SslConfig_Validate_ShouldReturnInvalid_WhenHandshakeTimeoutIsZero()
    {
        // Arrange
        var config = new SslConfig
        {
            HandshakeTimeoutSeconds = 0
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("HandshakeTimeoutSeconds必须大于0");
    }

    [Fact]
    public void SslConfig_Validate_ShouldReturnInvalid_WhenHandshakeTimeoutIsNegative()
    {
        // Arrange
        var config = new SslConfig
        {
            HandshakeTimeoutSeconds = -1
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("HandshakeTimeoutSeconds必须大于0");
    }

    [Fact]
    public void SslConfig_ShouldSupportTls12Only()
    {
        // Arrange
        var config = new SslConfig();

        // Act
        config.SslProtocols = SslProtocols.Tls12;

        // Assert
        config.SslProtocols.Should().Be(SslProtocols.Tls12);
    }

    [Fact]
    public void SslConfig_ShouldSupportTls13Only()
    {
        // Arrange
        var config = new SslConfig();

        // Act
        config.SslProtocols = SslProtocols.Tls13;

        // Assert
        config.SslProtocols.Should().Be(SslProtocols.Tls13);
    }

    [Fact]
    public void SslConfig_ShouldSupportCombinedProtocols()
    {
        // Arrange
        var config = new SslConfig();
        var combinedProtocols = SslProtocols.Tls12 | SslProtocols.Tls13;

        // Act
        config.SslProtocols = combinedProtocols;

        // Assert
        config.SslProtocols.Should().HaveFlag(SslProtocols.Tls12);
        config.SslProtocols.Should().HaveFlag(SslProtocols.Tls13);
    }

    [Fact]
    public void SslConfig_DefaultProtocol_ShouldBeNone()
    {
        // Arrange & Act
        var config = new SslConfig();

        // Assert
        config.SslProtocols.Should().Be(SslProtocols.None);
    }

    [Theory]
    [InlineData(1)]
    [InlineData(30)]
    [InlineData(60)]
    [InlineData(300)]
    public void SslConfig_Validate_ShouldReturnValid_ForValidTimeouts(int timeoutSeconds)
    {
        // Arrange
        var config = new SslConfig
        {
            HandshakeTimeoutSeconds = timeoutSeconds
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(-100)]
    public void SslConfig_Validate_ShouldReturnInvalid_ForInvalidTimeouts(int timeoutSeconds)
    {
        // Arrange
        var config = new SslConfig
        {
            HandshakeTimeoutSeconds = timeoutSeconds
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("HandshakeTimeoutSeconds必须大于0");
    }

    [Fact]
    public void SslConfig_ShouldAllowNullServerName()
    {
        // Arrange
        var config = new SslConfig();

        // Act
        config.ServerName = null;

        // Assert
        config.ServerName.Should().BeNull();
    }

    [Fact]
    public void SslConfig_ShouldAllowEmptyServerName()
    {
        // Arrange
        var config = new SslConfig();

        // Act
        config.ServerName = string.Empty;

        // Assert
        config.ServerName.Should().BeEmpty();
    }

    [Fact]
    public void SslConfig_ShouldAllowNullClientCertificate()
    {
        // Arrange
        var config = new SslConfig();

        // Act
        config.ClientCertificate = null;

        // Assert
        config.ClientCertificate.Should().BeNull();
    }
}
