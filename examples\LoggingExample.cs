using Liam.Logging.Constants;
using Liam.Logging.Extensions;
using Liam.Logging.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Liam.Examples;

/// <summary>
/// Liam.Logging使用示例
/// </summary>
public class LoggingExample
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("=== Liam.Logging 使用示例 ===\n");

        // 创建主机构建器
        var builder = Host.CreateApplicationBuilder(args);

        // 添加配置文件
        builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

        // 配置Liam日志记录
        builder.Services.AddLiamLogging(builder.Configuration)
            .AddConsoleLogging()
            .AddFileLogging();

        // 构建主机
        var host = builder.Build();

        // 获取日志记录器
        var logger = host.Services.GetRequiredService<ILiamLogger<LoggingExample>>();
        var factory = host.Services.GetRequiredService<ILiamLoggerFactory>();

        Console.WriteLine("1. 基本日志记录示例");
        await BasicLoggingExample(logger);

        Console.WriteLine("\n2. 结构化日志记录示例");
        await StructuredLoggingExample(logger);

        Console.WriteLine("\n3. 异步日志记录示例");
        await AsyncLoggingExample(logger);

        Console.WriteLine("\n4. 日志作用域示例");
        await ScopeLoggingExample(logger);

        Console.WriteLine("\n5. 异常日志记录示例");
        await ExceptionLoggingExample(logger);

        Console.WriteLine("\n6. 性能监控示例");
        await PerformanceMonitoringExample(logger, factory);

        Console.WriteLine("\n7. 方法跟踪示例");
        await MethodTrackingExample(logger);

        // 刷新所有日志提供程序
        await factory.FlushAsync();

        Console.WriteLine("\n=== 示例完成 ===");
        Console.WriteLine("请查看 logs/example.log 文件以查看文件日志输出");
    }

    /// <summary>
    /// 基本日志记录示例
    /// </summary>
    private static async Task BasicLoggingExample(ILiamLogger logger)
    {
        logger.LogTrace("这是一条跟踪消息");
        logger.LogDebug("这是一条调试消息");
        logger.LogInformation("这是一条信息消息");
        logger.LogWarning("这是一条警告消息");
        logger.LogError("这是一条错误消息");
        logger.LogCritical("这是一条严重错误消息");

        await Task.Delay(100); // 模拟一些工作
    }

    /// <summary>
    /// 结构化日志记录示例
    /// </summary>
    private static async Task StructuredLoggingExample(ILiamLogger logger)
    {
        var userId = 12345;
        var userName = "张三";
        var action = "登录";
        var timestamp = DateTime.Now;

        // 使用结构化日志记录
        logger.LogInformationStructured("用户 {UserId} ({UserName}) 执行了操作 {Action} 在 {Timestamp}",
            userId, userName, action, timestamp);

        var orderId = "ORD-2023-001";
        var amount = 299.99m;
        var currency = "CNY";

        logger.LogInformationStructured("订单 {OrderId} 创建成功，金额: {Amount} {Currency}",
            orderId, amount, currency);

        await Task.Delay(100);
    }

    /// <summary>
    /// 异步日志记录示例
    /// </summary>
    private static async Task AsyncLoggingExample(ILiamLogger logger)
    {
        // 异步日志记录
        await logger.LogInformationAsync("开始异步处理");

        // 模拟异步工作
        await Task.Delay(200);

        await logger.LogInformationAsync("异步处理完成");

        // 异步结构化日志
        await logger.LogStructuredAsync(LogLevel.Information,
            "异步处理了 {Count} 个项目，耗时 {Duration}ms",
            new object[] { 100, 200 });
    }

    /// <summary>
    /// 日志作用域示例
    /// </summary>
    private static async Task ScopeLoggingExample(ILiamLogger logger)
    {
        var requestId = Guid.NewGuid().ToString("N")[..8];
        var userId = 67890;

        // 使用作用域
        using (logger.BeginScope("RequestId: {RequestId}", requestId))
        {
            logger.LogInformation("开始处理请求");

            using (logger.BeginScope("UserId: {UserId}", userId))
            {
                logger.LogInformation("用户验证成功");
                logger.LogInformation("执行业务逻辑");

                // 模拟一些工作
                await Task.Delay(150);

                logger.LogInformation("业务逻辑执行完成");
            }

            logger.LogInformation("请求处理完成");
        }

        // 使用扩展方法的作用域
        await logger.WithScopeAsync($"Operation: DataProcessing", async () =>
        {
            logger.LogInformation("开始数据处理");
            await Task.Delay(100);
            logger.LogInformation("数据处理完成");
        });
    }

    /// <summary>
    /// 异常日志记录示例
    /// </summary>
    private static async Task ExceptionLoggingExample(ILiamLogger logger)
    {
        try
        {
            // 模拟一个会抛出异常的操作
            throw new InvalidOperationException("这是一个示例异常", 
                new ArgumentException("内部异常示例"));
        }
        catch (Exception ex)
        {
            logger.LogError("处理过程中发生异常", ex);
        }

        try
        {
            // 模拟另一个异常
            await Task.Run(() => throw new TimeoutException("操作超时"));
        }
        catch (Exception ex)
        {
            await logger.LogErrorAsync("异步操作失败", ex);
        }
    }

    /// <summary>
    /// 性能监控示例
    /// </summary>
    private static async Task PerformanceMonitoringExample(ILiamLogger logger, ILiamLoggerFactory factory)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // 模拟一些耗时操作
        await Task.Delay(300);
        
        stopwatch.Stop();

        // 记录性能日志
        logger.LogPerformance("数据库查询", stopwatch.Elapsed);

        // 获取性能统计信息
        var stats = factory.GetPerformanceStats();
        logger.LogInformation("性能统计信息: {Stats}", System.Text.Json.JsonSerializer.Serialize(stats));
    }

    /// <summary>
    /// 方法跟踪示例
    /// </summary>
    private static async Task MethodTrackingExample(ILiamLogger logger)
    {
        await ProcessOrderAsync(logger, "ORD-2023-002");
    }

    /// <summary>
    /// 模拟订单处理方法
    /// </summary>
    private static async Task ProcessOrderAsync(ILiamLogger logger, string orderId)
    {
        logger.LogMethodEntry(new { orderId });

        try
        {
            logger.LogInformation("开始验证订单 {OrderId}", orderId);
            await Task.Delay(100); // 模拟验证

            logger.LogInformation("开始处理支付 {OrderId}", orderId);
            await Task.Delay(200); // 模拟支付处理

            logger.LogInformation("开始更新库存 {OrderId}", orderId);
            await Task.Delay(150); // 模拟库存更新

            var result = new { OrderId = orderId, Status = "Completed", ProcessedAt = DateTime.Now };
            logger.LogMethodExit(result);
        }
        catch (Exception ex)
        {
            logger.LogError("处理订单 {OrderId} 失败", ex, orderId);
            throw;
        }
    }
}

/// <summary>
/// 业务服务示例类
/// </summary>
public class OrderService
{
    private readonly ILiamLogger<OrderService> _logger;

    public OrderService(ILiamLogger<OrderService> logger)
    {
        _logger = logger;
    }

    public async Task<string> CreateOrderAsync(decimal amount, string currency)
    {
        var orderId = Guid.NewGuid().ToString("N")[..8];

        using (_logger.BeginScope("OrderCreation"))
        {
            _logger.LogInformation("开始创建订单");

            // 模拟订单创建逻辑
            await Task.Delay(100);

            _logger.LogInformationStructured("订单 {OrderId} 创建成功，金额: {Amount} {Currency}",
                orderId, amount, currency);

            return orderId;
        }
    }

    public async Task<bool> ProcessPaymentAsync(string orderId, decimal amount)
    {
        _logger.LogMethodEntry(new { orderId, amount });

        try
        {
            // 模拟支付处理
            await Task.Delay(200);

            var success = amount > 0; // 简单的验证逻辑
            
            if (success)
            {
                _logger.LogInformation("订单 {OrderId} 支付成功", orderId);
            }
            else
            {
                _logger.LogWarning("订单 {OrderId} 支付失败：金额无效", orderId);
            }

            _logger.LogMethodExit(new { success });
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError("处理订单 {OrderId} 支付时发生异常", ex, orderId);
            throw;
        }
    }
}
