using System.Security.Cryptography;
using System.Text;
using Liam.Cryptography.Constants;
using Liam.Cryptography.Exceptions;
using Liam.Cryptography.Interfaces;

namespace Liam.Cryptography.Services;

/// <summary>
/// RSA数字签名服务实现
/// </summary>
public class RsaDigitalSignature : IDigitalSignature
{
    /// <summary>
    /// 生成数字签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>数字签名</returns>
    public byte[] Sign(string data, string privateKey)
    {
        if (data == null)
            throw new ArgumentNullException(nameof(data), "要签名的数据不能为null");
        // 允许空字符串作为有效输入

        var dataBytes = Encoding.UTF8.GetBytes(data);
        return Sign(dataBytes, privateKey);
    }

    /// <summary>
    /// 生成数字签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>数字签名</returns>
    public byte[] Sign(byte[] data, string privateKey)
    {
        try
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data), "要签名的数据不能为null");
            // 允许空字节数组作为有效输入

            if (string.IsNullOrEmpty(privateKey))
                throw new ArgumentException("私钥不能为空", nameof(privateKey));

            using var rsa = RSA.Create();
            // 支持PEM格式和Base64格式
            if (privateKey.StartsWith("-----BEGIN"))
            {
                rsa.ImportFromPem(privateKey);
            }
            else
            {
                rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);
            }

            return rsa.SignData(data, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new DigitalSignatureException(CryptoConstants.ErrorMessages.SIGNING_FAILED, ex);
        }
    }

    /// <summary>
    /// 生成数字签名（异步）
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>数字签名</returns>
    public async Task<byte[]> SignAsync(string data, string privateKey, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => Sign(data, privateKey), cancellationToken);
    }

    /// <summary>
    /// 生成数字签名（异步）
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>数字签名</returns>
    public async Task<byte[]> SignAsync(byte[] data, string privateKey, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => Sign(data, privateKey), cancellationToken);
    }

    /// <summary>
    /// 验证数字签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>验证结果</returns>
    public bool Verify(string data, byte[] signature, string publicKey)
    {
        if (data == null)
            throw new ArgumentNullException(nameof(data), "要验证的数据不能为null");
        // 允许空字符串作为有效输入

        var dataBytes = Encoding.UTF8.GetBytes(data);
        return Verify(dataBytes, signature, publicKey);
    }

    /// <summary>
    /// 验证数字签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>验证结果</returns>
    public bool Verify(byte[] data, byte[] signature, string publicKey)
    {
        // 参数验证 - 这些异常应该被抛出，不应该被捕获
        if (data == null)
            throw new ArgumentNullException(nameof(data), "要验证的数据不能为null");
        if (signature == null)
            throw new ArgumentNullException(nameof(signature), "签名不能为null");
        if (signature.Length == 0)
            throw new ArgumentException("签名不能为空", nameof(signature));
        if (string.IsNullOrEmpty(publicKey))
            throw new ArgumentException("公钥不能为空", nameof(publicKey));

        try
        {
            // 允许空字节数组作为有效输入
            using var rsa = RSA.Create();
            // 支持PEM格式和Base64格式
            if (publicKey.StartsWith("-----BEGIN"))
            {
                rsa.ImportFromPem(publicKey);
            }
            else
            {
                rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);
            }

            return rsa.VerifyData(data, signature, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
        }
        catch (Exception ex) when (!(ex is ArgumentException) && !(ex is ArgumentNullException))
        {
            // 只捕获非参数异常，参数异常应该被抛出
            return false;
        }
    }

    /// <summary>
    /// 验证数字签名（异步）
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">公钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    public async Task<bool> VerifyAsync(string data, byte[] signature, string publicKey, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => Verify(data, signature, publicKey), cancellationToken);
    }

    /// <summary>
    /// 验证数字签名（异步）
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">公钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    public async Task<bool> VerifyAsync(byte[] data, byte[] signature, string publicKey, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => Verify(data, signature, publicKey), cancellationToken);
    }
}
