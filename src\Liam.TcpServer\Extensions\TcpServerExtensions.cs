using System.Text;
using Liam.TcpServer.Interfaces;
using Liam.TcpServer.Models;

namespace Liam.TcpServer.Extensions;

/// <summary>
/// TCP服务器扩展方法
/// </summary>
public static class TcpServerExtensions
{
    /// <summary>
    /// 发送JSON数据到指定客户端
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <param name="connectionId">连接ID</param>
    /// <param name="jsonData">JSON数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<bool> SendJsonAsync(this ITcpServer server, string connectionId, string jsonData, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(server);
        ArgumentNullException.ThrowIfNull(connectionId);
        ArgumentNullException.ThrowIfNull(jsonData);

        var data = Encoding.UTF8.GetBytes(jsonData);
        return await server.SendAsync(connectionId, data, cancellationToken);
    }

    /// <summary>
    /// 广播JSON数据到所有客户端
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <param name="jsonData">JSON数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<int> BroadcastJsonAsync(this ITcpServer server, string jsonData, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(server);
        ArgumentNullException.ThrowIfNull(jsonData);

        var data = Encoding.UTF8.GetBytes(jsonData);
        return await server.BroadcastAsync(data, cancellationToken);
    }

    /// <summary>
    /// 发送文件到指定客户端
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <param name="connectionId">连接ID</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<bool> SendFileAsync(this ITcpServer server, string connectionId, string filePath, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(server);
        ArgumentNullException.ThrowIfNull(connectionId);
        ArgumentNullException.ThrowIfNull(filePath);

        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"文件不存在: {filePath}");
        }

        var fileData = await File.ReadAllBytesAsync(filePath, cancellationToken);
        return await server.SendAsync(connectionId, fileData, cancellationToken);
    }

    /// <summary>
    /// 发送流数据到指定客户端
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <param name="connectionId">连接ID</param>
    /// <param name="stream">数据流</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<bool> SendStreamAsync(this ITcpServer server, string connectionId, Stream stream, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(server);
        ArgumentNullException.ThrowIfNull(connectionId);
        ArgumentNullException.ThrowIfNull(stream);

        using var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream, cancellationToken);
        var data = memoryStream.ToArray();

        return await server.SendAsync(connectionId, data, cancellationToken);
    }

    /// <summary>
    /// 获取连接摘要信息
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <returns>连接摘要信息</returns>
    public static string GetConnectionsSummary(this ITcpServer server)
    {
        ArgumentNullException.ThrowIfNull(server);

        var connections = server.GetAllConnections();
        var activeConnections = server.GetActiveConnections();
        
        var summary = new StringBuilder();
        summary.AppendLine($"总连接数: {connections.Count}");
        summary.AppendLine($"活跃连接数: {activeConnections.Count}");
        summary.AppendLine($"最大连接数: {server.Configuration.MaxConnections}");
        summary.AppendLine($"连接使用率: {(double)connections.Count / server.Configuration.MaxConnections:P2}");

        if (activeConnections.Count > 0)
        {
            summary.AppendLine("\n活跃连接详情:");
            foreach (var connection in activeConnections.Take(10)) // 只显示前10个
            {
                summary.AppendLine($"  {connection.GetConnectionSummary()}");
            }

            if (activeConnections.Count > 10)
            {
                summary.AppendLine($"  ... 还有 {activeConnections.Count - 10} 个连接");
            }
        }

        return summary.ToString();
    }

    /// <summary>
    /// 获取服务器状态摘要
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <returns>服务器状态摘要</returns>
    public static string GetStatusSummary(this ITcpServer server)
    {
        ArgumentNullException.ThrowIfNull(server);

        var stats = server.GetStatistics();
        var summary = new StringBuilder();

        summary.AppendLine($"服务器状态: {server.Status}");
        summary.AppendLine($"运行时间: {server.Uptime?.ToString(@"dd\.hh\:mm\:ss") ?? "未运行"}");
        summary.AppendLine($"启动时间: {server.StartedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未启动"}");
        summary.AppendLine($"监听端口: {server.Configuration.Port}");
        summary.AppendLine($"SSL启用: {(server.Configuration.EnableSsl ? "是" : "否")}");
        summary.AppendLine($"心跳启用: {(server.Configuration.EnableHeartbeat ? "是" : "否")}");
        
        summary.AppendLine("\n连接统计:");
        summary.AppendLine($"  当前连接: {stats.CurrentConnections}");
        summary.AppendLine($"  总连接数: {stats.TotalConnections}");
        summary.AppendLine($"  峰值连接: {stats.MaxConcurrentConnections}");
        
        summary.AppendLine("\n数据统计:");
        summary.AppendLine($"  发送字节: {FormatBytes(stats.TotalBytesSent)}");
        summary.AppendLine($"  接收字节: {FormatBytes(stats.TotalBytesReceived)}");
        summary.AppendLine($"  发送消息: {stats.TotalMessagesSent:N0}");
        summary.AppendLine($"  接收消息: {stats.TotalMessagesReceived:N0}");
        summary.AppendLine($"  错误数量: {stats.TotalErrors:N0}");
        
        summary.AppendLine("\n性能指标:");
        summary.AppendLine($"  发送速率: {FormatBytes((long)stats.AverageSendRate)}/s");
        summary.AppendLine($"  接收速率: {FormatBytes((long)stats.AverageReceiveRate)}/s");
        summary.AppendLine($"  错误率: {stats.ErrorRate:P2}");

        return summary.ToString();
    }

    /// <summary>
    /// 等待服务器启动完成
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>等待任务</returns>
    public static async Task WaitForStartupAsync(this ITcpServer server, TimeSpan timeout = default, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(server);

        if (timeout == default)
        {
            timeout = TimeSpan.FromSeconds(30);
        }

        var startTime = DateTime.UtcNow;
        while (!server.IsRunning && DateTime.UtcNow - startTime < timeout)
        {
            cancellationToken.ThrowIfCancellationRequested();
            await Task.Delay(100, cancellationToken);
        }

        if (!server.IsRunning)
        {
            throw new TimeoutException($"服务器在 {timeout.TotalSeconds} 秒内未能启动");
        }
    }

    /// <summary>
    /// 等待服务器停止完成
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>等待任务</returns>
    public static async Task WaitForShutdownAsync(this ITcpServer server, TimeSpan timeout = default, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(server);

        if (timeout == default)
        {
            timeout = TimeSpan.FromSeconds(30);
        }

        var startTime = DateTime.UtcNow;
        while (server.IsRunning && DateTime.UtcNow - startTime < timeout)
        {
            cancellationToken.ThrowIfCancellationRequested();
            await Task.Delay(100, cancellationToken);
        }

        if (server.IsRunning)
        {
            throw new TimeoutException($"服务器在 {timeout.TotalSeconds} 秒内未能停止");
        }
    }

    /// <summary>
    /// 发送到指定IP的所有连接
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<int> SendToIpAsync(this ITcpServer server, string ipAddress, byte[] data, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(server);
        ArgumentNullException.ThrowIfNull(ipAddress);
        ArgumentNullException.ThrowIfNull(data);

        if (!System.Net.IPAddress.TryParse(ipAddress, out var ip))
        {
            throw new ArgumentException($"无效的IP地址: {ipAddress}", nameof(ipAddress));
        }

        var connections = server.GetAllConnections()
            .Where(c => c.ClientIpAddress.Equals(ip) && c.IsConnected)
            .ToList();

        int successCount = 0;
        var tasks = connections.Select(async connection =>
        {
            if (await server.SendAsync(connection.Id, data, cancellationToken))
            {
                Interlocked.Increment(ref successCount);
            }
        });

        await Task.WhenAll(tasks);
        return successCount;
    }

    /// <summary>
    /// 发送到已认证的连接
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<int> SendToAuthenticatedAsync(this ITcpServer server, byte[] data, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(server);
        ArgumentNullException.ThrowIfNull(data);

        var connections = server.GetAllConnections()
            .Where(c => c.IsAuthenticated && c.IsConnected)
            .ToList();

        int successCount = 0;
        var tasks = connections.Select(async connection =>
        {
            if (await server.SendAsync(connection.Id, data, cancellationToken))
            {
                Interlocked.Increment(ref successCount);
            }
        });

        await Task.WhenAll(tasks);
        return successCount;
    }

    /// <summary>
    /// 格式化字节数
    /// </summary>
    /// <param name="bytes">字节数</param>
    /// <returns>格式化后的字符串</returns>
    private static string FormatBytes(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
        int counter = 0;
        decimal number = bytes;
        
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        
        return $"{number:N1} {suffixes[counter]}";
    }

    /// <summary>
    /// 检查服务器健康状态
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <returns>健康状态信息</returns>
    public static HealthStatus CheckHealth(this ITcpServer server)
    {
        ArgumentNullException.ThrowIfNull(server);

        var stats = server.GetStatistics();
        var issues = new List<string>();

        // 检查服务器状态
        if (!server.IsRunning)
        {
            issues.Add("服务器未运行");
        }

        // 检查连接数
        var connectionUsage = (double)stats.CurrentConnections / server.Configuration.MaxConnections;
        if (connectionUsage > 0.9)
        {
            issues.Add($"连接数接近上限 ({connectionUsage:P1})");
        }

        // 检查错误率
        if (stats.ErrorRate > 0.05) // 5%
        {
            issues.Add($"错误率过高 ({stats.ErrorRate:P2})");
        }

        // 检查运行时间
        if (server.Uptime.HasValue && server.Uptime.Value.TotalDays > 30)
        {
            issues.Add($"运行时间过长 ({server.Uptime.Value.TotalDays:F1} 天)");
        }

        var isHealthy = issues.Count == 0;
        var status = isHealthy ? "健康" : "警告";

        return new HealthStatus
        {
            IsHealthy = isHealthy,
            Status = status,
            Issues = issues.AsReadOnly(),
            CheckedAt = DateTime.UtcNow,
            Statistics = stats
        };
    }
}

/// <summary>
/// 健康状态信息
/// </summary>
public class HealthStatus
{
    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 问题列表
    /// </summary>
    public IReadOnlyList<string> Issues { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckedAt { get; set; }

    /// <summary>
    /// 服务器统计信息
    /// </summary>
    public ServerStatistics? Statistics { get; set; }
}
