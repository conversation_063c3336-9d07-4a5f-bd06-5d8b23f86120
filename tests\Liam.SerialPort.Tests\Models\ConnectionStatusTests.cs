using FluentAssertions;
using Liam.SerialPort.Models;
using Liam.SerialPort.Tests.TestHelpers;
using Xunit;

namespace Liam.SerialPort.Tests.Models;

/// <summary>
/// ConnectionStatus 和相关类的测试
/// </summary>
public class ConnectionStatusTests : SerialPortTestBase
{
    [Theory]
    [InlineData(ConnectionStatus.Connected, true)]
    [InlineData(ConnectionStatus.Disconnected, false)]
    [InlineData(ConnectionStatus.Connecting, false)]
    [InlineData(ConnectionStatus.Disconnecting, false)]
    [InlineData(ConnectionStatus.Error, false)]
    [InlineData(ConnectionStatus.Reconnecting, false)]
    [InlineData(ConnectionStatus.Timeout, false)]
    [InlineData(ConnectionStatus.DeviceUnavailable, false)]
    public void IsConnected_ShouldReturnCorrectValue(ConnectionStatus status, bool expected)
    {
        // Act & Assert
        status.IsConnected().Should().Be(expected);
    }

    [Theory]
    [InlineData(ConnectionStatus.Disconnected, true)]
    [InlineData(ConnectionStatus.Connected, false)]
    [InlineData(ConnectionStatus.Connecting, false)]
    [InlineData(ConnectionStatus.Disconnecting, false)]
    [InlineData(ConnectionStatus.Error, false)]
    [InlineData(ConnectionStatus.Reconnecting, false)]
    [InlineData(ConnectionStatus.Timeout, false)]
    [InlineData(ConnectionStatus.DeviceUnavailable, false)]
    public void IsDisconnected_ShouldReturnCorrectValue(ConnectionStatus status, bool expected)
    {
        // Act & Assert
        status.IsDisconnected().Should().Be(expected);
    }

    [Theory]
    [InlineData(ConnectionStatus.Connecting, true)]
    [InlineData(ConnectionStatus.Disconnecting, true)]
    [InlineData(ConnectionStatus.Reconnecting, true)]
    [InlineData(ConnectionStatus.Connected, false)]
    [InlineData(ConnectionStatus.Disconnected, false)]
    [InlineData(ConnectionStatus.Error, false)]
    [InlineData(ConnectionStatus.Timeout, false)]
    [InlineData(ConnectionStatus.DeviceUnavailable, false)]
    public void IsTransitioning_ShouldReturnCorrectValue(ConnectionStatus status, bool expected)
    {
        // Act & Assert
        status.IsTransitioning().Should().Be(expected);
    }

    [Theory]
    [InlineData(ConnectionStatus.Error, true)]
    [InlineData(ConnectionStatus.Timeout, true)]
    [InlineData(ConnectionStatus.DeviceUnavailable, true)]
    [InlineData(ConnectionStatus.Connected, false)]
    [InlineData(ConnectionStatus.Disconnected, false)]
    [InlineData(ConnectionStatus.Connecting, false)]
    [InlineData(ConnectionStatus.Disconnecting, false)]
    [InlineData(ConnectionStatus.Reconnecting, false)]
    public void IsError_ShouldReturnCorrectValue(ConnectionStatus status, bool expected)
    {
        // Act & Assert
        status.IsError().Should().Be(expected);
    }

    [Theory]
    [InlineData(ConnectionStatus.Disconnected, "未连接")]
    [InlineData(ConnectionStatus.Connecting, "正在连接")]
    [InlineData(ConnectionStatus.Connected, "已连接")]
    [InlineData(ConnectionStatus.Disconnecting, "正在断开连接")]
    [InlineData(ConnectionStatus.Error, "连接错误")]
    [InlineData(ConnectionStatus.Reconnecting, "正在重连")]
    [InlineData(ConnectionStatus.Timeout, "连接超时")]
    [InlineData(ConnectionStatus.DeviceUnavailable, "设备不可用")]
    public void GetDescription_ShouldReturnCorrectDescription(ConnectionStatus status, string expected)
    {
        // Act & Assert
        status.GetDescription().Should().Be(expected);
    }

    [Theory]
    [InlineData(ConnectionStatus.Disconnected, true)]
    [InlineData(ConnectionStatus.Error, true)]
    [InlineData(ConnectionStatus.Timeout, true)]
    [InlineData(ConnectionStatus.DeviceUnavailable, true)]
    [InlineData(ConnectionStatus.Connected, false)]
    [InlineData(ConnectionStatus.Connecting, false)]
    [InlineData(ConnectionStatus.Disconnecting, false)]
    [InlineData(ConnectionStatus.Reconnecting, false)]
    public void CanConnect_ShouldReturnCorrectValue(ConnectionStatus status, bool expected)
    {
        // Act & Assert
        status.CanConnect().Should().Be(expected);
    }

    [Theory]
    [InlineData(ConnectionStatus.Connected, true)]
    [InlineData(ConnectionStatus.Connecting, true)]
    [InlineData(ConnectionStatus.Reconnecting, true)]
    [InlineData(ConnectionStatus.Disconnected, false)]
    [InlineData(ConnectionStatus.Disconnecting, false)]
    [InlineData(ConnectionStatus.Error, false)]
    [InlineData(ConnectionStatus.Timeout, false)]
    [InlineData(ConnectionStatus.DeviceUnavailable, false)]
    public void CanDisconnect_ShouldReturnCorrectValue(ConnectionStatus status, bool expected)
    {
        // Act & Assert
        status.CanDisconnect().Should().Be(expected);
    }

    [Theory]
    [InlineData(ConnectionStatus.Connected, true)]
    [InlineData(ConnectionStatus.Disconnected, false)]
    [InlineData(ConnectionStatus.Connecting, false)]
    [InlineData(ConnectionStatus.Disconnecting, false)]
    [InlineData(ConnectionStatus.Error, false)]
    [InlineData(ConnectionStatus.Reconnecting, false)]
    [InlineData(ConnectionStatus.Timeout, false)]
    [InlineData(ConnectionStatus.DeviceUnavailable, false)]
    public void CanSendData_ShouldReturnCorrectValue(ConnectionStatus status, bool expected)
    {
        // Act & Assert
        status.CanSendData().Should().Be(expected);
    }
}

/// <summary>
/// ConnectionStatistics 测试类
/// </summary>
public class ConnectionStatisticsTests : SerialPortTestBase
{
    [Fact]
    public void Constructor_ShouldSetDefaultValues()
    {
        // Arrange & Act
        var statistics = new ConnectionStatistics();

        // Assert
        statistics.ConnectedAt.Should().BeNull();
        statistics.LastActivity.Should().BeNull();
        statistics.Duration.Should().BeNull();
        statistics.BytesSent.Should().Be(0);
        statistics.BytesReceived.Should().Be(0);
        statistics.MessagesSent.Should().Be(0);
        statistics.MessagesReceived.Should().Be(0);
        statistics.ConnectionAttempts.Should().Be(0);
        statistics.ReconnectionCount.Should().Be(0);
        statistics.ErrorCount.Should().Be(0);
        statistics.LastErrorAt.Should().BeNull();
        statistics.LastError.Should().BeNull();
    }

    [Fact]
    public void Duration_WithConnectedAt_ShouldReturnCorrectDuration()
    {
        // Arrange
        var statistics = new ConnectionStatistics();
        var connectedAt = DateTime.Now.AddMinutes(-5);
        statistics.ConnectedAt = connectedAt;

        // Act
        var duration = statistics.Duration;

        // Assert
        duration.Should().NotBeNull();
        duration.Value.Should().BeCloseTo(TimeSpan.FromMinutes(5), TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Duration_WithoutConnectedAt_ShouldReturnNull()
    {
        // Arrange
        var statistics = new ConnectionStatistics();

        // Act
        var duration = statistics.Duration;

        // Assert
        duration.Should().BeNull();
    }

    [Fact]
    public void Reset_ShouldClearAllStatistics()
    {
        // Arrange
        var statistics = new ConnectionStatistics
        {
            ConnectedAt = DateTime.Now,
            LastActivity = DateTime.Now,
            BytesSent = 100,
            BytesReceived = 200,
            MessagesSent = 10,
            MessagesReceived = 20,
            ConnectionAttempts = 3,
            ReconnectionCount = 2,
            ErrorCount = 1,
            LastErrorAt = DateTime.Now,
            LastError = "Test error"
        };

        // Act
        statistics.Reset();

        // Assert
        statistics.ConnectedAt.Should().BeNull();
        statistics.LastActivity.Should().BeNull();
        statistics.BytesSent.Should().Be(0);
        statistics.BytesReceived.Should().Be(0);
        statistics.MessagesSent.Should().Be(0);
        statistics.MessagesReceived.Should().Be(0);
        statistics.ConnectionAttempts.Should().Be(0);
        statistics.ReconnectionCount.Should().Be(0);
        statistics.ErrorCount.Should().Be(0);
        statistics.LastErrorAt.Should().BeNull();
        statistics.LastError.Should().BeNull();
    }

    [Fact]
    public void RecordSent_ShouldUpdateStatistics()
    {
        // Arrange
        var statistics = new ConnectionStatistics();
        var beforeTime = DateTime.Now;

        // Act
        statistics.RecordSent(100);

        // Assert
        statistics.BytesSent.Should().Be(100);
        statistics.MessagesSent.Should().Be(1);
        statistics.LastActivity.Should().NotBeNull();
        statistics.LastActivity.Should().BeOnOrAfter(beforeTime);
    }

    [Fact]
    public void RecordReceived_ShouldUpdateStatistics()
    {
        // Arrange
        var statistics = new ConnectionStatistics();
        var beforeTime = DateTime.Now;

        // Act
        statistics.RecordReceived(200);

        // Assert
        statistics.BytesReceived.Should().Be(200);
        statistics.MessagesReceived.Should().Be(1);
        statistics.LastActivity.Should().NotBeNull();
        statistics.LastActivity.Should().BeOnOrAfter(beforeTime);
    }

    [Fact]
    public void RecordConnection_ShouldUpdateStatistics()
    {
        // Arrange
        var statistics = new ConnectionStatistics();
        var beforeTime = DateTime.Now;

        // Act
        statistics.RecordConnection();

        // Assert
        statistics.ConnectedAt.Should().NotBeNull();
        statistics.ConnectedAt.Should().BeOnOrAfter(beforeTime);
        statistics.LastActivity.Should().NotBeNull();
        statistics.LastActivity.Should().BeOnOrAfter(beforeTime);
        statistics.ConnectionAttempts.Should().Be(1);
    }

    [Fact]
    public void RecordReconnection_ShouldUpdateStatistics()
    {
        // Arrange
        var statistics = new ConnectionStatistics();

        // Act
        statistics.RecordReconnection();

        // Assert
        statistics.ReconnectionCount.Should().Be(1);
        statistics.ConnectionAttempts.Should().Be(1);
    }

    [Fact]
    public void RecordError_ShouldUpdateStatistics()
    {
        // Arrange
        var statistics = new ConnectionStatistics();
        var errorMessage = "Test error message";
        var beforeTime = DateTime.Now;

        // Act
        statistics.RecordError(errorMessage);

        // Assert
        statistics.ErrorCount.Should().Be(1);
        statistics.LastErrorAt.Should().NotBeNull();
        statistics.LastErrorAt.Should().BeOnOrAfter(beforeTime);
        statistics.LastError.Should().Be(errorMessage);
    }

    [Fact]
    public void MultipleOperations_ShouldAccumulateStatistics()
    {
        // Arrange
        var statistics = new ConnectionStatistics();

        // Act
        statistics.RecordConnection();
        statistics.RecordSent(100);
        statistics.RecordSent(50);
        statistics.RecordReceived(200);
        statistics.RecordReceived(150);
        statistics.RecordReconnection();
        statistics.RecordError("Error 1");
        statistics.RecordError("Error 2");

        // Assert
        statistics.ConnectionAttempts.Should().Be(2); // 1 connection + 1 reconnection
        statistics.BytesSent.Should().Be(150);
        statistics.BytesReceived.Should().Be(350);
        statistics.MessagesSent.Should().Be(2);
        statistics.MessagesReceived.Should().Be(2);
        statistics.ReconnectionCount.Should().Be(1);
        statistics.ErrorCount.Should().Be(2);
        statistics.LastError.Should().Be("Error 2");
    }
}
