using System.Net.Sockets;
using Liam.TcpClient.Models;

namespace Liam.TcpClient.Interfaces;

/// <summary>
/// 连接管理器接口
/// </summary>
public interface IConnectionManager : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 连接信息
    /// </summary>
    ConnectionInfo? ConnectionInfo { get; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// 是否正在连接
    /// </summary>
    bool IsConnecting { get; }

    /// <summary>
    /// 是否正在重连
    /// </summary>
    bool IsReconnecting { get; }

    /// <summary>
    /// TCP客户端
    /// </summary>
    System.Net.Sockets.TcpClient? TcpClient { get; }

    /// <summary>
    /// 网络流
    /// </summary>
    Stream? Stream { get; }

    /// <summary>
    /// 连接状态变更事件
    /// </summary>
    event EventHandler<string>? StateChanged;

    /// <summary>
    /// 连接建立事件
    /// </summary>
    event EventHandler<ConnectionInfo>? Connected;

    /// <summary>
    /// 连接断开事件
    /// </summary>
    event EventHandler<(ConnectionInfo ConnectionInfo, string? Reason, Exception? Exception)>? Disconnected;

    /// <summary>
    /// 连接到服务器
    /// </summary>
    /// <param name="config">客户端配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接任务</returns>
    Task<bool> ConnectAsync(TcpClientConfig config, CancellationToken cancellationToken = default);

    /// <summary>
    /// 连接到指定服务器
    /// </summary>
    /// <param name="host">服务器主机</param>
    /// <param name="port">服务器端口</param>
    /// <param name="config">客户端配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接任务</returns>
    Task<bool> ConnectAsync(string host, int port, TcpClientConfig config, CancellationToken cancellationToken = default);

    /// <summary>
    /// 断开连接
    /// </summary>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>断开任务</returns>
    Task DisconnectAsync(string? reason = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重新连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重连任务</returns>
    Task<bool> ReconnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查连接状态
    /// </summary>
    /// <returns>是否连接正常</returns>
    bool CheckConnection();

    /// <summary>
    /// 获取连接延迟
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>延迟时间（毫秒）</returns>
    Task<double?> GetLatencyAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 刷新连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>刷新任务</returns>
    Task RefreshAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 启用自动重连
    /// </summary>
    void EnableAutoReconnect();

    /// <summary>
    /// 禁用自动重连
    /// </summary>
    void DisableAutoReconnect();

    /// <summary>
    /// 获取连接质量评分
    /// </summary>
    /// <returns>质量评分（0-100）</returns>
    double GetConnectionQuality();
}

/// <summary>
/// 心跳管理器接口
/// </summary>
public interface IHeartbeatManager : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 是否启用心跳
    /// </summary>
    bool IsEnabled { get; }

    /// <summary>
    /// 是否正在运行
    /// </summary>
    bool IsRunning { get; }

    /// <summary>
    /// 心跳间隔
    /// </summary>
    TimeSpan Interval { get; set; }

    /// <summary>
    /// 心跳超时时间
    /// </summary>
    TimeSpan Timeout { get; set; }

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    DateTime? LastHeartbeatAt { get; }

    /// <summary>
    /// 最后响应时间
    /// </summary>
    DateTime? LastResponseAt { get; }

    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    double AverageResponseTime { get; }

    /// <summary>
    /// 心跳事件
    /// </summary>
    event EventHandler<(DateTime Timestamp, double? ResponseTime)>? HeartbeatSent;

    /// <summary>
    /// 心跳响应事件
    /// </summary>
    event EventHandler<(DateTime Timestamp, double ResponseTime)>? HeartbeatReceived;

    /// <summary>
    /// 心跳超时事件
    /// </summary>
    event EventHandler<DateTime>? HeartbeatTimeout;

    /// <summary>
    /// 启动心跳检测
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止心跳检测
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送心跳
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendHeartbeatAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理心跳响应
    /// </summary>
    /// <param name="message">心跳消息</param>
    void HandleHeartbeatResponse(TcpMessage message);

    /// <summary>
    /// 重置心跳统计
    /// </summary>
    void ResetStatistics();
}

/// <summary>
/// 消息处理器接口
/// </summary>
public interface IMessageHandler : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 是否正在运行
    /// </summary>
    bool IsRunning { get; }

    /// <summary>
    /// 消息接收事件
    /// </summary>
    event EventHandler<TcpMessage>? MessageReceived;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    event EventHandler<byte[]>? DataReceived;

    /// <summary>
    /// 错误事件
    /// </summary>
    event EventHandler<Exception>? Error;

    /// <summary>
    /// 启动消息处理
    /// </summary>
    /// <param name="stream">网络流</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(Stream stream, CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止消息处理
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendAsync(byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendMessageAsync(TcpMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 等待接收数据
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收的数据</returns>
    Task<byte[]?> ReceiveAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 等待接收消息
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收的消息</returns>
    Task<TcpMessage?> ReceiveMessageAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default);
}
