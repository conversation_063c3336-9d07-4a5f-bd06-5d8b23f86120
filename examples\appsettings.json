{"Logging": {"MinimumLevel": "Information", "EnableAsync": true, "AsyncQueueSize": 10000, "BatchSize": 100, "BatchTimeoutMs": 1000, "IncludeScopes": true, "IncludeExceptionDetails": true, "IncludeSourceInfo": false, "ApplicationName": "<PERSON>.Logging.Example", "Environment": "Development", "Providers": [{"TypeName": "<PERSON><PERSON><PERSON>", "Enabled": true, "Settings": {"EnableColors": true, "UseStandardError": false, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff", "FormatterType": "Text"}}, {"TypeName": "File", "Enabled": true, "Settings": {"FilePath": "logs/example.log", "EnableRotation": true, "MaxFileSize": 10485760, "RetainedFileCount": 10, "RotationInterval": "1.00:00:00", "Encoding": "UTF-8", "AutoFlush": true, "BufferSize": 4096, "FormatterType": "Text"}}], "Filters": [{"Type": "Level", "Condition": "Information", "Action": "Include"}, {"Type": "Category", "Condition": "Microsoft", "Action": "Exclude"}]}}