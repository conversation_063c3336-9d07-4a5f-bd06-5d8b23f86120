using System.Collections.Concurrent;
using System.IO.Ports;
using System.Text;
using Microsoft.Extensions.Logging;
using Liam.SerialPort.Constants;
using Liam.SerialPort.Events;
using Liam.SerialPort.Exceptions;
using Liam.SerialPort.Interfaces;

namespace Liam.SerialPort.Services;

/// <summary>
/// 串口数据处理服务实现
/// </summary>
public class SerialPortDataHandler : ISerialPortDataHandler
{
    private readonly ILogger<SerialPortDataHandler> _logger;
    private readonly object _lock = new();
    private readonly ConcurrentQueue<byte[]> _receiveBuffer = new();
    private readonly ManualResetEventSlim _dataReceivedEvent = new(false);
    
    private System.IO.Ports.SerialPort? _serialPort;
    private string _portName = string.Empty;
    private bool _isListening;
    private bool _disposed;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    public event EventHandler<DataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 数据发送完成事件
    /// </summary>
    public event EventHandler<DataSentEventArgs>? DataSent;

    /// <summary>
    /// 数据处理错误事件
    /// </summary>
    public event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 获取接收缓冲区中的字节数
    /// </summary>
    public int BytesToRead => _serialPort?.BytesToRead ?? 0;

    /// <summary>
    /// 获取发送缓冲区中的字节数
    /// </summary>
    public int BytesToWrite => _serialPort?.BytesToWrite ?? 0;

    /// <summary>
    /// 获取接收缓冲区大小
    /// </summary>
    public int ReceiveBufferSize => _serialPort?.ReadBufferSize ?? 0;

    /// <summary>
    /// 获取发送缓冲区大小
    /// </summary>
    public int SendBufferSize => _serialPort?.WriteBufferSize ?? 0;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public SerialPortDataHandler(ILogger<SerialPortDataHandler> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 设置串口对象
    /// </summary>
    /// <param name="serialPort">串口对象</param>
    /// <param name="portName">串口名称</param>
    public void SetSerialPort(System.IO.Ports.SerialPort serialPort, string portName)
    {
        lock (_lock)
        {
            _serialPort = serialPort ?? throw new ArgumentNullException(nameof(serialPort));
            _portName = portName ?? throw new ArgumentNullException(nameof(portName));

            // 注册数据接收事件
            _serialPort.DataReceived += OnSerialPortDataReceived;
            _serialPort.ErrorReceived += OnSerialPortErrorReceived;
        }
    }

    /// <summary>
    /// 发送数据（字节数组）
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task SendAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        if (data == null)
            throw new ArgumentNullException(nameof(data));

        if (data.Length == 0)
            return;

        if (_serialPort?.IsOpen != true)
            throw new SerialPortConnectionException(_portName, "串口未连接");

        var startTime = DateTime.Now;
        var success = false;
        Exception? exception = null;

        try
        {
            _logger.LogDebug("发送数据到串口 {PortName}，长度：{Length} 字节", _portName, data.Length);

            await Task.Run(() =>
            {
                _serialPort.Write(data, 0, data.Length);
            }, cancellationToken);

            success = true;
            _logger.LogDebug("数据发送成功，串口：{PortName}，长度：{Length} 字节", _portName, data.Length);
        }
        catch (OperationCanceledException)
        {
            exception = new SerialPortException(_portName, "发送操作被取消");
            throw exception;
        }
        catch (TimeoutException ex)
        {
            exception = new SerialPortTimeoutException(_portName, TimeSpan.FromMilliseconds(_serialPort.WriteTimeout), "发送", ex);
            throw exception;
        }
        catch (Exception ex)
        {
            exception = new SerialPortDataException(_portName, $"发送数据时发生错误: {ex.Message}", data.Length, ex);
            OnErrorOccurred(new SerialPortErrorEventArgs(_portName, SerialPortErrorType.Write, exception.Message, ex));
            throw exception;
        }
        finally
        {
            var duration = DateTime.Now - startTime;
            OnDataSent(new DataSentEventArgs(_portName, data, success, duration, exception?.Message));
        }
    }

    /// <summary>
    /// 发送数据（字符串）
    /// </summary>
    /// <param name="data">要发送的字符串</param>
    /// <param name="encoding">字符编码，默认为UTF-8</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task SendAsync(string data, Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(data))
            return;

        encoding ??= Encoding.UTF8;
        var bytes = encoding.GetBytes(data);
        await SendAsync(bytes, cancellationToken);
    }

    /// <summary>
    /// 发送数据（十六进制字符串）
    /// </summary>
    /// <param name="hexData">十六进制字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task SendHexAsync(string hexData, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(hexData))
            return;

        try
        {
            // 清理十六进制字符串
            var cleanHex = hexData.Replace(" ", "").Replace("-", "").Replace(":", "").Replace(",", "");
            
            if (cleanHex.Length % 2 != 0)
                throw new ArgumentException("十六进制字符串长度必须为偶数", nameof(hexData));

            var bytes = new byte[cleanHex.Length / 2];
            for (int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
            }

            await SendAsync(bytes, cancellationToken);
        }
        catch (Exception ex) when (!(ex is SerialPortException))
        {
            throw new SerialPortDataException(_portName, $"解析十六进制数据时发生错误: {ex.Message}", hexData.Length, ex);
        }
    }

    /// <summary>
    /// 读取数据（字节数组）
    /// </summary>
    /// <param name="count">要读取的字节数，-1表示读取所有可用数据</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取到的数据</returns>
    public async Task<byte[]> ReadAsync(int count = -1, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        if (_serialPort?.IsOpen != true)
            throw new SerialPortConnectionException(_portName, "串口未连接");

        timeout ??= TimeSpan.FromMilliseconds(_serialPort.ReadTimeout);

        try
        {
            return await Task.Run(async () =>
            {
                var buffer = new List<byte>();
                var endTime = DateTime.Now.Add(timeout.Value);

                while (DateTime.Now < endTime && !cancellationToken.IsCancellationRequested)
                {
                    if (_serialPort.BytesToRead > 0)
                    {
                        var availableBytes = _serialPort.BytesToRead;
                        var bytesToRead = count > 0 ? Math.Min(count - buffer.Count, availableBytes) : availableBytes;
                        
                        var tempBuffer = new byte[bytesToRead];
                        var bytesRead = _serialPort.Read(tempBuffer, 0, bytesToRead);
                        
                        if (bytesRead > 0)
                        {
                            buffer.AddRange(tempBuffer.Take(bytesRead));
                            
                            if (count > 0 && buffer.Count >= count)
                                break;
                        }
                    }

                    if (buffer.Count == 0 || (count > 0 && buffer.Count < count))
                    {
                        await Task.Delay(SerialPortConstants.Performance.AsyncDelay, cancellationToken);
                    }
                }

                return buffer.ToArray();
            }, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            throw new SerialPortException(_portName, "读取操作被取消");
        }
        catch (TimeoutException ex)
        {
            throw new SerialPortTimeoutException(_portName, timeout.Value, "读取", ex);
        }
        catch (Exception ex)
        {
            var errorMessage = $"读取数据时发生错误: {ex.Message}";
            OnErrorOccurred(new SerialPortErrorEventArgs(_portName, SerialPortErrorType.Read, errorMessage, ex));
            throw new SerialPortDataException(_portName, errorMessage, count, ex);
        }
    }

    /// <summary>
    /// 读取数据（字符串）
    /// </summary>
    /// <param name="encoding">字符编码，默认为UTF-8</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取到的字符串</returns>
    public async Task<string> ReadStringAsync(Encoding? encoding = null, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        encoding ??= Encoding.UTF8;
        var data = await ReadAsync(-1, timeout, cancellationToken);
        return encoding.GetString(data);
    }

    /// <summary>
    /// 读取一行数据
    /// </summary>
    /// <param name="encoding">字符编码，默认为UTF-8</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取到的行数据</returns>
    public async Task<string> ReadLineAsync(Encoding? encoding = null, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        if (_serialPort?.IsOpen != true)
            throw new SerialPortConnectionException(_portName, "串口未连接");

        encoding ??= Encoding.UTF8;
        timeout ??= TimeSpan.FromMilliseconds(_serialPort.ReadTimeout);

        try
        {
            return await Task.Run(async () =>
            {
                var buffer = new List<byte>();
                var endTime = DateTime.Now.Add(timeout.Value);
                var newLineBytes = encoding.GetBytes(_serialPort.NewLine);

                while (DateTime.Now < endTime && !cancellationToken.IsCancellationRequested)
                {
                    if (_serialPort.BytesToRead > 0)
                    {
                        var b = _serialPort.ReadByte();
                        if (b >= 0)
                        {
                            buffer.Add((byte)b);

                            // 检查是否遇到换行符
                            if (buffer.Count >= newLineBytes.Length)
                            {
                                var lastBytes = buffer.Skip(buffer.Count - newLineBytes.Length).ToArray();
                                if (lastBytes.SequenceEqual(newLineBytes))
                                {
                                    // 移除换行符
                                    buffer.RemoveRange(buffer.Count - newLineBytes.Length, newLineBytes.Length);
                                    break;
                                }
                            }
                        }
                    }
                    else
                    {
                        await Task.Delay(SerialPortConstants.Performance.AsyncDelay, cancellationToken);
                    }
                }

                return encoding.GetString(buffer.ToArray());
            }, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            throw new SerialPortException(_portName, "读取行操作被取消");
        }
        catch (TimeoutException ex)
        {
            throw new SerialPortTimeoutException(_portName, timeout.Value, "读取行", ex);
        }
        catch (Exception ex)
        {
            var errorMessage = $"读取行数据时发生错误: {ex.Message}";
            OnErrorOccurred(new SerialPortErrorEventArgs(_portName, SerialPortErrorType.Read, errorMessage, ex));
            throw new SerialPortDataException(_portName, errorMessage, -1, ex);
        }
    }

    /// <summary>
    /// 发送数据并等待响应
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收到的响应数据</returns>
    public async Task<byte[]> SendAndReceiveAsync(byte[] data, TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        // 清空接收缓冲区
        ClearReceiveBuffer();

        // 发送数据
        await SendAsync(data, cancellationToken);

        // 等待响应
        return await ReadAsync(-1, timeout, cancellationToken);
    }

    /// <summary>
    /// 发送数据并等待响应
    /// </summary>
    /// <param name="data">要发送的字符串</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="encoding">字符编码，默认为UTF-8</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收到的响应字符串</returns>
    public async Task<string> SendAndReceiveAsync(string data, TimeSpan timeout, Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        encoding ??= Encoding.UTF8;
        var sendBytes = encoding.GetBytes(data);
        var responseBytes = await SendAndReceiveAsync(sendBytes, timeout, cancellationToken);
        return encoding.GetString(responseBytes);
    }

    /// <summary>
    /// 清空接收缓冲区
    /// </summary>
    public void ClearReceiveBuffer()
    {
        try
        {
            _serialPort?.DiscardInBuffer();
            
            // 清空内部缓冲区
            while (_receiveBuffer.TryDequeue(out _)) { }
            
            _logger.LogDebug("已清空串口 {PortName} 接收缓冲区", _portName);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "清空接收缓冲区时发生错误");
        }
    }

    /// <summary>
    /// 清空发送缓冲区
    /// </summary>
    public void ClearSendBuffer()
    {
        try
        {
            _serialPort?.DiscardOutBuffer();
            _logger.LogDebug("已清空串口 {PortName} 发送缓冲区", _portName);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "清空发送缓冲区时发生错误");
        }
    }

    /// <summary>
    /// 清空所有缓冲区
    /// </summary>
    public void ClearAllBuffers()
    {
        ClearReceiveBuffer();
        ClearSendBuffer();
    }

    /// <summary>
    /// 开始数据接收监听
    /// </summary>
    public void StartListening()
    {
        lock (_lock)
        {
            if (_isListening)
                return;

            _isListening = true;
            _logger.LogDebug("开始监听串口 {PortName} 数据接收", _portName);
        }
    }

    /// <summary>
    /// 停止数据接收监听
    /// </summary>
    public void StopListening()
    {
        lock (_lock)
        {
            if (!_isListening)
                return;

            _isListening = false;
            _logger.LogDebug("停止监听串口 {PortName} 数据接收", _portName);
        }
    }

    /// <summary>
    /// 串口数据接收事件处理
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnSerialPortDataReceived(object sender, SerialDataReceivedEventArgs e)
    {
        if (!_isListening || _disposed)
            return;

        try
        {
            if (_serialPort?.BytesToRead > 0)
            {
                var buffer = new byte[_serialPort.BytesToRead];
                var bytesRead = _serialPort.Read(buffer, 0, buffer.Length);
                
                if (bytesRead > 0)
                {
                    var data = new byte[bytesRead];
                    Array.Copy(buffer, data, bytesRead);
                    
                    _receiveBuffer.Enqueue(data);
                    _dataReceivedEvent.Set();
                    
                    OnDataReceived(new DataReceivedEventArgs(_portName, data));
                    
                    _logger.LogDebug("接收到串口 {PortName} 数据，长度：{Length} 字节", _portName, bytesRead);
                }
            }
        }
        catch (Exception ex)
        {
            var errorMessage = $"处理接收数据时发生错误: {ex.Message}";
            OnErrorOccurred(new SerialPortErrorEventArgs(_portName, SerialPortErrorType.Read, errorMessage, ex));
            _logger.LogError(ex, "处理串口 {PortName} 接收数据时发生错误", _portName);
        }
    }

    /// <summary>
    /// 串口错误事件处理
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnSerialPortErrorReceived(object sender, SerialErrorReceivedEventArgs e)
    {
        var errorType = e.EventType switch
        {
            SerialError.Frame => SerialPortErrorType.Frame,
            SerialError.Overrun => SerialPortErrorType.Overrun,
            SerialError.RXOver => SerialPortErrorType.BufferOverflow,
            SerialError.RXParity => SerialPortErrorType.Parity,
            SerialError.TXFull => SerialPortErrorType.BufferOverflow,
            _ => SerialPortErrorType.Unknown
        };

        var errorMessage = $"串口错误: {e.EventType}";
        OnErrorOccurred(new SerialPortErrorEventArgs(_portName, errorType, errorMessage));
        
        _logger.LogWarning("串口 {PortName} 发生错误: {ErrorType}", _portName, e.EventType);
    }

    /// <summary>
    /// 触发数据接收事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnDataReceived(DataReceivedEventArgs e)
    {
        try
        {
            DataReceived?.Invoke(this, e);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "触发数据接收事件时发生错误");
        }
    }

    /// <summary>
    /// 触发数据发送事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnDataSent(DataSentEventArgs e)
    {
        try
        {
            DataSent?.Invoke(this, e);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "触发数据发送事件时发生错误");
        }
    }

    /// <summary>
    /// 触发错误事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnErrorOccurred(SerialPortErrorEventArgs e)
    {
        try
        {
            ErrorOccurred?.Invoke(this, e);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "触发错误事件时发生错误");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            StopListening();
            
            if (_serialPort != null)
            {
                _serialPort.DataReceived -= OnSerialPortDataReceived;
                _serialPort.ErrorReceived -= OnSerialPortErrorReceived;
            }

            _dataReceivedEvent.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放SerialPortDataHandler资源时发生错误");
        }
        finally
        {
            _disposed = true;
        }
    }
}
