using Liam.Logging.Constants;
using Liam.Logging.Interfaces;
using Liam.Logging.Models;
using System.Text.RegularExpressions;

namespace Liam.Logging.Filters;

/// <summary>
/// 日志级别过滤器
/// </summary>
public class LogLevelFilter : ILogFilter
{
    private readonly LogLevel _minimumLevel;
    private readonly LogLevel _maximumLevel;

    /// <summary>
    /// 过滤器名称
    /// </summary>
    public string Name => "LevelFilter";

    /// <summary>
    /// 初始化日志级别过滤器
    /// </summary>
    /// <param name="minimumLevel">最小日志级别</param>
    /// <param name="maximumLevel">最大日志级别</param>
    public LogLevelFilter(LogLevel minimumLevel, LogLevel maximumLevel = LogLevel.Critical)
    {
        _minimumLevel = minimumLevel;
        _maximumLevel = maximumLevel;
    }

    /// <summary>
    /// 判断是否应该记录日志
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>是否应该记录</returns>
    public bool ShouldLog(LogEvent logEvent)
    {
        return logEvent.Level >= _minimumLevel && logEvent.Level <= _maximumLevel;
    }
}

/// <summary>
/// 日志类别过滤器
/// </summary>
public class LogCategoryFilter : ILogFilter
{
    private readonly HashSet<string> _allowedCategories;
    private readonly HashSet<string> _blockedCategories;
    private readonly bool _isWhitelist;

    /// <summary>
    /// 过滤器名称
    /// </summary>
    public string Name => "CategoryFilter";

    /// <summary>
    /// 初始化日志类别过滤器（白名单模式）
    /// </summary>
    /// <param name="allowedCategories">允许的类别</param>
    public LogCategoryFilter(IEnumerable<string> allowedCategories)
    {
        _allowedCategories = new HashSet<string>(allowedCategories, StringComparer.OrdinalIgnoreCase);
        _blockedCategories = new HashSet<string>();
        _isWhitelist = true;
    }

    /// <summary>
    /// 初始化日志类别过滤器（黑名单模式）
    /// </summary>
    /// <param name="blockedCategories">阻止的类别</param>
    /// <param name="isBlacklist">是否为黑名单模式</param>
    public LogCategoryFilter(IEnumerable<string> blockedCategories, bool isBlacklist)
    {
        _allowedCategories = new HashSet<string>();
        _blockedCategories = new HashSet<string>(blockedCategories, StringComparer.OrdinalIgnoreCase);
        _isWhitelist = !isBlacklist;
    }

    /// <summary>
    /// 判断是否应该记录日志
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>是否应该记录</returns>
    public bool ShouldLog(LogEvent logEvent)
    {
        if (_isWhitelist)
        {
            return _allowedCategories.Count == 0 || _allowedCategories.Contains(logEvent.Category);
        }
        else
        {
            return !_blockedCategories.Contains(logEvent.Category);
        }
    }
}

/// <summary>
/// 日志消息内容过滤器
/// </summary>
public class LogMessageFilter : ILogFilter
{
    private readonly List<Regex> _includePatterns;
    private readonly List<Regex> _excludePatterns;

    /// <summary>
    /// 过滤器名称
    /// </summary>
    public string Name => "MessageFilter";

    /// <summary>
    /// 初始化日志消息内容过滤器
    /// </summary>
    /// <param name="includePatterns">包含的正则表达式模式</param>
    /// <param name="excludePatterns">排除的正则表达式模式</param>
    public LogMessageFilter(IEnumerable<string>? includePatterns = null, IEnumerable<string>? excludePatterns = null)
    {
        _includePatterns = includePatterns?.Select(p => new Regex(p, RegexOptions.IgnoreCase | RegexOptions.Compiled)).ToList() ?? new List<Regex>();
        _excludePatterns = excludePatterns?.Select(p => new Regex(p, RegexOptions.IgnoreCase | RegexOptions.Compiled)).ToList() ?? new List<Regex>();
    }

    /// <summary>
    /// 判断是否应该记录日志
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>是否应该记录</returns>
    public bool ShouldLog(LogEvent logEvent)
    {
        var message = logEvent.Message;

        // 检查排除模式
        if (_excludePatterns.Any(pattern => pattern.IsMatch(message)))
        {
            return false;
        }

        // 检查包含模式
        if (_includePatterns.Count > 0)
        {
            return _includePatterns.Any(pattern => pattern.IsMatch(message));
        }

        return true;
    }
}

/// <summary>
/// 日志属性过滤器
/// </summary>
public class LogPropertyFilter : ILogFilter
{
    private readonly Dictionary<string, object?> _requiredProperties;
    private readonly Dictionary<string, object?> _excludedProperties;

    /// <summary>
    /// 过滤器名称
    /// </summary>
    public string Name => "PropertyFilter";

    /// <summary>
    /// 初始化日志属性过滤器
    /// </summary>
    /// <param name="requiredProperties">必需的属性</param>
    /// <param name="excludedProperties">排除的属性</param>
    public LogPropertyFilter(
        Dictionary<string, object?>? requiredProperties = null,
        Dictionary<string, object?>? excludedProperties = null)
    {
        _requiredProperties = requiredProperties ?? new Dictionary<string, object?>();
        _excludedProperties = excludedProperties ?? new Dictionary<string, object?>();
    }

    /// <summary>
    /// 判断是否应该记录日志
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>是否应该记录</returns>
    public bool ShouldLog(LogEvent logEvent)
    {
        // 检查排除属性
        foreach (var excludedProperty in _excludedProperties)
        {
            if (logEvent.Properties.TryGetValue(excludedProperty.Key, out var value) &&
                Equals(value, excludedProperty.Value))
            {
                return false;
            }
        }

        // 检查必需属性
        foreach (var requiredProperty in _requiredProperties)
        {
            if (!logEvent.Properties.TryGetValue(requiredProperty.Key, out var value) ||
                !Equals(value, requiredProperty.Value))
            {
                return false;
            }
        }

        return true;
    }
}

/// <summary>
/// 时间范围过滤器
/// </summary>
public class LogTimeRangeFilter : ILogFilter
{
    private readonly TimeSpan? _startTime;
    private readonly TimeSpan? _endTime;
    private readonly DayOfWeek[]? _allowedDays;

    /// <summary>
    /// 过滤器名称
    /// </summary>
    public string Name => "TimeRangeFilter";

    /// <summary>
    /// 初始化时间范围过滤器
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="allowedDays">允许的星期</param>
    public LogTimeRangeFilter(TimeSpan? startTime = null, TimeSpan? endTime = null, DayOfWeek[]? allowedDays = null)
    {
        _startTime = startTime;
        _endTime = endTime;
        _allowedDays = allowedDays;
    }

    /// <summary>
    /// 判断是否应该记录日志
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>是否应该记录</returns>
    public bool ShouldLog(LogEvent logEvent)
    {
        var timestamp = logEvent.Timestamp;

        // 检查星期
        if (_allowedDays != null && !_allowedDays.Contains(timestamp.DayOfWeek))
        {
            return false;
        }

        var timeOfDay = timestamp.TimeOfDay;

        // 检查时间范围
        if (_startTime.HasValue && timeOfDay < _startTime.Value)
        {
            return false;
        }

        if (_endTime.HasValue && timeOfDay > _endTime.Value)
        {
            return false;
        }

        return true;
    }
}

/// <summary>
/// 复合日志过滤器
/// </summary>
public class CompositeLogFilter : ILogFilter
{
    private readonly List<ILogFilter> _filters;
    private readonly LogFilterCombineMode _combineMode;

    /// <summary>
    /// 过滤器名称
    /// </summary>
    public string Name => "CompositeFilter";

    /// <summary>
    /// 初始化复合日志过滤器
    /// </summary>
    /// <param name="filters">子过滤器集合</param>
    /// <param name="combineMode">组合模式</param>
    public CompositeLogFilter(IEnumerable<ILogFilter> filters, LogFilterCombineMode combineMode = LogFilterCombineMode.And)
    {
        _filters = filters.ToList();
        _combineMode = combineMode;
    }

    /// <summary>
    /// 判断是否应该记录日志
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>是否应该记录</returns>
    public bool ShouldLog(LogEvent logEvent)
    {
        if (_filters.Count == 0)
        {
            return true;
        }

        return _combineMode switch
        {
            LogFilterCombineMode.And => _filters.All(filter => filter.ShouldLog(logEvent)),
            LogFilterCombineMode.Or => _filters.Any(filter => filter.ShouldLog(logEvent)),
            _ => true
        };
    }
}

/// <summary>
/// 日志过滤器组合模式
/// </summary>
public enum LogFilterCombineMode
{
    /// <summary>
    /// 与操作（所有过滤器都通过）
    /// </summary>
    And,

    /// <summary>
    /// 或操作（任一过滤器通过）
    /// </summary>
    Or
}
