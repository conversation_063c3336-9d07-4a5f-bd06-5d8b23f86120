using Liam.Cryptography.Services;
using Liam.Cryptography.Exceptions;
using Liam.Cryptography.Tests.Fixtures;
using Liam.Cryptography.Tests.TestHelpers;

namespace Liam.Cryptography.Tests.Services;

/// <summary>
/// SHA256哈希提供者测试
/// </summary>
[Collection("Crypto Tests")]
public class Sha256HashProviderTests
{
    private readonly CryptoTestFixture _fixture;
    private readonly Sha256HashProvider _hashService;

    public Sha256HashProviderTests(CryptoTestFixture fixture)
    {
        _fixture = fixture;
        _hashService = _fixture.HashService;
    }

    #region 字符串哈希测试
    [Fact]
    public void ComputeHash_ValidString_ShouldReturnCorrectHash()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.SimpleText;
        var expectedHash = "a591a6d40bf420404a011733cfb7b190d62c65bf0bcda32b57b277d9ad9f146e"; // "Hello World"的SHA256哈希

        // Act
        var hash = _hashService.ComputeHash(input);

        // Assert
        hash.Should().NotBeNullOrEmpty();
        hash.Should().Be(expectedHash);
        hash.Length.Should().Be(64); // SHA256哈希是64个十六进制字符
    }

    [Theory]
    [InlineData("")]
    [InlineData("A")]
    [InlineData("Hello World")]
    [InlineData("你好世界")]
    [InlineData("!@#$%^&*()_+-=[]{}|;':\",./<>?`~")]
    [InlineData("🌟🚀💻🔐🛡️")]
    public void ComputeHash_DifferentInputs_ShouldReturnConsistentHashes(string input)
    {
        // Act
        var hash1 = _hashService.ComputeHash(input);
        var hash2 = _hashService.ComputeHash(input);

        // Assert
        hash1.Should().Be(hash2);
        hash1.Length.Should().Be(64);
        hash1.Should().MatchRegex("^[a-f0-9]{64}$"); // 应该是64个小写十六进制字符
    }

    [Fact]
    public void ComputeHash_DifferentStrings_ShouldReturnDifferentHashes()
    {
        // Arrange
        var input1 = "Hello World";
        var input2 = "Hello world"; // 注意大小写不同

        // Act
        var hash1 = _hashService.ComputeHash(input1);
        var hash2 = _hashService.ComputeHash(input2);

        // Assert
        hash1.Should().NotBe(hash2);
    }

    [Fact]
    public void ComputeHash_LongString_ShouldWorkCorrectly()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.LongText;

        // Act
        var hash = _hashService.ComputeHash(input);

        // Assert
        hash.Should().NotBeNullOrEmpty();
        hash.Length.Should().Be(64);
        hash.Should().MatchRegex("^[a-f0-9]{64}$");
    }

    #endregion

    #region 字节数组哈希测试

    [Fact]
    public void ComputeHash_ValidByteArray_ShouldReturnCorrectHash()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.SimpleBytes;
        var expectedHash = "a591a6d40bf420404a011733cfb7b190d62c65bf0bcda32b57b277d9ad9f146e"; // "Hello World"的SHA256哈希

        // Act
        var hash = _hashService.ComputeHash(input);

        // Assert
        hash.Should().Be(expectedHash);
    }

    [Fact]
    public void ComputeHash_EmptyByteArray_ShouldReturnEmptyStringHash()
    {
        // Arrange
        var input = Array.Empty<byte>();
        var expectedHash = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"; // 空字符串的SHA256哈希

        // Act
        var hash = _hashService.ComputeHash(input);

        // Assert
        hash.Should().Be(expectedHash);
    }

    [Fact]
    public void ComputeHash_StringAndByteArray_ShouldReturnSameHash()
    {
        // Arrange
        var stringInput = CryptoTestHelper.TestData.SimpleText;
        var byteInput = CryptoTestHelper.TestData.SimpleBytes;

        // Act
        var stringHash = _hashService.ComputeHash(stringInput);
        var byteHash = _hashService.ComputeHash(byteInput);

        // Assert
        stringHash.Should().Be(byteHash);
    }

    #endregion

    #region 文件哈希测试

    [Fact]
    public void ComputeFileHash_ValidFile_ShouldReturnCorrectHash()
    {
        // Arrange
        var content = CryptoTestHelper.TestData.SimpleText;
        var tempFile = _fixture.CreateTempFile(content);

        try
        {
            // Act
            var hash = _hashService.ComputeFileHash(tempFile);

            // Assert
            hash.Should().NotBeNullOrEmpty();
            hash.Length.Should().Be(64);
            
            // 验证文件哈希与字符串哈希相同
            var stringHash = _hashService.ComputeHash(content);
            hash.Should().Be(stringHash);
        }
        finally
        {
            // Cleanup is handled by fixture
        }
    }

    [Fact]
    public void ComputeFileHash_EmptyFile_ShouldReturnEmptyStringHash()
    {
        // Arrange
        var tempFile = _fixture.CreateTempFile(string.Empty);
        var expectedHash = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"; // 空字符串的SHA256哈希

        try
        {
            // Act
            var hash = _hashService.ComputeFileHash(tempFile);

            // Assert
            hash.Should().Be(expectedHash);
        }
        finally
        {
            // Cleanup is handled by fixture
        }
    }

    [Fact]
    public void ComputeFileHash_NonExistentFile_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var nonExistentFile = "non-existent-file.txt";

        // Act & Assert
        var action = () => _hashService.ComputeFileHash(nonExistentFile);
        action.Should().Throw<FileNotFoundException>();
    }

    #endregion

    #region 异步操作测试

    [Fact]
    public async Task ComputeHashAsync_ValidString_ShouldReturnCorrectHash()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.SimpleText;
        var expectedHash = "a591a6d40bf420404a011733cfb7b190d62c65bf0bcda32b57b277d9ad9f146e";

        // Act
        var hash = await _hashService.ComputeHashAsync(input);

        // Assert
        hash.Should().Be(expectedHash);
    }

    [Fact]
    public async Task ComputeHashAsync_ValidByteArray_ShouldReturnCorrectHash()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.SimpleBytes;
        var expectedHash = "a591a6d40bf420404a011733cfb7b190d62c65bf0bcda32b57b277d9ad9f146e";

        // Act
        var hash = await _hashService.ComputeHashAsync(input);

        // Assert
        hash.Should().Be(expectedHash);
    }

    [Fact]
    public async Task ComputeFileHashAsync_ValidFile_ShouldReturnCorrectHash()
    {
        // Arrange
        var content = CryptoTestHelper.TestData.SimpleText;
        var tempFile = _fixture.CreateTempFile(content);

        try
        {
            // Act
            var hash = await _hashService.ComputeFileHashAsync(tempFile);

            // Assert
            hash.Should().NotBeNullOrEmpty();
            hash.Length.Should().Be(64);
            
            // 验证与同步方法结果相同
            var syncHash = _hashService.ComputeFileHash(tempFile);
            hash.Should().Be(syncHash);
        }
        finally
        {
            // Cleanup is handled by fixture
        }
    }

    [Fact]
    public async Task ComputeHashAsync_WithCancellation_ShouldRespectCancellationToken()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.SimpleText;
        using var cts = new CancellationTokenSource();

        // Act & Assert
        var hash = await _hashService.ComputeHashAsync(input, cts.Token);
        hash.Should().NotBeNullOrEmpty();
    }

    #endregion

    #region 哈希验证测试

    [Fact]
    public void VerifyHash_ValidStringAndHash_ShouldReturnTrue()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.SimpleText;
        var hash = _hashService.ComputeHash(input);

        // Act
        var isValid = _hashService.VerifyHash(input, hash);

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void VerifyHash_ValidByteArrayAndHash_ShouldReturnTrue()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.SimpleBytes;
        var hash = _hashService.ComputeHash(input);

        // Act
        var isValid = _hashService.VerifyHash(input, hash);

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void VerifyHash_InvalidHash_ShouldReturnFalse()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.SimpleText;
        var invalidHash = "invalid-hash";

        // Act
        var isValid = _hashService.VerifyHash(input, invalidHash);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void VerifyHash_WrongHash_ShouldReturnFalse()
    {
        // Arrange
        var input1 = "Hello World";
        var input2 = "Hello world";
        var hash1 = _hashService.ComputeHash(input1);

        // Act
        var isValid = _hashService.VerifyHash(input2, hash1);

        // Assert
        isValid.Should().BeFalse();
    }

    #endregion

    #region 异常处理测试

    [Fact]
    public void ComputeHash_NullString_ShouldThrowArgumentException()
    {
        // Act & Assert
        var action = () => _hashService.ComputeHash((string)null!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void ComputeHash_NullByteArray_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var action = () => _hashService.ComputeHash((byte[])null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void ComputeFileHash_NullFilePath_ShouldThrowArgumentException()
    {
        // Act & Assert
        var action = () => _hashService.ComputeFileHash(null!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void ComputeFileHash_EmptyFilePath_ShouldThrowArgumentException()
    {
        // Act & Assert
        var action = () => _hashService.ComputeFileHash(string.Empty);
        action.Should().Throw<ArgumentException>();
    }

    #endregion

    #region 性能测试

    [Fact]
    public void ComputeHash_LargeData_ShouldCompleteInReasonableTime()
    {
        // Arrange
        var largeData = new byte[1024 * 1024]; // 1MB数据
        new Random().NextBytes(largeData);

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var hash = _hashService.ComputeHash(largeData);
        stopwatch.Stop();

        // Assert
        hash.Should().NotBeNullOrEmpty();
        hash.Length.Should().Be(64);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // 应该在5秒内完成
    }

    #endregion
}
