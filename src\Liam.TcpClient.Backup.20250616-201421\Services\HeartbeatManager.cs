using Microsoft.Extensions.Logging;
using Liam.TcpClient.Constants;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;

namespace Liam.TcpClient.Services;

/// <summary>
/// 心跳管理器实现
/// </summary>
public class HeartbeatManager : IHeartbeatManager
{
    private readonly ILogger<HeartbeatManager> _logger;
    private readonly IMessageHandler _messageHandler;
    private Timer? _heartbeatTimer;
    private Timer? _timeoutTimer;
    private CancellationTokenSource? _heartbeatCts;
    private DateTime? _lastHeartbeatSentAt;
    private DateTime? _lastResponseReceivedAt;
    private readonly List<double> _responseTimes = new();
    private readonly object _lockObject = new();
    private bool _isEnabled;
    private bool _isRunning;
    private bool _disposed;

    /// <summary>
    /// 是否启用心跳
    /// </summary>
    public bool IsEnabled => _isEnabled;

    /// <summary>
    /// 是否正在运行
    /// </summary>
    public bool IsRunning => _isRunning;

    /// <summary>
    /// 心跳间隔
    /// </summary>
    public TimeSpan Interval { get; set; } = TimeSpan.FromSeconds(TcpClientConstants.Defaults.HeartbeatIntervalSeconds);

    /// <summary>
    /// 心跳超时时间
    /// </summary>
    public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(TcpClientConstants.Defaults.HeartbeatTimeoutSeconds);

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    public DateTime? LastHeartbeatAt => _lastHeartbeatSentAt;

    /// <summary>
    /// 最后响应时间
    /// </summary>
    public DateTime? LastResponseAt => _lastResponseReceivedAt;

    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    public double AverageResponseTime
    {
        get
        {
            lock (_lockObject)
            {
                return _responseTimes.Count > 0 ? _responseTimes.Average() : 0;
            }
        }
    }

    /// <summary>
    /// 心跳事件
    /// </summary>
    public event EventHandler<(DateTime Timestamp, double? ResponseTime)>? HeartbeatSent;

    /// <summary>
    /// 心跳响应事件
    /// </summary>
    public event EventHandler<(DateTime Timestamp, double ResponseTime)>? HeartbeatReceived;

    /// <summary>
    /// 心跳超时事件
    /// </summary>
    public event EventHandler<DateTime>? HeartbeatTimeout;

    /// <summary>
    /// 初始化心跳管理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="messageHandler">消息处理器</param>
    public HeartbeatManager(ILogger<HeartbeatManager> logger, IMessageHandler messageHandler)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _messageHandler = messageHandler ?? throw new ArgumentNullException(nameof(messageHandler));
    }

    /// <summary>
    /// 启动心跳检测
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(HeartbeatManager));
        }

        if (_isRunning)
        {
            _logger.LogWarning("心跳管理器已在运行");
            return;
        }

        _isEnabled = true;
        _isRunning = true;
        _heartbeatCts = new CancellationTokenSource();

        // 启动心跳定时器
        _heartbeatTimer = new Timer(async _ => await SendHeartbeatCallback().ConfigureAwait(false), 
            null, Interval, Interval);

        _logger.LogInformation("心跳管理器已启动，间隔：{Interval}秒，超时：{Timeout}秒", 
            Interval.TotalSeconds, Timeout.TotalSeconds);

        await Task.CompletedTask;
    }

    /// <summary>
    /// 停止心跳检测
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (!_isRunning)
        {
            return;
        }

        _isRunning = false;
        _isEnabled = false;

        // 停止定时器
        if (_heartbeatTimer != null)
        {
            await _heartbeatTimer.DisposeAsync().ConfigureAwait(false);
            _heartbeatTimer = null;
        }

        if (_timeoutTimer != null)
        {
            await _timeoutTimer.DisposeAsync().ConfigureAwait(false);
            _timeoutTimer = null;
        }

        // 取消心跳任务
        _heartbeatCts?.Cancel();
        _heartbeatCts?.Dispose();
        _heartbeatCts = null;

        _logger.LogInformation("心跳管理器已停止");
    }

    /// <summary>
    /// 发送心跳
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendHeartbeatAsync(CancellationToken cancellationToken = default)
    {
        if (!_isEnabled || _heartbeatCts?.Token.IsCancellationRequested == true)
        {
            return false;
        }

        try
        {
            var heartbeatMessage = TcpMessage.CreateHeartbeatRequest();
            var sendTime = DateTime.UtcNow;
            
            var success = await _messageHandler.SendMessageAsync(heartbeatMessage, cancellationToken).ConfigureAwait(false);
            
            if (success)
            {
                _lastHeartbeatSentAt = sendTime;
                
                // 启动超时定时器
                StartTimeoutTimer(sendTime);
                
                _logger.LogDebug("发送心跳请求");
                HeartbeatSent?.Invoke(this, (sendTime, null));
            }
            else
            {
                _logger.LogWarning("发送心跳请求失败");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送心跳时发生错误");
            return false;
        }
    }

    /// <summary>
    /// 处理心跳响应
    /// </summary>
    /// <param name="message">心跳消息</param>
    public void HandleHeartbeatResponse(TcpMessage message)
    {
        ArgumentNullException.ThrowIfNull(message);

        if (message.MessageType != TcpClientConstants.MessageTypes.HeartbeatResponse)
        {
            return;
        }

        var responseTime = DateTime.UtcNow;
        _lastResponseReceivedAt = responseTime;

        // 停止超时定时器
        StopTimeoutTimer();

        // 计算响应时间
        double? latency = null;
        if (_lastHeartbeatSentAt.HasValue)
        {
            latency = (responseTime - _lastHeartbeatSentAt.Value).TotalMilliseconds;
            
            lock (_lockObject)
            {
                _responseTimes.Add(latency.Value);
                
                // 保持最近100个响应时间记录
                if (_responseTimes.Count > 100)
                {
                    _responseTimes.RemoveAt(0);
                }
            }
        }

        _logger.LogDebug("接收到心跳响应，延迟：{Latency}ms", latency?.ToString("F2") ?? "N/A");
        
        if (latency.HasValue)
        {
            HeartbeatReceived?.Invoke(this, (responseTime, latency.Value));
        }
    }

    /// <summary>
    /// 重置心跳统计
    /// </summary>
    public void ResetStatistics()
    {
        lock (_lockObject)
        {
            _responseTimes.Clear();
        }
        
        _lastHeartbeatSentAt = null;
        _lastResponseReceivedAt = null;
        
        _logger.LogDebug("心跳统计已重置");
    }

    /// <summary>
    /// 心跳定时器回调
    /// </summary>
    private async Task SendHeartbeatCallback()
    {
        if (!_isEnabled || _heartbeatCts?.Token.IsCancellationRequested == true)
        {
            return;
        }

        try
        {
            await SendHeartbeatAsync(_heartbeatCts?.Token ?? CancellationToken.None).ConfigureAwait(false);
        }
        catch (OperationCanceledException)
        {
            // 预期的取消异常
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "心跳定时器回调时发生错误");
        }
    }

    /// <summary>
    /// 启动超时定时器
    /// </summary>
    /// <param name="sendTime">发送时间</param>
    private void StartTimeoutTimer(DateTime sendTime)
    {
        StopTimeoutTimer();
        
        _timeoutTimer = new Timer(_ => HandleHeartbeatTimeout(sendTime), 
            null, Timeout, System.Threading.Timeout.InfiniteTimeSpan);
    }

    /// <summary>
    /// 停止超时定时器
    /// </summary>
    private void StopTimeoutTimer()
    {
        if (_timeoutTimer != null)
        {
            _timeoutTimer.Dispose();
            _timeoutTimer = null;
        }
    }

    /// <summary>
    /// 处理心跳超时
    /// </summary>
    /// <param name="sendTime">发送时间</param>
    private void HandleHeartbeatTimeout(DateTime sendTime)
    {
        if (_lastResponseReceivedAt.HasValue && _lastResponseReceivedAt.Value > sendTime)
        {
            // 已经收到响应，不是超时
            return;
        }

        var timeoutTime = DateTime.UtcNow;
        _logger.LogWarning("心跳超时，发送时间：{SendTime}，超时时间：{TimeoutTime}", 
            sendTime.ToString("HH:mm:ss.fff"), timeoutTime.ToString("HH:mm:ss.fff"));
        
        HeartbeatTimeout?.Invoke(this, timeoutTime);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        try
        {
            StopAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "释放心跳管理器时发生错误");
        }

        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        try
        {
            await StopAsync().ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "异步释放心跳管理器时发生错误");
        }

        GC.SuppressFinalize(this);
    }
}
