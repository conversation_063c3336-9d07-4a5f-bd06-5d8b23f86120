using Liam.Logging.Constants;

namespace Liam.Logging.Models;

/// <summary>
/// 日志配置类
/// </summary>
public class LogConfiguration
{
    /// <summary>
    /// 最小日志级别
    /// </summary>
    public LogLevel MinimumLevel { get; set; } = LogLevel.Information;

    /// <summary>
    /// 是否启用异步日志记录
    /// </summary>
    public bool EnableAsync { get; set; } = true;

    /// <summary>
    /// 异步队列大小
    /// </summary>
    public int AsyncQueueSize { get; set; } = 10000;

    /// <summary>
    /// 批处理大小
    /// </summary>
    public int BatchSize { get; set; } = 100;

    /// <summary>
    /// 批处理超时时间（毫秒）
    /// </summary>
    public int BatchTimeoutMs { get; set; } = 1000;

    /// <summary>
    /// 是否包含作用域信息
    /// </summary>
    public bool IncludeScopes { get; set; } = true;

    /// <summary>
    /// 是否包含异常详细信息
    /// </summary>
    public bool IncludeExceptionDetails { get; set; } = true;

    /// <summary>
    /// 是否包含源代码信息
    /// </summary>
    public bool IncludeSourceInfo { get; set; } = false;

    /// <summary>
    /// 应用程序名称
    /// </summary>
    public string? ApplicationName { get; set; }

    /// <summary>
    /// 环境名称
    /// </summary>
    public string? Environment { get; set; }

    /// <summary>
    /// 自定义属性
    /// </summary>
    public Dictionary<string, object?> GlobalProperties { get; set; } = new();

    /// <summary>
    /// 日志提供程序配置
    /// </summary>
    public List<LogProviderConfiguration> Providers { get; set; } = new();

    /// <summary>
    /// 日志过滤器配置
    /// </summary>
    public List<LogFilterConfiguration> Filters { get; set; } = new();
}

/// <summary>
/// 日志提供程序配置
/// </summary>
public class LogProviderConfiguration
{
    /// <summary>
    /// 提供程序类型名称
    /// </summary>
    public string TypeName { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 最小日志级别
    /// </summary>
    public LogLevel? MinimumLevel { get; set; }

    /// <summary>
    /// 配置参数
    /// </summary>
    public Dictionary<string, object?> Settings { get; set; } = new();
}

/// <summary>
/// 日志过滤器配置
/// </summary>
public class LogFilterConfiguration
{
    /// <summary>
    /// 过滤器类型
    /// </summary>
    public LogFilterType Type { get; set; }

    /// <summary>
    /// 过滤条件
    /// </summary>
    public string Condition { get; set; } = string.Empty;

    /// <summary>
    /// 过滤动作
    /// </summary>
    public LogFilterAction Action { get; set; } = LogFilterAction.Include;

    /// <summary>
    /// 配置参数
    /// </summary>
    public Dictionary<string, object?> Settings { get; set; } = new();
}

/// <summary>
/// 日志过滤器类型
/// </summary>
public enum LogFilterType
{
    /// <summary>
    /// 按级别过滤
    /// </summary>
    Level,

    /// <summary>
    /// 按类别过滤
    /// </summary>
    Category,

    /// <summary>
    /// 按消息内容过滤
    /// </summary>
    Message,

    /// <summary>
    /// 按属性过滤
    /// </summary>
    Property,

    /// <summary>
    /// 自定义过滤器
    /// </summary>
    Custom
}

/// <summary>
/// 日志过滤动作
/// </summary>
public enum LogFilterAction
{
    /// <summary>
    /// 包含（记录日志）
    /// </summary>
    Include,

    /// <summary>
    /// 排除（不记录日志）
    /// </summary>
    Exclude
}

/// <summary>
/// 文件日志配置
/// </summary>
public class FileLogConfiguration : LogProviderConfiguration
{
    /// <summary>
    /// 日志文件路径
    /// </summary>
    public string FilePath { get; set; } = "logs/app.log";

    /// <summary>
    /// 是否启用日志轮转
    /// </summary>
    public bool EnableRotation { get; set; } = true;

    /// <summary>
    /// 最大文件大小（字节）
    /// </summary>
    public long MaxFileSize { get; set; } = 10 * 1024 * 1024; // 10MB

    /// <summary>
    /// 保留文件数量
    /// </summary>
    public int RetainedFileCount { get; set; } = 10;

    /// <summary>
    /// 日志轮转间隔
    /// </summary>
    public TimeSpan? RotationInterval { get; set; } = TimeSpan.FromDays(1);

    /// <summary>
    /// 文件编码
    /// </summary>
    public string Encoding { get; set; } = "UTF-8";

    /// <summary>
    /// 是否自动刷新
    /// </summary>
    public bool AutoFlush { get; set; } = true;

    /// <summary>
    /// 缓冲区大小
    /// </summary>
    public int BufferSize { get; set; } = 4096;
}

/// <summary>
/// 控制台日志配置
/// </summary>
public class ConsoleLogConfiguration : LogProviderConfiguration
{
    /// <summary>
    /// 是否启用颜色输出
    /// </summary>
    public bool EnableColors { get; set; } = true;

    /// <summary>
    /// 是否输出到标准错误流
    /// </summary>
    public bool UseStandardError { get; set; } = false;

    /// <summary>
    /// 时间戳格式
    /// </summary>
    public string TimestampFormat { get; set; } = "yyyy-MM-dd HH:mm:ss.fff";
}
