using Liam.Logging.Constants;
using Liam.Logging.Interfaces;
using Liam.Logging.Models;
using System.Text;
using System.Text.Json;

namespace Liam.Logging.Formatters;

/// <summary>
/// 文本日志格式化器
/// </summary>
public class TextLogFormatter : ILogFormatter
{
    private readonly string _timestampFormat;
    private readonly bool _includeScopes;
    private readonly bool _includeExceptionDetails;
    private readonly bool _includeSourceInfo;

    /// <summary>
    /// 格式化器名称
    /// </summary>
    public string Name => "Text";

    /// <summary>
    /// 初始化文本日志格式化器
    /// </summary>
    /// <param name="timestampFormat">时间戳格式</param>
    /// <param name="includeScopes">是否包含作用域信息</param>
    /// <param name="includeExceptionDetails">是否包含异常详细信息</param>
    /// <param name="includeSourceInfo">是否包含源代码信息</param>
    public TextLogFormatter(
        string timestampFormat = "yyyy-MM-dd HH:mm:ss.fff",
        bool includeScopes = true,
        bool includeExceptionDetails = true,
        bool includeSourceInfo = false)
    {
        _timestampFormat = timestampFormat;
        _includeScopes = includeScopes;
        _includeExceptionDetails = includeExceptionDetails;
        _includeSourceInfo = includeSourceInfo;
    }

    /// <summary>
    /// 格式化日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>格式化后的字符串</returns>
    public string Format(LogEvent logEvent)
    {
        var sb = new StringBuilder();

        // 时间戳
        sb.Append(logEvent.Timestamp.ToString(_timestampFormat));
        sb.Append(' ');

        // 日志级别
        sb.Append('[');
        sb.Append(LogLevelConstants.GetLevelName(logEvent.Level).PadRight(5));
        sb.Append(']');
        sb.Append(' ');

        // 类别
        if (!string.IsNullOrEmpty(logEvent.Category))
        {
            sb.Append(logEvent.Category);
            sb.Append(' ');
        }

        // 事件ID
        if (logEvent.EventId != 0)
        {
            sb.Append('(');
            sb.Append(logEvent.EventId);
            if (!string.IsNullOrEmpty(logEvent.EventName))
            {
                sb.Append(':');
                sb.Append(logEvent.EventName);
            }
            sb.Append(')');
            sb.Append(' ');
        }

        // 线程ID
        sb.Append("[T:");
        sb.Append(logEvent.ThreadId);
        sb.Append("] ");

        // 源代码信息
        if (_includeSourceInfo && logEvent.Source != null)
        {
            if (!string.IsNullOrEmpty(logEvent.Source.MethodName))
            {
                sb.Append('[');
                if (!string.IsNullOrEmpty(logEvent.Source.ClassName))
                {
                    sb.Append(logEvent.Source.ClassName);
                    sb.Append('.');
                }
                sb.Append(logEvent.Source.MethodName);
                if (logEvent.Source.LineNumber.HasValue)
                {
                    sb.Append(':');
                    sb.Append(logEvent.Source.LineNumber.Value);
                }
                sb.Append("] ");
            }
        }

        // 日志消息
        sb.Append(logEvent.Message);

        // 作用域信息
        if (_includeScopes && logEvent.Scopes.Count > 0)
        {
            sb.AppendLine();
            sb.Append("  Scopes: ");
            var scopeItems = logEvent.Scopes.Select(kvp => $"{kvp.Key}={kvp.Value}");
            sb.Append(string.Join(", ", scopeItems));
        }

        // 自定义属性
        if (logEvent.Properties.Count > 0)
        {
            sb.AppendLine();
            sb.Append("  Properties: ");
            var propertyItems = logEvent.Properties.Select(kvp => $"{kvp.Key}={kvp.Value}");
            sb.Append(string.Join(", ", propertyItems));
        }

        // 异常信息
        if (_includeExceptionDetails && logEvent.Exception != null)
        {
            sb.AppendLine();
            sb.Append("  Exception: ");
            sb.Append(FormatException(logEvent.Exception));
        }

        return sb.ToString();
    }

    /// <summary>
    /// 格式化日志事件为字节数组
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>格式化后的字节数组</returns>
    public byte[] FormatToBytes(LogEvent logEvent)
    {
        var text = Format(logEvent);
        return Encoding.UTF8.GetBytes(text);
    }

    /// <summary>
    /// 格式化异常信息
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <returns>格式化后的异常信息</returns>
    private static string FormatException(Exception exception)
    {
        var sb = new StringBuilder();
        var currentException = exception;
        var level = 0;

        while (currentException != null)
        {
            if (level > 0)
            {
                sb.AppendLine();
                sb.Append("  ");
                sb.Append(new string(' ', level * 2));
                sb.Append("---> ");
            }

            sb.Append(currentException.GetType().FullName);
            sb.Append(": ");
            sb.Append(currentException.Message);

            if (!string.IsNullOrEmpty(currentException.StackTrace))
            {
                sb.AppendLine();
                sb.Append("  ");
                sb.Append(new string(' ', level * 2));
                sb.Append("Stack Trace:");
                sb.AppendLine();
                
                var stackLines = currentException.StackTrace.Split('\n');
                foreach (var line in stackLines)
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        sb.Append("  ");
                        sb.Append(new string(' ', level * 2));
                        sb.Append(line.Trim());
                        sb.AppendLine();
                    }
                }
            }

            currentException = currentException.InnerException;
            level++;
        }

        return sb.ToString();
    }
}

/// <summary>
/// JSON日志格式化器
/// </summary>
public class JsonLogFormatter : ILogFormatter
{
    private readonly JsonSerializerOptions _jsonOptions;

    /// <summary>
    /// 格式化器名称
    /// </summary>
    public string Name => "Json";

    /// <summary>
    /// 初始化JSON日志格式化器
    /// </summary>
    /// <param name="jsonOptions">JSON序列化选项</param>
    public JsonLogFormatter(JsonSerializerOptions? jsonOptions = null)
    {
        _jsonOptions = jsonOptions ?? new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };
    }

    /// <summary>
    /// 格式化日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>格式化后的字符串</returns>
    public string Format(LogEvent logEvent)
    {
        var jsonObject = new Dictionary<string, object?>
        {
            ["id"] = logEvent.Id,
            ["timestamp"] = logEvent.Timestamp,
            ["level"] = LogLevelConstants.GetLevelName(logEvent.Level),
            ["message"] = logEvent.Message,
            ["category"] = logEvent.Category,
            ["threadId"] = logEvent.ThreadId,
            ["processId"] = logEvent.ProcessId,
            ["machineName"] = logEvent.MachineName
        };

        if (!string.IsNullOrEmpty(logEvent.MessageTemplate))
        {
            jsonObject["messageTemplate"] = logEvent.MessageTemplate;
        }

        if (logEvent.Parameters?.Length > 0)
        {
            jsonObject["parameters"] = logEvent.Parameters;
        }

        if (logEvent.EventId != 0)
        {
            jsonObject["eventId"] = logEvent.EventId;
        }

        if (!string.IsNullOrEmpty(logEvent.EventName))
        {
            jsonObject["eventName"] = logEvent.EventName;
        }

        if (!string.IsNullOrEmpty(logEvent.UserName))
        {
            jsonObject["userName"] = logEvent.UserName;
        }

        if (!string.IsNullOrEmpty(logEvent.ApplicationName))
        {
            jsonObject["applicationName"] = logEvent.ApplicationName;
        }

        if (logEvent.Scopes.Count > 0)
        {
            jsonObject["scopes"] = logEvent.Scopes;
        }

        if (logEvent.Properties.Count > 0)
        {
            jsonObject["properties"] = logEvent.Properties;
        }

        if (logEvent.Source != null)
        {
            jsonObject["source"] = new Dictionary<string, object?>
            {
                ["className"] = logEvent.Source.ClassName,
                ["methodName"] = logEvent.Source.MethodName,
                ["filePath"] = logEvent.Source.FilePath,
                ["lineNumber"] = logEvent.Source.LineNumber,
                ["namespace"] = logEvent.Source.Namespace
            };
        }

        if (logEvent.Exception != null)
        {
            jsonObject["exception"] = FormatExceptionToObject(logEvent.Exception);
        }

        return JsonSerializer.Serialize(jsonObject, _jsonOptions);
    }

    /// <summary>
    /// 格式化日志事件为字节数组
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>格式化后的字节数组</returns>
    public byte[] FormatToBytes(LogEvent logEvent)
    {
        var json = Format(logEvent);
        return Encoding.UTF8.GetBytes(json);
    }

    /// <summary>
    /// 将异常转换为对象
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <returns>异常对象字典</returns>
    private static Dictionary<string, object?> FormatExceptionToObject(Exception exception)
    {
        var result = new Dictionary<string, object?>
        {
            ["type"] = exception.GetType().FullName,
            ["message"] = exception.Message,
            ["stackTrace"] = exception.StackTrace
        };

        if (exception.Data.Count > 0)
        {
            var data = new Dictionary<string, object?>();
            foreach (var key in exception.Data.Keys)
            {
                data[key.ToString() ?? ""] = exception.Data[key];
            }
            result["data"] = data;
        }

        if (exception.InnerException != null)
        {
            result["innerException"] = FormatExceptionToObject(exception.InnerException);
        }

        return result;
    }
}
