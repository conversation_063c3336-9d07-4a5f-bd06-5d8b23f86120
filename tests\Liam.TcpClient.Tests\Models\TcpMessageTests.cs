using FluentAssertions;
using Liam.TcpClient.Constants;
using Liam.TcpClient.Models;
using System.Text;
using Xunit;

namespace Liam.TcpClient.Tests.Models;

public class TcpMessageTests
{
    [Fact]
    public void Constructor_WithByteArray_ShouldCreateMessage()
    {
        // Arrange
        var messageType = TcpClientConstants.MessageTypes.Data;
        var data = Encoding.UTF8.GetBytes("Hello World");

        // Act
        var message = new TcpMessage(messageType, data);

        // Assert
        message.MessageType.Should().Be(messageType);
        message.Data.Should().BeEquivalentTo(data);
        message.Length.Should().Be(data.Length);
        message.Id.Should().NotBeNullOrEmpty();
        message.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        message.Properties.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_WithText_ShouldCreateMessage()
    {
        // Arrange
        var messageType = TcpClientConstants.MessageTypes.Data;
        var text = "Hello World";
        var expectedData = Encoding.UTF8.GetBytes(text);

        // Act
        var message = new TcpMessage(messageType, text);

        // Assert
        message.MessageType.Should().Be(messageType);
        message.Data.Should().BeEquivalentTo(expectedData);
        message.Length.Should().Be(expectedData.Length);
    }

    [Fact]
    public void GetText_ShouldReturnCorrectText()
    {
        // Arrange
        var text = "Hello World";
        var message = new TcpMessage(TcpClientConstants.MessageTypes.Data, text);

        // Act
        var result = message.GetText();

        // Assert
        result.Should().Be(text);
    }

    [Fact]
    public void SetText_ShouldUpdateData()
    {
        // Arrange
        var message = new TcpMessage(TcpClientConstants.MessageTypes.Data, "Initial");
        var newText = "Updated Text";

        // Act
        message.SetText(newText);

        // Assert
        message.GetText().Should().Be(newText);
        message.Data.Should().BeEquivalentTo(Encoding.UTF8.GetBytes(newText));
    }

    [Fact]
    public void Properties_ShouldWorkCorrectly()
    {
        // Arrange
        var message = new TcpMessage(TcpClientConstants.MessageTypes.Data, "test");
        var key = "TestKey";
        var value = "TestValue";

        // Act
        message.SetProperty(key, value);
        var retrievedValue = message.GetProperty<string>(key);
        var hasProperty = message.RemoveProperty(key);
        var removedValue = message.GetProperty<string>(key);

        // Assert
        retrievedValue.Should().Be(value);
        hasProperty.Should().BeTrue();
        removedValue.Should().BeNull();
    }

    [Fact]
    public void IsHeartbeat_ShouldReturnCorrectValue()
    {
        // Arrange & Act
        var heartbeatRequest = new TcpMessage(TcpClientConstants.MessageTypes.HeartbeatRequest, Array.Empty<byte>());
        var heartbeatResponse = new TcpMessage(TcpClientConstants.MessageTypes.HeartbeatResponse, Array.Empty<byte>());
        var dataMessage = new TcpMessage(TcpClientConstants.MessageTypes.Data, "test");

        // Assert
        heartbeatRequest.IsHeartbeat.Should().BeTrue();
        heartbeatResponse.IsHeartbeat.Should().BeTrue();
        dataMessage.IsHeartbeat.Should().BeFalse();
    }

    [Fact]
    public void IsControlMessage_ShouldReturnCorrectValue()
    {
        // Arrange & Act
        var dataMessage = new TcpMessage(TcpClientConstants.MessageTypes.Data, "test");
        var controlMessage = new TcpMessage(TcpClientConstants.MessageTypes.HeartbeatRequest, Array.Empty<byte>());

        // Assert
        dataMessage.IsControlMessage.Should().BeFalse();
        controlMessage.IsControlMessage.Should().BeTrue();
    }

    [Fact]
    public void Serialize_ShouldCreateCorrectByteArray()
    {
        // Arrange
        var messageType = TcpClientConstants.MessageTypes.Data;
        var text = "Hello";
        var message = new TcpMessage(messageType, text);

        // Act
        var serialized = message.Serialize();

        // Assert
        serialized.Should().NotBeNull();
        serialized.Length.Should().Be(1 + 4 + text.Length); // 1 byte type + 4 bytes length + data
        serialized[0].Should().Be(messageType);
        
        var length = BitConverter.ToInt32(serialized, 1);
        length.Should().Be(text.Length);
        
        var data = new byte[length];
        Array.Copy(serialized, 5, data, 0, length);
        Encoding.UTF8.GetString(data).Should().Be(text);
    }

    [Fact]
    public void Deserialize_ShouldCreateCorrectMessage()
    {
        // Arrange
        var originalMessage = new TcpMessage(TcpClientConstants.MessageTypes.Data, "Hello World");
        var serialized = originalMessage.Serialize();

        // Act
        var deserializedMessage = TcpMessage.Deserialize(serialized);

        // Assert
        deserializedMessage.MessageType.Should().Be(originalMessage.MessageType);
        deserializedMessage.Data.Should().BeEquivalentTo(originalMessage.Data);
        deserializedMessage.Length.Should().Be(originalMessage.Length);
        deserializedMessage.GetText().Should().Be(originalMessage.GetText());
    }

    [Fact]
    public void TryDeserialize_WithValidData_ShouldReturnTrue()
    {
        // Arrange
        var originalMessage = new TcpMessage(TcpClientConstants.MessageTypes.Data, "Test");
        var serialized = originalMessage.Serialize();

        // Act
        var success = TcpMessage.TryDeserialize(serialized, out var message);

        // Assert
        success.Should().BeTrue();
        message.Should().NotBeNull();
        message!.GetText().Should().Be("Test");
    }

    [Fact]
    public void TryDeserialize_WithInvalidData_ShouldReturnFalse()
    {
        // Arrange
        var invalidData = new byte[] { 1, 2, 3 }; // Too short

        // Act
        var success = TcpMessage.TryDeserialize(invalidData, out var message);

        // Assert
        success.Should().BeFalse();
        message.Should().BeNull();
    }

    [Theory]
    [InlineData(null)]
    [InlineData(new byte[0])]
    [InlineData(new byte[] { 1, 2, 3, 4 })] // Too short
    public void Deserialize_WithInvalidData_ShouldThrowException(byte[]? data)
    {
        // Act & Assert
        if (data == null)
        {
            Assert.Throws<ArgumentNullException>(() => TcpMessage.Deserialize(data!));
        }
        else
        {
            Assert.Throws<ArgumentException>(() => TcpMessage.Deserialize(data));
        }
    }

    [Fact]
    public void CreateDataMessage_ShouldCreateCorrectMessage()
    {
        // Arrange
        var data = Encoding.UTF8.GetBytes("Test Data");

        // Act
        var message = TcpMessage.CreateDataMessage(data);

        // Assert
        message.MessageType.Should().Be(TcpClientConstants.MessageTypes.Data);
        message.Data.Should().BeEquivalentTo(data);
    }

    [Fact]
    public void CreateTextMessage_ShouldCreateCorrectMessage()
    {
        // Arrange
        var text = "Test Text";

        // Act
        var message = TcpMessage.CreateTextMessage(text);

        // Assert
        message.MessageType.Should().Be(TcpClientConstants.MessageTypes.Data);
        message.GetText().Should().Be(text);
    }

    [Fact]
    public void CreateHeartbeatRequest_ShouldCreateCorrectMessage()
    {
        // Act
        var message = TcpMessage.CreateHeartbeatRequest();

        // Assert
        message.MessageType.Should().Be(TcpClientConstants.MessageTypes.HeartbeatRequest);
        message.Data.Should().BeEmpty();
        message.IsHeartbeat.Should().BeTrue();
    }

    [Fact]
    public void CreateHeartbeatResponse_ShouldCreateCorrectMessage()
    {
        // Act
        var message = TcpMessage.CreateHeartbeatResponse();

        // Assert
        message.MessageType.Should().Be(TcpClientConstants.MessageTypes.HeartbeatResponse);
        message.Data.Should().BeEmpty();
        message.IsHeartbeat.Should().BeTrue();
    }

    [Fact]
    public void CreateConnectionAck_ShouldCreateCorrectMessage()
    {
        // Act
        var message = TcpMessage.CreateConnectionAck();

        // Assert
        message.MessageType.Should().Be(TcpClientConstants.MessageTypes.ConnectionAck);
        message.Data.Should().BeEmpty();
    }

    [Fact]
    public void CreateDisconnectMessage_ShouldCreateCorrectMessage()
    {
        // Arrange
        var reason = "Test disconnect";

        // Act
        var message = TcpMessage.CreateDisconnectMessage(reason);

        // Assert
        message.MessageType.Should().Be(TcpClientConstants.MessageTypes.Disconnect);
        message.GetText().Should().Be(reason);
    }

    [Fact]
    public void CreateErrorMessage_ShouldCreateCorrectMessage()
    {
        // Arrange
        var error = "Test error";

        // Act
        var message = TcpMessage.CreateErrorMessage(error);

        // Assert
        message.MessageType.Should().Be(TcpClientConstants.MessageTypes.Error);
        message.GetText().Should().Be(error);
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var message = TcpMessage.CreateTextMessage("Test");

        // Act
        var result = message.ToString();

        // Assert
        result.Should().Contain("TcpMessage");
        result.Should().Contain("Type:Data");
        result.Should().Contain($"Length:{message.Length}");
        result.Should().Contain("Created:");
    }
}
