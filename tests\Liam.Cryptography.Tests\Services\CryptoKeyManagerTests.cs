using Liam.Cryptography.Services;
using Liam.Cryptography.Exceptions;
using Liam.Cryptography.Tests.Fixtures;
using Liam.Cryptography.Tests.TestHelpers;

namespace Liam.Cryptography.Tests.Services;

/// <summary>
/// 密钥管理服务测试
/// </summary>
[Collection("Crypto Tests")]
public class CryptoKeyManagerTests
{
    private readonly CryptoTestFixture _fixture;
    private readonly CryptoKeyManager _keyManager;

    public CryptoKeyManagerTests(CryptoTestFixture fixture)
    {
        _fixture = fixture;
        _keyManager = _fixture.KeyManagerService;
    }

    #region 对称密钥生成测试

    [Theory]
    [InlineData(128)]
    [InlineData(192)]
    [InlineData(256)]
    public void GenerateSymmetricKey_ValidKeySize_ShouldReturnCorrectLength(int keySize)
    {
        // Act
        var key = _keyManager.GenerateSymmetricKey(keySize);

        // Assert
        key.Should().NotBeNull();
        key.Length.Should().Be(keySize / 8);
    }

    [Theory]
    [InlineData(64)]
    [InlineData(512)]
    [InlineData(0)]
    [InlineData(-1)]
    public void GenerateSymmetricKey_InvalidKeySize_ShouldThrowException(int keySize)
    {
        // Act & Assert
        var action = () => _keyManager.GenerateSymmetricKey(keySize);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void GenerateSymmetricKey_MultipleCalls_ShouldReturnDifferentKeys()
    {
        // Act
        var key1 = _keyManager.GenerateSymmetricKey(256);
        var key2 = _keyManager.GenerateSymmetricKey(256);

        // Assert
        key1.Should().NotEqual(key2);
    }

    #endregion

    #region 非对称密钥对生成测试

    [Theory]
    [InlineData(1024)]
    [InlineData(2048)]
    [InlineData(3072)]
    [InlineData(4096)]
    public void GenerateAsymmetricKeyPair_ValidKeySize_ShouldReturnValidKeyPair(int keySize)
    {
        // Act
        var keyPair = _keyManager.GenerateAsymmetricKeyPair(keySize);

        // Assert
        keyPair.Should().NotBeNull();
        keyPair.PublicKey.Should().NotBeNullOrEmpty();
        keyPair.PrivateKey.Should().NotBeNullOrEmpty();
        keyPair.KeySize.Should().Be(keySize);
        keyPair.IsValid().Should().BeTrue();
    }

    [Theory]
    [InlineData(512)]
    [InlineData(8192)]
    [InlineData(0)]
    [InlineData(-1)]
    public void GenerateAsymmetricKeyPair_InvalidKeySize_ShouldThrowException(int keySize)
    {
        // Act & Assert
        var action = () => _keyManager.GenerateAsymmetricKeyPair(keySize);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void GenerateAsymmetricKeyPair_MultipleCalls_ShouldReturnDifferentKeyPairs()
    {
        // Act
        var keyPair1 = _keyManager.GenerateAsymmetricKeyPair(2048);
        var keyPair2 = _keyManager.GenerateAsymmetricKeyPair(2048);

        // Assert
        keyPair1.PublicKey.Should().NotBe(keyPair2.PublicKey!);
        keyPair1.PrivateKey.Should().NotBe(keyPair2.PrivateKey!);
    }

    #endregion

    #region 对称密钥导出导入测试

    [Fact]
    public void ExportImportKey_WithPassword_ShouldWorkCorrectly()
    {
        // Arrange
        var originalKey = _keyManager.GenerateSymmetricKey(256);
        var password = "test-password-123";
        var tempFile = Path.GetTempFileName();

        try
        {
            // Act
            _keyManager.ExportKey(originalKey, tempFile, password);
            var importedKey = _keyManager.ImportKey(tempFile, password);

            // Assert
            importedKey.Should().Equal(originalKey);
        }
        finally
        {
            CryptoTestHelper.CleanupTempFile(tempFile);
        }
    }

    [Fact]
    public void ExportImportKey_WithoutPassword_ShouldWorkCorrectly()
    {
        // Arrange
        var originalKey = _keyManager.GenerateSymmetricKey(256);
        var tempFile = Path.GetTempFileName();

        try
        {
            // Act
            _keyManager.ExportKey(originalKey, tempFile);
            var importedKey = _keyManager.ImportKey(tempFile);

            // Assert
            importedKey.Should().Equal(originalKey);
        }
        finally
        {
            CryptoTestHelper.CleanupTempFile(tempFile);
        }
    }

    [Fact]
    public void ImportKey_WrongPassword_ShouldThrowCryptographyException()
    {
        // Arrange
        var originalKey = _keyManager.GenerateSymmetricKey(256);
        var correctPassword = "correct-password";
        var wrongPassword = "wrong-password";
        var tempFile = Path.GetTempFileName();

        try
        {
            // Act
            _keyManager.ExportKey(originalKey, tempFile, correctPassword);

            // Assert
            var action = () => _keyManager.ImportKey(tempFile, wrongPassword);
            action.Should().Throw<CryptographyException>();
        }
        finally
        {
            CryptoTestHelper.CleanupTempFile(tempFile);
        }
    }

    [Fact]
    public void ImportKey_NonExistentFile_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var nonExistentFile = "non-existent-key-file.key";

        // Act & Assert
        var action = () => _keyManager.ImportKey(nonExistentFile);
        action.Should().Throw<FileNotFoundException>();
    }

    #endregion

    #region 非对称密钥对导出导入测试

    [Fact]
    public void ExportImportKeyPair_WithPassword_ShouldWorkCorrectly()
    {
        // Arrange
        var originalKeyPair = _keyManager.GenerateAsymmetricKeyPair(2048);
        var password = "test-password-123";
        var privateKeyFile = Path.GetTempFileName();
        var publicKeyFile = Path.GetTempFileName();

        try
        {
            // Act
            _keyManager.ExportKeyPair(originalKeyPair, privateKeyFile, publicKeyFile, password);
            var importedPrivateKey = _keyManager.ImportPrivateKey(privateKeyFile, password);
            var importedPublicKey = _keyManager.ImportPublicKey(publicKeyFile);

            // Assert
            importedPrivateKey.Should().Be(originalKeyPair.PrivateKey!);
            importedPublicKey.Should().Be(originalKeyPair.PublicKey!);
        }
        finally
        {
            CryptoTestHelper.CleanupTempFile(privateKeyFile);
            CryptoTestHelper.CleanupTempFile(publicKeyFile);
        }
    }

    [Fact]
    public void ExportImportKeyPair_WithoutPassword_ShouldWorkCorrectly()
    {
        // Arrange
        var originalKeyPair = _keyManager.GenerateAsymmetricKeyPair(2048);
        var privateKeyFile = Path.GetTempFileName();
        var publicKeyFile = Path.GetTempFileName();

        try
        {
            // Act
            _keyManager.ExportKeyPair(originalKeyPair, privateKeyFile, publicKeyFile);
            var importedPrivateKey = _keyManager.ImportPrivateKey(privateKeyFile);
            var importedPublicKey = _keyManager.ImportPublicKey(publicKeyFile);

            // Assert
            importedPrivateKey.Should().Be(originalKeyPair.PrivateKey!);
            importedPublicKey.Should().Be(originalKeyPair.PublicKey!);
        }
        finally
        {
            CryptoTestHelper.CleanupTempFile(privateKeyFile);
            CryptoTestHelper.CleanupTempFile(publicKeyFile);
        }
    }

    [Fact]
    public void ImportPrivateKey_WrongPassword_ShouldThrowCryptographyException()
    {
        // Arrange
        var originalKeyPair = _keyManager.GenerateAsymmetricKeyPair(2048);
        var correctPassword = "correct-password";
        var wrongPassword = "wrong-password";
        var privateKeyFile = Path.GetTempFileName();
        var publicKeyFile = Path.GetTempFileName();

        try
        {
            // Act
            _keyManager.ExportKeyPair(originalKeyPair, privateKeyFile, publicKeyFile, correctPassword);

            // Assert
            var action = () => _keyManager.ImportPrivateKey(privateKeyFile, wrongPassword);
            action.Should().Throw<CryptographyException>();
        }
        finally
        {
            CryptoTestHelper.CleanupTempFile(privateKeyFile);
            CryptoTestHelper.CleanupTempFile(publicKeyFile);
        }
    }

    #endregion

    #region 密钥验证测试

    [Fact]
    public void ValidateKey_ValidSymmetricKey_ShouldReturnTrue()
    {
        // Arrange
        var validKey = _keyManager.GenerateSymmetricKey(256);
        var keyString = Convert.ToBase64String(validKey);

        // Act
        var isValid = _keyManager.ValidateKey(keyString, Liam.Cryptography.Models.KeyType.Symmetric);

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void ValidateKey_ValidPublicKey_ShouldReturnTrue()
    {
        // Arrange
        var validKeyPair = _keyManager.GenerateAsymmetricKeyPair(2048);

        // Act
        var isValid = _keyManager.ValidateKey(validKeyPair.PublicKey, Liam.Cryptography.Models.KeyType.AsymmetricPublic);

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void ValidateKey_ValidPrivateKey_ShouldReturnTrue()
    {
        // Arrange
        var validKeyPair = _keyManager.GenerateAsymmetricKeyPair(2048);

        // Act
        var isValid = _keyManager.ValidateKey(validKeyPair.PrivateKey, Liam.Cryptography.Models.KeyType.AsymmetricPrivate);

        // Assert
        isValid.Should().BeTrue();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("invalid-key-format")]
    public void ValidateKey_InvalidKey_ShouldReturnFalse(string? invalidKey)
    {
        // Act
        var isValid = _keyManager.ValidateKey(invalidKey!, Liam.Cryptography.Models.KeyType.Symmetric);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void ExtractPublicKey_ValidPrivateKey_ShouldReturnPublicKey()
    {
        // Arrange
        var keyPair = _keyManager.GenerateAsymmetricKeyPair(2048);

        // Act
        var extractedPublicKey = _keyManager.ExtractPublicKey(keyPair.PrivateKey!);

        // Assert
        extractedPublicKey.Should().NotBeNullOrEmpty();
        extractedPublicKey.Should().Be(keyPair.PublicKey!);
    }

    [Fact]
    public void ExtractPublicKey_InvalidPrivateKey_ShouldThrowException()
    {
        // Arrange
        var invalidPrivateKey = "invalid-private-key";

        // Act & Assert
        var action = () => _keyManager.ExtractPublicKey(invalidPrivateKey);
        action.Should().Throw<Exception>();
    }

    #endregion

    #region 异常处理测试

    [Fact]
    public void ExportKey_NullKey_ShouldThrowArgumentNullException()
    {
        // Arrange
        var tempFile = Path.GetTempFileName();

        try
        {
            // Act & Assert
            var action = () => _keyManager.ExportKey(null!, tempFile);
            action.Should().Throw<ArgumentNullException>();
        }
        finally
        {
            CryptoTestHelper.CleanupTempFile(tempFile);
        }
    }

    [Fact]
    public void ExportKey_NullFilePath_ShouldThrowArgumentException()
    {
        // Arrange
        var key = _keyManager.GenerateSymmetricKey(256);

        // Act & Assert
        var action = () => _keyManager.ExportKey(key, null!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void ExportKeyPair_NullKeyPair_ShouldThrowArgumentNullException()
    {
        // Arrange
        var privateKeyFile = Path.GetTempFileName();
        var publicKeyFile = Path.GetTempFileName();

        try
        {
            // Act & Assert
            var action = () => _keyManager.ExportKeyPair(null!, privateKeyFile, publicKeyFile);
            action.Should().Throw<ArgumentNullException>();
        }
        finally
        {
            CryptoTestHelper.CleanupTempFile(privateKeyFile);
            CryptoTestHelper.CleanupTempFile(publicKeyFile);
        }
    }

    [Fact]
    public void ImportKey_InvalidFileFormat_ShouldThrowCryptographyException()
    {
        // Arrange
        var tempFile = _fixture.CreateTempFile("invalid-key-file-content");

        // Act & Assert
        var action = () => _keyManager.ImportKey(tempFile);
        action.Should().Throw<CryptographyException>();
    }

    [Fact]
    public void ImportPrivateKey_InvalidKeyFormat_ShouldThrowCryptographyException()
    {
        // Arrange
        var tempFile = _fixture.CreateTempFile("invalid-private-key-content");

        // Act & Assert
        var action = () => _keyManager.ImportPrivateKey(tempFile);
        action.Should().Throw<CryptographyException>();
    }

    [Fact]
    public void ImportPublicKey_InvalidKeyFormat_ShouldThrowCryptographyException()
    {
        // Arrange
        var tempFile = _fixture.CreateTempFile("invalid-public-key-content");

        // Act & Assert
        var action = () => _keyManager.ImportPublicKey(tempFile);
        action.Should().Throw<CryptographyException>();
    }

    #endregion

    #region 边界条件测试

    [Fact]
    public void ExportImportKey_EmptyPassword_ShouldWorkCorrectly()
    {
        // Arrange
        var originalKey = _keyManager.GenerateSymmetricKey(256);
        var emptyPassword = string.Empty;
        var tempFile = Path.GetTempFileName();

        try
        {
            // Act
            _keyManager.ExportKey(originalKey, tempFile, emptyPassword);
            var importedKey = _keyManager.ImportKey(tempFile, emptyPassword);

            // Assert
            importedKey.Should().Equal(originalKey);
        }
        finally
        {
            CryptoTestHelper.CleanupTempFile(tempFile);
        }
    }

    [Theory]
    [InlineData(128)]
    [InlineData(192)]
    [InlineData(256)]
    public void ExportImportKey_DifferentKeySizes_ShouldWorkCorrectly(int keySize)
    {
        // Arrange
        var originalKey = _keyManager.GenerateSymmetricKey(keySize);
        var tempFile = Path.GetTempFileName();

        try
        {
            // Act
            _keyManager.ExportKey(originalKey, tempFile);
            var importedKey = _keyManager.ImportKey(tempFile);

            // Assert
            importedKey.Should().Equal(originalKey);
        }
        finally
        {
            CryptoTestHelper.CleanupTempFile(tempFile);
        }
    }

    #endregion
}
