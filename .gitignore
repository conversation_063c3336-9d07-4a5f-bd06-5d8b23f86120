# .NET Core
bin/
obj/
*.user
*.suo
*.cache
*.docstates

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/

# NuGet Packages
*.nupkg
# The packages folder can be ignored because of Package Restore
**/packages/*
# except build/, which is used as an MSBuild target.
!**/packages/build/

# Visual Studio
.vs/
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Temporary files
temp_extract/
TestResults/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
