using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

#if LIAM_LOGGING_AVAILABLE
using Liam.Logging.Extensions;
using Liam.Logging.Constants;
using LiamLogLevel = Liam.Logging.Constants.LogLevel;
#endif

namespace Liam.SerialPort.Extensions;

/// <summary>
/// Liam.Logging集成扩展方法
/// </summary>
public static class LiamLoggingExtensions
{
    /// <summary>
    /// 为串口服务配置Liam.Logging日志记录
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureLogging">日志配置委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSerialPortWithLiamLogging(
        this IServiceCollection services,
        Action<SerialPortLoggingOptions>? configureLogging = null)
    {
        // 添加基础串口服务
        services.AddSerialPort();

        var options = new SerialPortLoggingOptions();
        configureLogging?.Invoke(options);

#if LIAM_LOGGING_AVAILABLE
        // 如果Liam.Logging可用，使用增强的日志功能
        services.AddLiamLogging();
        services.ConfigureLiamLogging(builder =>
        {
            // 设置最小日志级别
            builder.SetMinimumLevel(ConvertToLiamLogLevel(options.MinLogLevel));

            // 启用异步日志
            if (options.EnableAsyncLogging)
            {
                builder.EnableAsync();
            }

            // 添加控制台输出
            if (options.EnableConsoleLogging)
            {
                builder.AddConsole();
            }

            // 添加文件输出
            if (options.EnableFileLogging && !string.IsNullOrEmpty(options.LogFilePath))
            {
                builder.AddFile(fileConfig =>
                {
                    fileConfig.FilePath = options.LogFilePath;
                    fileConfig.MaxFileSize = options.MaxFileSize;
                    fileConfig.RetainedFileCount = options.MaxFiles;
                    fileConfig.EnableRotation = options.EnableLogRotation;
                });
            }
        });

        // 同时注册标准的Microsoft.Extensions.Logging服务
        services.AddLogging(builder =>
        {
            builder.SetMinimumLevel(options.MinLogLevel);
            if (options.EnableConsoleLogging)
            {
                builder.AddConsole();
            }
        });
#else
        // 如果Liam.Logging不可用，使用标准日志
        services.AddLogging(builder =>
        {
            builder.SetMinimumLevel(options.MinLogLevel);

            if (options.EnableConsoleLogging)
            {
                builder.AddConsole();
            }
        });
#endif

        return services;
    }

    /// <summary>
    /// 检查Liam.Logging是否可用
    /// </summary>
    /// <returns>是否可用</returns>
    public static bool IsLiamLoggingAvailable()
    {
#if LIAM_LOGGING_AVAILABLE
        return true;
#else
        return false;
#endif
    }

#if LIAM_LOGGING_AVAILABLE
    /// <summary>
    /// 转换Microsoft.Extensions.Logging.LogLevel到Liam.Logging.Constants.LogLevel
    /// </summary>
    /// <param name="logLevel">Microsoft日志级别</param>
    /// <returns>Liam日志级别</returns>
    private static LiamLogLevel ConvertToLiamLogLevel(Microsoft.Extensions.Logging.LogLevel logLevel)
    {
        return logLevel switch
        {
            Microsoft.Extensions.Logging.LogLevel.Trace => LiamLogLevel.Trace,
            Microsoft.Extensions.Logging.LogLevel.Debug => LiamLogLevel.Debug,
            Microsoft.Extensions.Logging.LogLevel.Information => LiamLogLevel.Information,
            Microsoft.Extensions.Logging.LogLevel.Warning => LiamLogLevel.Warning,
            Microsoft.Extensions.Logging.LogLevel.Error => LiamLogLevel.Error,
            Microsoft.Extensions.Logging.LogLevel.Critical => LiamLogLevel.Critical,
            Microsoft.Extensions.Logging.LogLevel.None => LiamLogLevel.None,
            _ => LiamLogLevel.Information
        };
    }
#endif
}

/// <summary>
/// 串口日志配置选项
/// </summary>
public class SerialPortLoggingOptions
{
    /// <summary>
    /// 最小日志级别
    /// </summary>
    public Microsoft.Extensions.Logging.LogLevel MinLogLevel { get; set; } = Microsoft.Extensions.Logging.LogLevel.Information;

    /// <summary>
    /// 启用控制台日志
    /// </summary>
    public bool EnableConsoleLogging { get; set; } = true;

    /// <summary>
    /// 启用文件日志
    /// </summary>
    public bool EnableFileLogging { get; set; } = false;

    /// <summary>
    /// 日志文件路径
    /// </summary>
    public string LogFilePath { get; set; } = "logs/serialport.log";

    /// <summary>
    /// 启用结构化日志
    /// </summary>
    public bool EnableStructuredLogging { get; set; } = true;

    /// <summary>
    /// 启用异步日志
    /// </summary>
    public bool EnableAsyncLogging { get; set; } = true;

    /// <summary>
    /// 启用日志轮转
    /// </summary>
    public bool EnableLogRotation { get; set; } = true;

    /// <summary>
    /// 最大文件大小（字节）
    /// </summary>
    public long MaxFileSize { get; set; } = 10 * 1024 * 1024; // 10MB

    /// <summary>
    /// 最大文件数量
    /// </summary>
    public int MaxFiles { get; set; } = 5;
}
