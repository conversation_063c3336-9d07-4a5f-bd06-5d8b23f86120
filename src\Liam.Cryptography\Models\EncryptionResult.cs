namespace Liam.Cryptography.Models;

/// <summary>
/// 加密结果模型
/// </summary>
public class EncryptionResult
{
    /// <summary>
    /// 加密后的数据
    /// </summary>
    public byte[] Data { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 初始化向量（如果使用）
    /// </summary>
    public byte[]? IV { get; set; }

    /// <summary>
    /// 盐值（如果使用）
    /// </summary>
    public byte[]? Salt { get; set; }

    /// <summary>
    /// 使用的算法
    /// </summary>
    public string Algorithm { get; set; } = string.Empty;

    /// <summary>
    /// 密钥长度（位）
    /// </summary>
    public int KeySize { get; set; }

    /// <summary>
    /// 加密时间
    /// </summary>
    public DateTime EncryptedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 构造函数
    /// </summary>
    public EncryptionResult()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="data">加密数据</param>
    /// <param name="algorithm">算法名称</param>
    /// <param name="keySize">密钥长度</param>
    /// <param name="iv">初始化向量</param>
    /// <param name="salt">盐值</param>
    public EncryptionResult(byte[] data, string algorithm, int keySize, byte[]? iv = null, byte[]? salt = null)
    {
        Data = data;
        Algorithm = algorithm;
        KeySize = keySize;
        IV = iv;
        Salt = salt;
    }

    /// <summary>
    /// 获取Base64编码的加密数据
    /// </summary>
    /// <returns>Base64字符串</returns>
    public string ToBase64()
    {
        return Convert.ToBase64String(Data);
    }

    /// <summary>
    /// 获取十六进制编码的加密数据
    /// </summary>
    /// <returns>十六进制字符串</returns>
    public string ToHex()
    {
        return Convert.ToHexString(Data);
    }

    /// <summary>
    /// 验证结果是否有效
    /// </summary>
    /// <returns>验证结果</returns>
    public bool IsValid()
    {
        return Data.Length > 0 && !string.IsNullOrEmpty(Algorithm) && KeySize > 0;
    }
}
