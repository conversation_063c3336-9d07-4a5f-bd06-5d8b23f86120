using Liam.Logging.Constants;
using Liam.Logging.Exceptions;
using Liam.Logging.Formatters;
using Liam.Logging.Interfaces;
using Liam.Logging.Models;

namespace Liam.Logging.Providers;

/// <summary>
/// 控制台日志提供程序
/// </summary>
public class ConsoleLogProvider : ILogProvider
{
    private readonly object _lock = new();
    private readonly ILogFormatter _formatter;
    private readonly bool _enableColors;
    private readonly bool _useStandardError;
    private readonly TextWriter _output;
    private readonly TextWriter _errorOutput;
    private bool _disposed;

    /// <summary>
    /// 提供程序名称
    /// </summary>
    public string Name => "Console";

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 是否支持异步写入
    /// </summary>
    public bool SupportsAsync => true;

    /// <summary>
    /// 初始化控制台日志提供程序
    /// </summary>
    /// <param name="formatter">日志格式化器</param>
    /// <param name="enableColors">是否启用颜色输出</param>
    /// <param name="useStandardError">是否输出到标准错误流</param>
    public ConsoleLogProvider(
        ILogFormatter? formatter = null,
        bool enableColors = true,
        bool useStandardError = false)
    {
        _formatter = formatter ?? new TextLogFormatter();
        _enableColors = enableColors;
        _useStandardError = useStandardError;
        _output = Console.Out;
        _errorOutput = Console.Error;
    }

    /// <summary>
    /// 初始化提供程序
    /// </summary>
    /// <param name="configuration">配置信息</param>
    /// <returns>异步任务</returns>
    public Task InitializeAsync(LogProviderConfiguration configuration)
    {
        // 控制台提供程序无需特殊初始化
        return Task.CompletedTask;
    }

    /// <summary>
    /// 写入日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    public void WriteLog(LogEvent logEvent)
    {
        if (!IsEnabled || _disposed)
            return;

        try
        {
            var formattedMessage = _formatter.Format(logEvent);
            var writer = ShouldUseErrorStream(logEvent.Level) ? _errorOutput : _output;

            lock (_lock)
            {
                if (_enableColors && SupportsColors())
                {
                    WriteWithColor(writer, formattedMessage, logEvent.Level);
                }
                else
                {
                    writer.WriteLine(formattedMessage);
                }
                writer.Flush();
            }
        }
        catch (Exception ex)
        {
            throw new LogProviderException(Name, "写入控制台日志失败", ex);
        }
    }

    /// <summary>
    /// 异步写入日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public Task WriteLogAsync(LogEvent logEvent, CancellationToken cancellationToken = default)
    {
        if (!IsEnabled || _disposed)
            return Task.CompletedTask;

        try
        {
            cancellationToken.ThrowIfCancellationRequested();

            var formattedMessage = _formatter.Format(logEvent);
            var writer = ShouldUseErrorStream(logEvent.Level) ? _errorOutput : _output;

            lock (_lock)
            {
                if (_enableColors && SupportsColors())
                {
                    WriteWithColor(writer, formattedMessage, logEvent.Level);
                }
                else
                {
                    writer.WriteLine(formattedMessage);
                }
                writer.Flush();
            }

            return Task.CompletedTask;
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw new LogProviderException(Name, "异步写入控制台日志失败", ex);
        }
    }

    /// <summary>
    /// 批量写入日志事件
    /// </summary>
    /// <param name="logEvents">日志事件集合</param>
    public void WriteLogs(IEnumerable<LogEvent> logEvents)
    {
        if (!IsEnabled || _disposed)
            return;

        try
        {
            lock (_lock)
            {
                foreach (var logEvent in logEvents)
                {
                    var formattedMessage = _formatter.Format(logEvent);
                    var writer = ShouldUseErrorStream(logEvent.Level) ? _errorOutput : _output;

                    if (_enableColors && SupportsColors())
                    {
                        WriteWithColor(writer, formattedMessage, logEvent.Level);
                    }
                    else
                    {
                        writer.WriteLine(formattedMessage);
                    }
                }
                _output.Flush();
                _errorOutput.Flush();
            }
        }
        catch (Exception ex)
        {
            throw new LogProviderException(Name, "批量写入控制台日志失败", ex);
        }
    }

    /// <summary>
    /// 异步批量写入日志事件
    /// </summary>
    /// <param name="logEvents">日志事件集合</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public Task WriteLogsAsync(IEnumerable<LogEvent> logEvents, CancellationToken cancellationToken = default)
    {
        if (!IsEnabled || _disposed)
            return Task.CompletedTask;

        try
        {
            lock (_lock)
            {
                foreach (var logEvent in logEvents)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var formattedMessage = _formatter.Format(logEvent);
                    var writer = ShouldUseErrorStream(logEvent.Level) ? _errorOutput : _output;

                    if (_enableColors && SupportsColors())
                    {
                        WriteWithColor(writer, formattedMessage, logEvent.Level);
                    }
                    else
                    {
                        writer.WriteLine(formattedMessage);
                    }
                }
                _output.Flush();
                _errorOutput.Flush();
            }

            return Task.CompletedTask;
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw new LogProviderException(Name, "异步批量写入控制台日志失败", ex);
        }
    }

    /// <summary>
    /// 刷新缓冲区
    /// </summary>
    public void Flush()
    {
        if (_disposed)
            return;

        lock (_lock)
        {
            _output.Flush();
            _errorOutput.Flush();
        }
    }

    /// <summary>
    /// 异步刷新缓冲区
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public Task FlushAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return Task.CompletedTask;

        cancellationToken.ThrowIfCancellationRequested();

        lock (_lock)
        {
            _output.Flush();
            _errorOutput.Flush();
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                try
                {
                    Flush();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"刷新控制台输出失败: {ex.Message}");
                }
            }
            _disposed = true;
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 判断是否应该使用错误流
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <returns>是否使用错误流</returns>
    private bool ShouldUseErrorStream(LogLevel level)
    {
        return _useStandardError || level >= LogLevel.Error;
    }

    /// <summary>
    /// 判断是否支持颜色输出
    /// </summary>
    /// <returns>是否支持颜色</returns>
    private static bool SupportsColors()
    {
        try
        {
            // 检查是否在支持颜色的终端中运行
            return !Console.IsOutputRedirected && !Console.IsErrorRedirected;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 带颜色写入文本
    /// </summary>
    /// <param name="writer">文本写入器</param>
    /// <param name="message">消息</param>
    /// <param name="level">日志级别</param>
    private static void WriteWithColor(TextWriter writer, string message, LogLevel level)
    {
        var originalColor = Console.ForegroundColor;
        try
        {
            Console.ForegroundColor = LogLevelConstants.GetLevelColor(level);
            writer.WriteLine(message);
        }
        finally
        {
            Console.ForegroundColor = originalColor;
        }
    }
}
