using FluentAssertions;
using Liam.TcpServer.Models;
using Liam.TcpServer.Security;
using Xunit;

namespace Liam.TcpServer.Tests.Security;

/// <summary>
/// 密码哈希器测试
/// </summary>
public class PasswordHasherTests
{
    private readonly SecuritySettings _settings;

    public PasswordHasherTests()
    {
        _settings = new SecuritySettings
        {
            SaltLength = 32,
            Pbkdf2Iterations = 10000, // 测试时使用较少的迭代次数
            Argon2MemorySize = 1024,
            Argon2Parallelism = 1,
            Argon2Iterations = 2
        };
    }

    [Theory]
    [InlineData(PasswordHashAlgorithm.PBKDF2_SHA256)]
    [InlineData(PasswordHashAlgorithm.Argon2id)]
    [InlineData(PasswordHashAlgorithm.BCrypt)]
    public void HashPassword_ShouldCreateValidUserSecurityInfo(PasswordHashAlgorithm algorithm)
    {
        // Arrange
        const string password = "TestPassword123!";
        _settings.PasswordHashAlgorithm = algorithm;

        // Act
        var userInfo = PasswordHasher.HashPassword(password, algorithm, _settings);

        // Assert
        userInfo.Should().NotBeNull();
        userInfo.PasswordHash.Should().NotBeNullOrEmpty();
        userInfo.Salt.Should().NotBeNullOrEmpty();
        userInfo.Salt.Length.Should().Be(_settings.SaltLength);
        userInfo.Algorithm.Should().Be(algorithm);
        userInfo.AlgorithmParameters.Should().NotBeNullOrEmpty();
        userInfo.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        userInfo.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Theory]
    [InlineData(PasswordHashAlgorithm.PBKDF2_SHA256)]
    [InlineData(PasswordHashAlgorithm.Argon2id)]
    [InlineData(PasswordHashAlgorithm.BCrypt)]
    public void VerifyPassword_WithCorrectPassword_ShouldReturnTrue(PasswordHashAlgorithm algorithm)
    {
        // Arrange
        const string password = "TestPassword123!";
        _settings.PasswordHashAlgorithm = algorithm;
        var userInfo = PasswordHasher.HashPassword(password, algorithm, _settings);

        // Act
        var result = PasswordHasher.VerifyPassword(password, userInfo);

        // Assert
        result.Should().BeTrue();
    }

    [Theory]
    [InlineData(PasswordHashAlgorithm.PBKDF2_SHA256)]
    [InlineData(PasswordHashAlgorithm.Argon2id)]
    [InlineData(PasswordHashAlgorithm.BCrypt)]
    public void VerifyPassword_WithIncorrectPassword_ShouldReturnFalse(PasswordHashAlgorithm algorithm)
    {
        // Arrange
        const string correctPassword = "TestPassword123!";
        const string incorrectPassword = "WrongPassword456!";
        _settings.PasswordHashAlgorithm = algorithm;
        var userInfo = PasswordHasher.HashPassword(correctPassword, algorithm, _settings);

        // Act
        var result = PasswordHasher.VerifyPassword(incorrectPassword, userInfo);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void VerifyPassword_WithExpiredUserInfo_ShouldReturnFalse()
    {
        // Arrange
        const string password = "TestPassword123!";
        var userInfo = PasswordHasher.HashPassword(password, PasswordHashAlgorithm.PBKDF2_SHA256, _settings);
        userInfo.IsExpired = true;

        // Act
        var result = PasswordHasher.VerifyPassword(password, userInfo);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void VerifyPassword_WithExpiredExpiresAt_ShouldReturnFalse()
    {
        // Arrange
        const string password = "TestPassword123!";
        var userInfo = PasswordHasher.HashPassword(password, PasswordHashAlgorithm.PBKDF2_SHA256, _settings);
        userInfo.ExpiresAt = DateTime.UtcNow.AddDays(-1); // 昨天过期

        // Act
        var result = PasswordHasher.VerifyPassword(password, userInfo);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void NeedsRehash_WithDifferentAlgorithm_ShouldReturnTrue()
    {
        // Arrange
        var userInfo = PasswordHasher.HashPassword("password", PasswordHashAlgorithm.PBKDF2_SHA256, _settings);
        _settings.PasswordHashAlgorithm = PasswordHashAlgorithm.Argon2id;

        // Act
        var result = PasswordHasher.NeedsRehash(userInfo, _settings);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void NeedsRehash_WithDifferentSaltLength_ShouldReturnTrue()
    {
        // Arrange
        var userInfo = PasswordHasher.HashPassword("password", PasswordHashAlgorithm.PBKDF2_SHA256, _settings);
        _settings.SaltLength = 64; // 不同的盐值长度

        // Act
        var result = PasswordHasher.NeedsRehash(userInfo, _settings);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void NeedsRehash_WithExpiredUserInfo_ShouldReturnTrue()
    {
        // Arrange
        var userInfo = PasswordHasher.HashPassword("password", PasswordHashAlgorithm.PBKDF2_SHA256, _settings);
        userInfo.IsExpired = true;

        // Act
        var result = PasswordHasher.NeedsRehash(userInfo, _settings);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void NeedsRehash_WithSameSettings_ShouldReturnFalse()
    {
        // Arrange
        var userInfo = PasswordHasher.HashPassword("password", PasswordHashAlgorithm.PBKDF2_SHA256, _settings);

        // 创建一个新的设置对象，确保参数完全相同
        var sameSettings = new SecuritySettings
        {
            SaltLength = 32,
            Pbkdf2Iterations = 10000,
            Argon2MemorySize = 1024,
            Argon2Parallelism = 1,
            Argon2Iterations = 2,
            PasswordHashAlgorithm = PasswordHashAlgorithm.PBKDF2_SHA256
        };

        // Act
        var result = PasswordHasher.NeedsRehash(userInfo, sameSettings);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void HashPassword_WithNullPassword_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => PasswordHasher.HashPassword(null!, PasswordHashAlgorithm.PBKDF2_SHA256, _settings);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void VerifyPassword_WithNullPassword_ShouldThrowArgumentNullException()
    {
        // Arrange
        var userInfo = PasswordHasher.HashPassword("password", PasswordHashAlgorithm.PBKDF2_SHA256, _settings);

        // Act & Assert
        var act = () => PasswordHasher.VerifyPassword(null!, userInfo);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void VerifyPassword_WithNullUserInfo_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => PasswordHasher.VerifyPassword("password", null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void HashPassword_ShouldGenerateUniqueSalts()
    {
        // Arrange
        const string password = "TestPassword123!";

        // Act
        var userInfo1 = PasswordHasher.HashPassword(password, PasswordHashAlgorithm.PBKDF2_SHA256, _settings);
        var userInfo2 = PasswordHasher.HashPassword(password, PasswordHashAlgorithm.PBKDF2_SHA256, _settings);

        // Assert
        userInfo1.Salt.Should().NotBeEquivalentTo(userInfo2.Salt);
        userInfo1.PasswordHash.Should().NotBe(userInfo2.PasswordHash);
    }

    [Fact]
    public void HashPassword_ShouldGenerateUniqueHashesForSamePassword()
    {
        // Arrange
        const string password = "TestPassword123!";

        // Act
        var userInfo1 = PasswordHasher.HashPassword(password, PasswordHashAlgorithm.PBKDF2_SHA256, _settings);
        var userInfo2 = PasswordHasher.HashPassword(password, PasswordHashAlgorithm.PBKDF2_SHA256, _settings);

        // Assert
        userInfo1.PasswordHash.Should().NotBe(userInfo2.PasswordHash);
        
        // 但是验证应该都成功
        PasswordHasher.VerifyPassword(password, userInfo1).Should().BeTrue();
        PasswordHasher.VerifyPassword(password, userInfo2).Should().BeTrue();
    }
}
