using System.Net;
using Liam.TcpServer.Constants;

namespace Liam.TcpServer.Models;

/// <summary>
/// 安全设置
/// </summary>
public class SecuritySettings
{
    /// <summary>
    /// 是否启用IP白名单
    /// </summary>
    public bool EnableWhitelist { get; set; } = false;

    /// <summary>
    /// IP白名单
    /// </summary>
    public HashSet<IPAddress> Whitelist { get; set; } = new();

    /// <summary>
    /// 是否启用IP黑名单
    /// </summary>
    public bool EnableBlacklist { get; set; } = false;

    /// <summary>
    /// IP黑名单
    /// </summary>
    public HashSet<IPAddress> Blacklist { get; set; } = new();

    /// <summary>
    /// 是否启用连接频率限制
    /// </summary>
    public bool EnableConnectionRateLimit { get; set; } = false;

    /// <summary>
    /// 连接频率限制（每分钟最大连接数）
    /// </summary>
    public int ConnectionRateLimit { get; set; } = TcpServerConstants.Security.DefaultConnectionRateLimit;

    /// <summary>
    /// 连接频率限制时间窗口（分钟）
    /// </summary>
    public int RateLimitWindowMinutes { get; set; } = 1;

    /// <summary>
    /// 是否启用自动黑名单（频繁连接的IP自动加入黑名单）
    /// </summary>
    public bool EnableAutoBlacklist { get; set; } = false;

    /// <summary>
    /// 自动黑名单触发阈值（超过此连接数自动加入黑名单）
    /// </summary>
    public int AutoBlacklistThreshold { get; set; } = 100;

    /// <summary>
    /// 自动黑名单持续时间（分钟）
    /// </summary>
    public int AutoBlacklistDurationMinutes { get; set; } = 60;

    /// <summary>
    /// 黑名单检查间隔（分钟）
    /// </summary>
    public int BlacklistCheckIntervalMinutes { get; set; } = TcpServerConstants.Security.BlacklistCheckIntervalMinutes;

    /// <summary>
    /// 是否启用连接认证
    /// </summary>
    public bool EnableAuthentication { get; set; } = false;

    /// <summary>
    /// 认证超时时间（秒）
    /// </summary>
    public int AuthenticationTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 认证密钥（简单认证）
    /// </summary>
    public string? AuthenticationKey { get; set; }

    /// <summary>
    /// 有效用户凭据（用户名 -> 用户安全信息）
    /// </summary>
    public Dictionary<string, UserSecurityInfo>? ValidUsers { get; set; }

    /// <summary>
    /// 密码哈希算法类型
    /// </summary>
    public PasswordHashAlgorithm PasswordHashAlgorithm { get; set; } = PasswordHashAlgorithm.Argon2id;

    /// <summary>
    /// PBKDF2 迭代次数
    /// </summary>
    public int Pbkdf2Iterations { get; set; } = 100000;

    /// <summary>
    /// Argon2 内存大小 (KB)
    /// </summary>
    public int Argon2MemorySize { get; set; } = 65536; // 64MB

    /// <summary>
    /// Argon2 并行度
    /// </summary>
    public int Argon2Parallelism { get; set; } = 1;

    /// <summary>
    /// Argon2 迭代次数
    /// </summary>
    public int Argon2Iterations { get; set; } = 3;

    /// <summary>
    /// 盐值长度（字节）
    /// </summary>
    public int SaltLength { get; set; } = 32;

    /// <summary>
    /// 是否启用消息加密
    /// </summary>
    public bool EnableMessageEncryption { get; set; } = false;

    /// <summary>
    /// 消息加密算法类型
    /// </summary>
    public MessageEncryptionAlgorithm MessageEncryptionAlgorithm { get; set; } = MessageEncryptionAlgorithm.AES256GCM;

    /// <summary>
    /// 消息加密密钥
    /// </summary>
    public byte[]? EncryptionKey { get; set; }

    /// <summary>
    /// 密钥轮换间隔（小时）
    /// </summary>
    public int KeyRotationIntervalHours { get; set; } = 24;

    /// <summary>
    /// 添加IP到白名单
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    public void AddToWhitelist(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);
        Whitelist.Add(ipAddress);
    }

    /// <summary>
    /// 添加IP到白名单
    /// </summary>
    /// <param name="ipAddress">IP地址字符串</param>
    public void AddToWhitelist(string ipAddress)
    {
        if (IPAddress.TryParse(ipAddress, out var ip))
        {
            AddToWhitelist(ip);
        }
        else
        {
            throw new ArgumentException($"无效的IP地址格式：{ipAddress}", nameof(ipAddress));
        }
    }

    /// <summary>
    /// 从白名单移除IP
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    public void RemoveFromWhitelist(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);
        Whitelist.Remove(ipAddress);
    }

    /// <summary>
    /// 添加IP到黑名单
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    public void AddToBlacklist(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);
        Blacklist.Add(ipAddress);
    }

    /// <summary>
    /// 添加IP到黑名单
    /// </summary>
    /// <param name="ipAddress">IP地址字符串</param>
    public void AddToBlacklist(string ipAddress)
    {
        if (IPAddress.TryParse(ipAddress, out var ip))
        {
            AddToBlacklist(ip);
        }
        else
        {
            throw new ArgumentException($"无效的IP地址格式：{ipAddress}", nameof(ipAddress));
        }
    }

    /// <summary>
    /// 从黑名单移除IP
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    public void RemoveFromBlacklist(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);
        Blacklist.Remove(ipAddress);
    }

    /// <summary>
    /// 检查IP是否在白名单中
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>是否在白名单中</returns>
    public bool IsInWhitelist(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);
        return !EnableWhitelist || Whitelist.Contains(ipAddress);
    }

    /// <summary>
    /// 检查IP是否在黑名单中
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>是否在黑名单中</returns>
    public bool IsInBlacklist(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);
        return EnableBlacklist && Blacklist.Contains(ipAddress);
    }

    /// <summary>
    /// 检查IP是否被允许连接
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>是否被允许连接</returns>
    public bool IsConnectionAllowed(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);
        
        // 检查黑名单
        if (IsInBlacklist(ipAddress))
        {
            return false;
        }

        // 检查白名单
        if (EnableWhitelist && !IsInWhitelist(ipAddress))
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// 清空白名单
    /// </summary>
    public void ClearWhitelist()
    {
        Whitelist.Clear();
    }

    /// <summary>
    /// 清空黑名单
    /// </summary>
    public void ClearBlacklist()
    {
        Blacklist.Clear();
    }

    /// <summary>
    /// 验证安全设置
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        if (EnableConnectionRateLimit)
        {
            if (ConnectionRateLimit <= 0)
            {
                errors.Add($"连接频率限制必须大于0，当前值：{ConnectionRateLimit}");
            }

            if (RateLimitWindowMinutes <= 0)
            {
                errors.Add($"频率限制时间窗口必须大于0，当前值：{RateLimitWindowMinutes}");
            }
        }

        if (EnableAutoBlacklist)
        {
            if (AutoBlacklistThreshold <= 0)
            {
                errors.Add($"自动黑名单阈值必须大于0，当前值：{AutoBlacklistThreshold}");
            }

            if (AutoBlacklistDurationMinutes <= 0)
            {
                errors.Add($"自动黑名单持续时间必须大于0，当前值：{AutoBlacklistDurationMinutes}");
            }
        }

        if (BlacklistCheckIntervalMinutes <= 0)
        {
            errors.Add($"黑名单检查间隔必须大于0，当前值：{BlacklistCheckIntervalMinutes}");
        }

        if (EnableAuthentication)
        {
            if (AuthenticationTimeoutSeconds <= 0)
            {
                errors.Add($"认证超时时间必须大于0，当前值：{AuthenticationTimeoutSeconds}");
            }

            if (string.IsNullOrWhiteSpace(AuthenticationKey))
            {
                errors.Add("启用认证时必须提供认证密钥");
            }
        }

        if (EnableMessageEncryption)
        {
            if (EncryptionKey == null || EncryptionKey.Length == 0)
            {
                errors.Add("启用消息加密时必须提供加密密钥");
            }
        }

        return new ValidationResult(errors.Count == 0, errors);
    }
}

/// <summary>
/// 用户安全信息
/// </summary>
public class UserSecurityInfo
{
    /// <summary>
    /// 密码哈希
    /// </summary>
    public string PasswordHash { get; set; } = string.Empty;

    /// <summary>
    /// 盐值
    /// </summary>
    public byte[] Salt { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 哈希算法类型
    /// </summary>
    public PasswordHashAlgorithm Algorithm { get; set; } = PasswordHashAlgorithm.Argon2id;

    /// <summary>
    /// 算法参数（JSON格式存储）
    /// </summary>
    public string? AlgorithmParameters { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired { get; set; } = false;

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// 密码哈希算法类型
/// </summary>
public enum PasswordHashAlgorithm
{
    /// <summary>
    /// PBKDF2 with SHA-256
    /// </summary>
    PBKDF2_SHA256,

    /// <summary>
    /// Argon2id (推荐)
    /// </summary>
    Argon2id,

    /// <summary>
    /// BCrypt
    /// </summary>
    BCrypt
}

/// <summary>
/// 消息加密算法类型
/// </summary>
public enum MessageEncryptionAlgorithm
{
    /// <summary>
    /// AES-256-GCM (推荐)
    /// </summary>
    AES256GCM,

    /// <summary>
    /// ChaCha20-Poly1305
    /// </summary>
    ChaCha20Poly1305,

    /// <summary>
    /// AES-256-CBC (不推荐，仅兼容性)
    /// </summary>
    AES256CBC
}
