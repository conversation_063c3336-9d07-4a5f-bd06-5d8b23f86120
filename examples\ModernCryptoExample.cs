using Liam.Cryptography.Extensions;
using Liam.Cryptography.Services;

namespace Liam.Cryptography.Examples;

/// <summary>
/// 现代加密算法使用示例
/// </summary>
public class ModernCryptoExample
{
    public static async Task RunExamples()
    {
        Console.WriteLine("=== Liam.Cryptography 现代加密算法示例 ===\n");

        // 1. ChaCha20-Poly1305 AEAD加密示例
        await ChaCha20Poly1305Example();

        // 2. ECDSA数字签名示例
        await EcdsaSignatureExample();

        // 3. Argon2密码哈希示例
        await Argon2PasswordExample();

        // 4. 流式处理示例
        await StreamProcessingExample();

        // 5. 性能对比示例
        await PerformanceComparisonExample();
    }

    private static async Task ChaCha20Poly1305Example()
    {
        Console.WriteLine("1. ChaCha20-Poly1305 AEAD加密示例");
        Console.WriteLine("================================");

        try
        {
            // 生成密钥
            var chacha = new ChaCha20Poly1305Crypto();
            var key = chacha.GenerateKey();
            Console.WriteLine($"生成的密钥长度: {key.Length * 8} 位");

            // 加密数据
            var plainText = "这是使用ChaCha20-Poly1305加密的敏感数据！🔐";
            var encrypted = plainText.EncryptChaCha20Poly1305(key);
            Console.WriteLine($"原文: {plainText}");
            Console.WriteLine($"密文: {encrypted[..50]}...");

            // 解密数据
            var decrypted = encrypted.DecryptChaCha20Poly1305(key);
            Console.WriteLine($"解密: {decrypted}");
            Console.WriteLine($"验证: {plainText == decrypted} ✅\n");

            // 异步操作
            var encryptedAsync = await plainText.EncryptChaCha20Poly1305Async(key);
            var decryptedAsync = await encryptedAsync.DecryptChaCha20Poly1305Async(key);
            Console.WriteLine($"异步操作验证: {plainText == decryptedAsync} ✅\n");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ChaCha20-Poly1305示例出错: {ex.Message}\n");
        }
    }

    private static async Task EcdsaSignatureExample()
    {
        Console.WriteLine("2. ECDSA数字签名示例");
        Console.WriteLine("===================");

        try
        {
            // 生成密钥对
            var ecdsa = new Ed25519DigitalSignature();
            var keyPair = ecdsa.GenerateKeyPair();
            Console.WriteLine("生成ECDSA密钥对成功");

            // 数字签名
            var data = "这是需要签名的重要文档内容";
            var signature = data.SignEcdsa(keyPair.PrivateKey!);
            Console.WriteLine($"原始数据: {data}");
            Console.WriteLine($"数字签名: {signature[..50]}...");

            // 验证签名
            var isValid = data.VerifyEcdsaSignature(signature, keyPair.PublicKey!);
            Console.WriteLine($"签名验证: {isValid} ✅");

            // 篡改检测
            var tamperedData = data + " [已篡改]";
            var isTamperedValid = tamperedData.VerifyEcdsaSignature(signature, keyPair.PublicKey!);
            Console.WriteLine($"篡改检测: {!isTamperedValid} ✅");

            // 异步操作
            var signatureAsync = await data.SignEcdsaAsync(keyPair.PrivateKey!);
            var isValidAsync = await data.VerifyEcdsaSignatureAsync(signatureAsync, keyPair.PublicKey!);
            Console.WriteLine($"异步签名验证: {isValidAsync} ✅\n");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ECDSA示例出错: {ex.Message}\n");
        }
    }

    private static async Task Argon2PasswordExample()
    {
        Console.WriteLine("3. Argon2密码哈希示例");
        Console.WriteLine("====================");

        try
        {
            // 密码哈希
            var password = "MySecurePassword123!";
            var hashResult = password.ToArgon2Hash();
            Console.WriteLine($"原始密码: {password}");
            Console.WriteLine($"Argon2哈希: {hashResult.FormattedHash}");
            Console.WriteLine($"盐值: {hashResult.Salt}");

            // 密码验证
            var isValid = password.VerifyArgon2Hash(hashResult);
            Console.WriteLine($"密码验证: {isValid} ✅");

            // 错误密码验证
            var wrongPassword = "WrongPassword";
            var isWrongValid = wrongPassword.VerifyArgon2Hash(hashResult);
            Console.WriteLine($"错误密码验证: {!isWrongValid} ✅");

            // 自定义配置
            var customOptions = new Argon2PasswordHasher.Argon2Options
            {
                Iterations = 6,
                MemorySize = 131072, // 128 MB
                DegreeOfParallelism = 2
            };
            var customHashResult = password.ToArgon2Hash(customOptions);
            Console.WriteLine($"自定义配置哈希: {customHashResult.FormattedHash}");

            // 异步操作
            var hashResultAsync = await password.ToArgon2HashAsync();
            var isValidAsync = await password.VerifyArgon2HashAsync(hashResultAsync);
            Console.WriteLine($"异步验证: {isValidAsync} ✅\n");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Argon2示例出错: {ex.Message}\n");
        }
    }

    private static async Task StreamProcessingExample()
    {
        Console.WriteLine("4. 流式处理示例");
        Console.WriteLine("===============");

        try
        {
            // 创建测试数据
            var testData = string.Join("\n", Enumerable.Range(1, 1000)
                .Select(i => $"这是第{i}行测试数据，用于演示流式处理功能。"));
            
            var inputFile = Path.GetTempFileName();
            var encryptedFile = Path.GetTempFileName();
            var decryptedFile = Path.GetTempFileName();

            try
            {
                // 写入测试数据
                await File.WriteAllTextAsync(inputFile, testData);
                Console.WriteLine($"创建测试文件: {new FileInfo(inputFile).Length} 字节");

                // 生成AES密钥
                var aes = new AesSymmetricCrypto();
                var key = aes.GenerateKey();

                // 流式加密
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                await StreamCryptoExtensions.EncryptFileAsync(inputFile, encryptedFile, key);
                stopwatch.Stop();
                Console.WriteLine($"加密完成: {stopwatch.ElapsedMilliseconds}ms");
                Console.WriteLine($"加密文件大小: {new FileInfo(encryptedFile).Length} 字节");

                // 流式解密
                stopwatch.Restart();
                await StreamCryptoExtensions.DecryptFileAsync(encryptedFile, decryptedFile, key);
                stopwatch.Stop();
                Console.WriteLine($"解密完成: {stopwatch.ElapsedMilliseconds}ms");

                // 验证数据完整性
                var originalHash = StreamCryptoExtensions.ComputeFileSha256Hash(inputFile);
                var decryptedHash = StreamCryptoExtensions.ComputeFileSha256Hash(decryptedFile);
                Console.WriteLine($"数据完整性验证: {originalHash == decryptedHash} ✅\n");
            }
            finally
            {
                // 清理临时文件
                File.Delete(inputFile);
                File.Delete(encryptedFile);
                File.Delete(decryptedFile);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"流式处理示例出错: {ex.Message}\n");
        }
    }

    private static async Task PerformanceComparisonExample()
    {
        Console.WriteLine("5. 性能对比示例");
        Console.WriteLine("===============");

        try
        {
            var testData = "这是用于性能测试的数据" + new string('A', 10000);
            var iterations = 100;

            // AES vs ChaCha20-Poly1305 性能对比
            var aes = new AesSymmetricCrypto();
            var aesKey = aes.GenerateKey();

            var chacha = new ChaCha20Poly1305Crypto();
            var chachaKey = chacha.GenerateKey();

            // AES性能测试
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            for (int i = 0; i < iterations; i++)
            {
                var encrypted = testData.EncryptAes(aesKey);
                var decrypted = encrypted.DecryptAes(aesKey);
            }
            stopwatch.Stop();
            var aesTime = stopwatch.ElapsedMilliseconds;

            // ChaCha20-Poly1305性能测试
            stopwatch.Restart();
            for (int i = 0; i < iterations; i++)
            {
                var encrypted = testData.EncryptChaCha20Poly1305(chachaKey);
                var decrypted = encrypted.DecryptChaCha20Poly1305(chachaKey);
            }
            stopwatch.Stop();
            var chachaTime = stopwatch.ElapsedMilliseconds;

            Console.WriteLine($"AES-256-CBC: {aesTime}ms ({iterations}次操作)");
            Console.WriteLine($"ChaCha20-Poly1305: {chachaTime}ms ({iterations}次操作)");
            Console.WriteLine($"性能比较: ChaCha20-Poly1305 {(aesTime > chachaTime ? "更快" : "较慢")}");

            // 哈希性能测试
            stopwatch.Restart();
            for (int i = 0; i < iterations; i++)
            {
                var hash = testData.ToSha256Hash();
            }
            stopwatch.Stop();
            Console.WriteLine($"SHA-256哈希: {stopwatch.ElapsedMilliseconds}ms ({iterations}次操作)");

            // Argon2性能测试（较少迭代，因为计算密集）
            var passwordIterations = 5;
            stopwatch.Restart();
            for (int i = 0; i < passwordIterations; i++)
            {
                var hash = $"password{i}".ToArgon2Hash();
            }
            stopwatch.Stop();
            Console.WriteLine($"Argon2密码哈希: {stopwatch.ElapsedMilliseconds}ms ({passwordIterations}次操作)");
            Console.WriteLine();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"性能对比示例出错: {ex.Message}\n");
        }
    }
}
