using FluentAssertions;
using Liam.SerialPort.Models;
using Liam.SerialPort.Tests.TestHelpers;
using System.IO.Ports;
using Xunit;

namespace Liam.SerialPort.Tests.Models;

/// <summary>
/// SerialPortSettings 测试类
/// </summary>
public class SerialPortSettingsTests : SerialPortTestBase
{
    [Fact]
    public void Constructor_ShouldSetDefaultValues()
    {
        // Arrange & Act
        var settings = new SerialPortSettings();

        // Assert
        settings.BaudRate.Should().Be(9600);
        settings.DataBits.Should().Be(8);
        settings.StopBits.Should().Be(StopBits.One);
        settings.Parity.Should().Be(Parity.None);
        settings.Handshake.Should().Be(Handshake.None);
        settings.ReadTimeout.Should().Be(5000);
        settings.WriteTimeout.Should().Be(5000);
        settings.ReceiveBufferSize.Should().Be(4096);
        settings.SendBufferSize.Should().Be(2048);
        settings.DtrEnable.Should().BeFalse();
        settings.RtsEnable.Should().BeFalse();
        settings.NewLine.Should().Be("\r\n");
        settings.Encoding.Should().Be(System.Text.Encoding.UTF8);
        settings.DiscardNull.Should().BeFalse();
        settings.RetryCount.Should().Be(3);
        settings.RetryInterval.Should().Be(1000);
        settings.AutoReconnect.Should().BeTrue();
        settings.AutoReconnectInterval.Should().Be(5000);
        settings.MaxAutoReconnectAttempts.Should().Be(-1);
        settings.ConnectionTimeout.Should().Be(10000);
        settings.HeartbeatInterval.Should().Be(0);
        settings.HeartbeatData.Should().BeEmpty();
        settings.EnableDataLogging.Should().BeFalse();
        settings.MaxLogSize.Should().Be(1024 * 1024);
    }

    [Fact]
    public void Default_ShouldReturnDefaultSettings()
    {
        // Arrange & Act
        var settings = SerialPortSettings.Default;

        // Assert
        settings.Should().NotBeNull();
        settings.BaudRate.Should().Be(9600);
        settings.DataBits.Should().Be(8);
        settings.StopBits.Should().Be(StopBits.One);
        settings.Parity.Should().Be(Parity.None);
    }

    [Fact]
    public void Baud9600_ShouldReturnCorrectBaudRate()
    {
        // Arrange & Act
        var settings = SerialPortSettings.Baud9600;

        // Assert
        settings.Should().NotBeNull();
        settings.BaudRate.Should().Be(9600);
    }

    [Fact]
    public void Baud115200_ShouldReturnCorrectBaudRate()
    {
        // Arrange & Act
        var settings = SerialPortSettings.Baud115200;

        // Assert
        settings.Should().NotBeNull();
        settings.BaudRate.Should().Be(115200);
    }

    [Theory]
    [InlineData(9600, 8, true)]
    [InlineData(115200, 8, true)]
    [InlineData(0, 8, false)]
    [InlineData(-1, 8, false)]
    [InlineData(9600, 4, false)]
    [InlineData(9600, 9, false)]
    public void Validate_BaudRateAndDataBits_ShouldReturnExpectedResult(int baudRate, int dataBits, bool expectedValid)
    {
        // Arrange
        var settings = new SerialPortSettings
        {
            BaudRate = baudRate,
            DataBits = dataBits
        };

        // Act
        var result = settings.Validate();

        // Assert
        result.IsValid.Should().Be(expectedValid);
    }

    [Theory]
    [InlineData(-1, false)]
    [InlineData(0, true)]
    [InlineData(1000, true)]
    [InlineData(5000, true)]
    public void Validate_ReadTimeout_ShouldReturnExpectedResult(int readTimeout, bool expectedValid)
    {
        // Arrange
        var settings = new SerialPortSettings
        {
            ReadTimeout = readTimeout
        };

        // Act
        var result = settings.Validate();

        // Assert
        result.IsValid.Should().Be(expectedValid);
    }

    [Theory]
    [InlineData(-1, false)]
    [InlineData(0, true)]
    [InlineData(1000, true)]
    [InlineData(5000, true)]
    public void Validate_WriteTimeout_ShouldReturnExpectedResult(int writeTimeout, bool expectedValid)
    {
        // Arrange
        var settings = new SerialPortSettings
        {
            WriteTimeout = writeTimeout
        };

        // Act
        var result = settings.Validate();

        // Assert
        result.IsValid.Should().Be(expectedValid);
    }

    [Theory]
    [InlineData(0, false)]
    [InlineData(-1, false)]
    [InlineData(1, true)]
    [InlineData(1024, true)]
    [InlineData(4096, true)]
    public void Validate_BufferSizes_ShouldReturnExpectedResult(int bufferSize, bool expectedValid)
    {
        // Arrange
        var settings = new SerialPortSettings
        {
            ReceiveBufferSize = bufferSize,
            SendBufferSize = bufferSize
        };

        // Act
        var result = settings.Validate();

        // Assert
        result.IsValid.Should().Be(expectedValid);
    }

    [Theory]
    [InlineData(-1, false)]
    [InlineData(0, true)]
    [InlineData(3, true)]
    [InlineData(10, true)]
    public void Validate_RetryCount_ShouldReturnExpectedResult(int retryCount, bool expectedValid)
    {
        // Arrange
        var settings = new SerialPortSettings
        {
            RetryCount = retryCount
        };

        // Act
        var result = settings.Validate();

        // Assert
        result.IsValid.Should().Be(expectedValid);
    }

    [Theory]
    [InlineData(-1, false)]
    [InlineData(0, true)]
    [InlineData(1000, true)]
    [InlineData(5000, true)]
    public void Validate_Intervals_ShouldReturnExpectedResult(int interval, bool expectedValid)
    {
        // Arrange
        var settings = new SerialPortSettings
        {
            RetryInterval = interval,
            AutoReconnectInterval = interval
        };

        // Act
        var result = settings.Validate();

        // Assert
        result.IsValid.Should().Be(expectedValid);
    }

    [Theory]
    [InlineData(0, false)]
    [InlineData(-1, false)]
    [InlineData(1000, true)]
    [InlineData(10000, true)]
    public void Validate_ConnectionTimeout_ShouldReturnExpectedResult(int connectionTimeout, bool expectedValid)
    {
        // Arrange
        var settings = new SerialPortSettings
        {
            ConnectionTimeout = connectionTimeout
        };

        // Act
        var result = settings.Validate();

        // Assert
        result.IsValid.Should().Be(expectedValid);
    }

    [Theory]
    [InlineData(-1, false)]
    [InlineData(0, true)]
    [InlineData(1000, true)]
    [InlineData(30000, true)]
    public void Validate_HeartbeatInterval_ShouldReturnExpectedResult(int heartbeatInterval, bool expectedValid)
    {
        // Arrange
        var settings = new SerialPortSettings
        {
            HeartbeatInterval = heartbeatInterval
        };

        // Act
        var result = settings.Validate();

        // Assert
        result.IsValid.Should().Be(expectedValid);
    }

    [Theory]
    [InlineData(0, false)]
    [InlineData(-1, false)]
    [InlineData(1024, true)]
    [InlineData(1024 * 1024, true)]
    public void Validate_MaxLogSize_ShouldReturnExpectedResult(long maxLogSize, bool expectedValid)
    {
        // Arrange
        var settings = new SerialPortSettings
        {
            MaxLogSize = maxLogSize
        };

        // Act
        var result = settings.Validate();

        // Assert
        result.IsValid.Should().Be(expectedValid);
    }

    [Fact]
    public void Validate_ValidSettings_ShouldReturnValidResult()
    {
        // Arrange
        var settings = CreateTestSettings();

        // Act
        var result = settings.Validate();

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void Validate_InvalidSettings_ShouldReturnInvalidResultWithErrors()
    {
        // Arrange
        var settings = new SerialPortSettings
        {
            BaudRate = -1,
            DataBits = 10,
            ReadTimeout = -1,
            WriteTimeout = -1,
            ReceiveBufferSize = 0,
            SendBufferSize = 0,
            RetryCount = -1,
            RetryInterval = -1,
            AutoReconnectInterval = -1,
            ConnectionTimeout = 0,
            HeartbeatInterval = -1,
            MaxLogSize = 0
        };

        // Act
        var result = settings.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().NotBeEmpty();
        result.Errors.Should().Contain("波特率必须大于0");
        result.Errors.Should().Contain("数据位必须在5-8之间");
        result.Errors.Should().Contain("读取超时时间不能为负数");
        result.Errors.Should().Contain("写入超时时间不能为负数");
        result.Errors.Should().Contain("接收缓冲区大小必须大于0");
        result.Errors.Should().Contain("发送缓冲区大小必须大于0");
        result.Errors.Should().Contain("重试次数不能为负数");
        result.Errors.Should().Contain("重试间隔不能为负数");
        result.Errors.Should().Contain("自动重连间隔不能为负数");
        result.Errors.Should().Contain("连接超时时间必须大于0");
        result.Errors.Should().Contain("心跳检测间隔不能为负数");
        result.Errors.Should().Contain("日志最大大小必须大于0");
    }

    [Fact]
    public void Clone_ShouldCreateDeepCopy()
    {
        // Arrange
        var original = CreateTestSettings(115200, true);
        original.HeartbeatData = new byte[] { 0x01, 0x02, 0x03 };

        // Act
        var cloned = original.Clone();

        // Assert
        cloned.Should().NotBeSameAs(original);
        cloned.BaudRate.Should().Be(original.BaudRate);
        cloned.DataBits.Should().Be(original.DataBits);
        cloned.StopBits.Should().Be(original.StopBits);
        cloned.Parity.Should().Be(original.Parity);
        cloned.Handshake.Should().Be(original.Handshake);
        cloned.ReadTimeout.Should().Be(original.ReadTimeout);
        cloned.WriteTimeout.Should().Be(original.WriteTimeout);
        cloned.ReceiveBufferSize.Should().Be(original.ReceiveBufferSize);
        cloned.SendBufferSize.Should().Be(original.SendBufferSize);
        cloned.DtrEnable.Should().Be(original.DtrEnable);
        cloned.RtsEnable.Should().Be(original.RtsEnable);
        cloned.NewLine.Should().Be(original.NewLine);
        cloned.Encoding.Should().Be(original.Encoding);
        cloned.DiscardNull.Should().Be(original.DiscardNull);
        cloned.RetryCount.Should().Be(original.RetryCount);
        cloned.RetryInterval.Should().Be(original.RetryInterval);
        cloned.AutoReconnect.Should().Be(original.AutoReconnect);
        cloned.AutoReconnectInterval.Should().Be(original.AutoReconnectInterval);
        cloned.MaxAutoReconnectAttempts.Should().Be(original.MaxAutoReconnectAttempts);
        cloned.ConnectionTimeout.Should().Be(original.ConnectionTimeout);
        cloned.HeartbeatInterval.Should().Be(original.HeartbeatInterval);
        cloned.HeartbeatData.Should().BeEquivalentTo(original.HeartbeatData);
        cloned.HeartbeatData.Should().NotBeSameAs(original.HeartbeatData);
        cloned.EnableDataLogging.Should().Be(original.EnableDataLogging);
        cloned.MaxLogSize.Should().Be(original.MaxLogSize);
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var settings = CreateTestSettings(115200);

        // Act
        var result = settings.ToString();

        // Assert
        result.Should().Contain("BaudRate=115200");
        result.Should().Contain("DataBits=8");
        result.Should().Contain("StopBits=One");
        result.Should().Contain("Parity=None");
        result.Should().Contain("Handshake=None");
    }
}
