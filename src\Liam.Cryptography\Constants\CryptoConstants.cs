namespace Liam.Cryptography.Constants;

/// <summary>
/// 加密相关常量
/// </summary>
public static class CryptoConstants
{
    /// <summary>
    /// 默认密钥长度
    /// </summary>
    public static class KeySizes
    {
        /// <summary>
        /// AES默认密钥长度（位）
        /// </summary>
        public const int AES_DEFAULT = 256;

        /// <summary>
        /// RSA默认密钥长度（位）
        /// </summary>
        public const int RSA_DEFAULT = 2048;

        /// <summary>
        /// RSA最小密钥长度（位）
        /// </summary>
        public const int RSA_MINIMUM = 1024;

        /// <summary>
        /// RSA最大密钥长度（位）
        /// </summary>
        public const int RSA_MAXIMUM = 4096;

        /// <summary>
        /// DES密钥长度（位）
        /// </summary>
        public const int DES = 64;

        /// <summary>
        /// 3DES密钥长度（位）
        /// </summary>
        public const int TRIPLE_DES = 192;
    }

    /// <summary>
    /// 初始化向量长度
    /// </summary>
    public static class IVSizes
    {
        /// <summary>
        /// AES初始化向量长度（字节）
        /// </summary>
        public const int AES = 16;

        /// <summary>
        /// DES初始化向量长度（字节）
        /// </summary>
        public const int DES = 8;

        /// <summary>
        /// 3DES初始化向量长度（字节）
        /// </summary>
        public const int TRIPLE_DES = 8;
    }

    /// <summary>
    /// 算法名称
    /// </summary>
    public static class Algorithms
    {
        /// <summary>
        /// AES算法
        /// </summary>
        public const string AES = "AES";

        /// <summary>
        /// DES算法
        /// </summary>
        public const string DES = "DES";

        /// <summary>
        /// 3DES算法
        /// </summary>
        public const string TRIPLE_DES = "3DES";

        /// <summary>
        /// RSA算法
        /// </summary>
        public const string RSA = "RSA";

        /// <summary>
        /// MD5算法
        /// </summary>
        public const string MD5 = "MD5";

        /// <summary>
        /// SHA1算法
        /// </summary>
        public const string SHA1 = "SHA1";

        /// <summary>
        /// SHA256算法
        /// </summary>
        public const string SHA256 = "SHA256";

        /// <summary>
        /// SHA384算法
        /// </summary>
        public const string SHA384 = "SHA384";

        /// <summary>
        /// SHA512算法
        /// </summary>
        public const string SHA512 = "SHA512";
    }

    /// <summary>
    /// 错误消息
    /// </summary>
    public static class ErrorMessages
    {
        /// <summary>
        /// 无效的密钥长度
        /// </summary>
        public const string INVALID_KEY_SIZE = "无效的密钥长度";

        /// <summary>
        /// 无效的密钥格式
        /// </summary>
        public const string INVALID_KEY_FORMAT = "无效的密钥格式";

        /// <summary>
        /// 加密失败
        /// </summary>
        public const string ENCRYPTION_FAILED = "加密操作失败";

        /// <summary>
        /// 解密失败
        /// </summary>
        public const string DECRYPTION_FAILED = "解密操作失败";

        /// <summary>
        /// 签名失败
        /// </summary>
        public const string SIGNING_FAILED = "数字签名操作失败";

        /// <summary>
        /// 验证失败
        /// </summary>
        public const string VERIFICATION_FAILED = "签名验证失败";

        /// <summary>
        /// 密钥生成失败
        /// </summary>
        public const string KEY_GENERATION_FAILED = "密钥生成失败";

        /// <summary>
        /// 文件不存在
        /// </summary>
        public const string FILE_NOT_FOUND = "指定的文件不存在";

        /// <summary>
        /// 文件访问失败
        /// </summary>
        public const string FILE_ACCESS_FAILED = "文件访问失败";

        /// <summary>
        /// 不支持的算法
        /// </summary>
        public const string UNSUPPORTED_ALGORITHM = "不支持的算法类型";
    }

    /// <summary>
    /// 文件扩展名
    /// </summary>
    public static class FileExtensions
    {
        /// <summary>
        /// 私钥文件扩展名
        /// </summary>
        public const string PRIVATE_KEY = ".pem";

        /// <summary>
        /// 公钥文件扩展名
        /// </summary>
        public const string PUBLIC_KEY = ".pub";

        /// <summary>
        /// 密钥文件扩展名
        /// </summary>
        public const string KEY = ".key";

        /// <summary>
        /// 证书文件扩展名
        /// </summary>
        public const string CERTIFICATE = ".crt";
    }
}
