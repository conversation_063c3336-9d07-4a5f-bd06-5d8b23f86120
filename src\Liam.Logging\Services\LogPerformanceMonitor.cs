using Liam.Logging.Interfaces;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace Liam.Logging.Services;

/// <summary>
/// 日志性能监控器实现
/// </summary>
public class LogPerformanceMonitor : ILogPerformanceMonitor
{
    private readonly ConcurrentDictionary<string, ProviderPerformanceStats> _providerStats = new();
    private readonly object _lock = new();
    private readonly Stopwatch _uptime = Stopwatch.StartNew();

    /// <summary>
    /// 记录日志写入性能
    /// </summary>
    /// <param name="providerName">提供程序名称</param>
    /// <param name="duration">耗时</param>
    /// <param name="logCount">日志数量</param>
    public void RecordWritePerformance(string providerName, TimeSpan duration, int logCount)
    {
        var stats = _providerStats.GetOrAdd(providerName, _ => new ProviderPerformanceStats(providerName));
        stats.RecordWrite(duration, logCount);
    }

    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    /// <returns>性能统计信息</returns>
    public Dictionary<string, object> GetPerformanceStats()
    {
        lock (_lock)
        {
            var result = new Dictionary<string, object>
            {
                ["uptime"] = _uptime.Elapsed,
                ["totalProviders"] = _providerStats.Count,
                ["providers"] = new Dictionary<string, object>()
            };

            var providersDict = (Dictionary<string, object>)result["providers"];

            foreach (var kvp in _providerStats)
            {
                providersDict[kvp.Key] = kvp.Value.GetStats();
            }

            // 计算全局统计
            var totalWrites = _providerStats.Values.Sum(s => s.TotalWrites);
            var totalLogs = _providerStats.Values.Sum(s => s.TotalLogs);
            var totalDuration = TimeSpan.FromTicks(_providerStats.Values.Sum(s => s.TotalDuration.Ticks));

            result["global"] = new Dictionary<string, object>
            {
                ["totalWrites"] = totalWrites,
                ["totalLogs"] = totalLogs,
                ["totalDuration"] = totalDuration,
                ["averageWriteTime"] = totalWrites > 0 ? TimeSpan.FromTicks(totalDuration.Ticks / totalWrites) : TimeSpan.Zero,
                ["logsPerSecond"] = _uptime.Elapsed.TotalSeconds > 0 ? totalLogs / _uptime.Elapsed.TotalSeconds : 0
            };

            return result;
        }
    }

    /// <summary>
    /// 重置性能统计
    /// </summary>
    public void Reset()
    {
        lock (_lock)
        {
            _providerStats.Clear();
            _uptime.Restart();
        }
    }

    /// <summary>
    /// 提供程序性能统计
    /// </summary>
    private class ProviderPerformanceStats
    {
        private readonly string _providerName;
        private readonly object _lock = new();
        private long _totalWrites;
        private long _totalLogs;
        private TimeSpan _totalDuration;
        private TimeSpan _minDuration = TimeSpan.MaxValue;
        private TimeSpan _maxDuration = TimeSpan.MinValue;
        private readonly Queue<WriteRecord> _recentWrites = new();
        private const int MaxRecentRecords = 100;

        public long TotalWrites => _totalWrites;
        public long TotalLogs => _totalLogs;
        public TimeSpan TotalDuration => _totalDuration;

        public ProviderPerformanceStats(string providerName)
        {
            _providerName = providerName;
        }

        public void RecordWrite(TimeSpan duration, int logCount)
        {
            lock (_lock)
            {
                _totalWrites++;
                _totalLogs += logCount;
                _totalDuration = _totalDuration.Add(duration);

                if (duration < _minDuration)
                    _minDuration = duration;
                if (duration > _maxDuration)
                    _maxDuration = duration;

                // 记录最近的写入
                _recentWrites.Enqueue(new WriteRecord(DateTime.UtcNow, duration, logCount));
                while (_recentWrites.Count > MaxRecentRecords)
                {
                    _recentWrites.Dequeue();
                }
            }
        }

        public Dictionary<string, object> GetStats()
        {
            lock (_lock)
            {
                var avgDuration = _totalWrites > 0 ? TimeSpan.FromTicks(_totalDuration.Ticks / _totalWrites) : TimeSpan.Zero;
                var avgLogsPerWrite = _totalWrites > 0 ? (double)_totalLogs / _totalWrites : 0;

                // 计算最近1分钟的统计
                var oneMinuteAgo = DateTime.UtcNow.AddMinutes(-1);
                var recentWrites = _recentWrites.Where(r => r.Timestamp >= oneMinuteAgo).ToList();
                var recentTotalDuration = TimeSpan.FromTicks(recentWrites.Sum(r => r.Duration.Ticks));
                var recentTotalLogs = recentWrites.Sum(r => r.LogCount);

                return new Dictionary<string, object>
                {
                    ["providerName"] = _providerName,
                    ["totalWrites"] = _totalWrites,
                    ["totalLogs"] = _totalLogs,
                    ["totalDuration"] = _totalDuration,
                    ["averageDuration"] = avgDuration,
                    ["minDuration"] = _minDuration == TimeSpan.MaxValue ? TimeSpan.Zero : _minDuration,
                    ["maxDuration"] = _maxDuration == TimeSpan.MinValue ? TimeSpan.Zero : _maxDuration,
                    ["averageLogsPerWrite"] = avgLogsPerWrite,
                    ["recent"] = new Dictionary<string, object>
                    {
                        ["writes"] = recentWrites.Count,
                        ["logs"] = recentTotalLogs,
                        ["duration"] = recentTotalDuration,
                        ["logsPerSecond"] = recentTotalLogs / 60.0
                    }
                };
            }
        }

        private record WriteRecord(DateTime Timestamp, TimeSpan Duration, int LogCount);
    }
}

/// <summary>
/// 空的性能监控器（用于禁用性能监控）
/// </summary>
public class NullLogPerformanceMonitor : ILogPerformanceMonitor
{
    /// <summary>
    /// 单例实例
    /// </summary>
    public static readonly NullLogPerformanceMonitor Instance = new();

    private NullLogPerformanceMonitor() { }

    /// <summary>
    /// 记录日志写入性能（空实现）
    /// </summary>
    /// <param name="providerName">提供程序名称</param>
    /// <param name="duration">耗时</param>
    /// <param name="logCount">日志数量</param>
    public void RecordWritePerformance(string providerName, TimeSpan duration, int logCount)
    {
        // 空实现
    }

    /// <summary>
    /// 获取性能统计信息（空实现）
    /// </summary>
    /// <returns>空的统计信息</returns>
    public Dictionary<string, object> GetPerformanceStats()
    {
        return new Dictionary<string, object>
        {
            ["enabled"] = false,
            ["message"] = "Performance monitoring is disabled"
        };
    }
}

/// <summary>
/// 性能监控扩展方法
/// </summary>
public static class PerformanceMonitorExtensions
{
    /// <summary>
    /// 测量执行时间并记录性能
    /// </summary>
    /// <param name="monitor">性能监控器</param>
    /// <param name="providerName">提供程序名称</param>
    /// <param name="action">要执行的操作</param>
    /// <param name="logCount">日志数量</param>
    public static void MeasureAndRecord(this ILogPerformanceMonitor monitor, string providerName, Action action, int logCount = 1)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            action();
        }
        finally
        {
            stopwatch.Stop();
            monitor.RecordWritePerformance(providerName, stopwatch.Elapsed, logCount);
        }
    }

    /// <summary>
    /// 异步测量执行时间并记录性能
    /// </summary>
    /// <param name="monitor">性能监控器</param>
    /// <param name="providerName">提供程序名称</param>
    /// <param name="asyncAction">要执行的异步操作</param>
    /// <param name="logCount">日志数量</param>
    /// <returns>异步任务</returns>
    public static async Task MeasureAndRecordAsync(this ILogPerformanceMonitor monitor, string providerName, Func<Task> asyncAction, int logCount = 1)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            await asyncAction();
        }
        finally
        {
            stopwatch.Stop();
            monitor.RecordWritePerformance(providerName, stopwatch.Elapsed, logCount);
        }
    }

    /// <summary>
    /// 测量执行时间并记录性能（带返回值）
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="monitor">性能监控器</param>
    /// <param name="providerName">提供程序名称</param>
    /// <param name="func">要执行的函数</param>
    /// <param name="logCount">日志数量</param>
    /// <returns>函数返回值</returns>
    public static T MeasureAndRecord<T>(this ILogPerformanceMonitor monitor, string providerName, Func<T> func, int logCount = 1)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            return func();
        }
        finally
        {
            stopwatch.Stop();
            monitor.RecordWritePerformance(providerName, stopwatch.Elapsed, logCount);
        }
    }

    /// <summary>
    /// 异步测量执行时间并记录性能（带返回值）
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="monitor">性能监控器</param>
    /// <param name="providerName">提供程序名称</param>
    /// <param name="asyncFunc">要执行的异步函数</param>
    /// <param name="logCount">日志数量</param>
    /// <returns>异步任务</returns>
    public static async Task<T> MeasureAndRecordAsync<T>(this ILogPerformanceMonitor monitor, string providerName, Func<Task<T>> asyncFunc, int logCount = 1)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            return await asyncFunc();
        }
        finally
        {
            stopwatch.Stop();
            monitor.RecordWritePerformance(providerName, stopwatch.Elapsed, logCount);
        }
    }
}
