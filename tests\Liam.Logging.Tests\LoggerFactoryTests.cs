using FluentAssertions;
using Liam.Logging.Constants;
using Liam.Logging.Extensions;
using Liam.Logging.Interfaces;
using Liam.Logging.Models;
using Liam.Logging.Providers;
using Liam.Logging.Services;
using Moq;
using Xunit;

namespace Liam.Logging.Tests;

/// <summary>
/// 日志工厂测试
/// </summary>
public class LoggerFactoryTests
{
    [Fact]
    public void Constructor_ShouldCreateFactoryWithDefaultConfiguration()
    {
        // Arrange & Act
        var factory = new LiamLoggerFactory();

        // Assert
        factory.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_WithConfiguration_ShouldUseProvidedConfiguration()
    {
        // Arrange
        var config = new LogConfiguration
        {
            MinimumLevel = LogLevel.Warning,
            EnableAsync = false
        };

        // Act
        var factory = new LiamLoggerFactory(config);

        // Assert
        factory.Should().NotBeNull();
    }

    [Fact]
    public void CreateLogger_ShouldReturnLoggerWithCorrectCategory()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        var categoryName = "TestCategory";

        // Act
        var logger = factory.CreateLogger(categoryName);

        // Assert
        logger.Should().NotBeNull();
        logger.CategoryName.Should().Be(categoryName);
    }

    [Fact]
    public void CreateLogger_WithSameCategory_ShouldReturnSameInstance()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        var categoryName = "TestCategory";

        // Act
        var logger1 = factory.CreateLogger(categoryName);
        var logger2 = factory.CreateLogger(categoryName);

        // Assert
        logger1.Should().BeSameAs(logger2);
    }

    [Fact]
    public void CreateGenericLogger_ShouldReturnLoggerWithTypeCategory()
    {
        // Arrange
        var factory = new LiamLoggerFactory();

        // Act
        var logger = factory.CreateLogger<LoggerFactoryTests>();

        // Assert
        logger.Should().NotBeNull();
        logger.CategoryName.Should().Be(typeof(LoggerFactoryTests).FullName);
    }

    [Fact]
    public void AddProvider_ShouldAddProviderToCollection()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        var mockProvider = new Mock<ILogProvider>();
        mockProvider.Setup(x => x.Name).Returns("TestProvider");

        // Act
        factory.AddProvider(mockProvider.Object);

        // Assert
        var providers = factory.GetProviders();
        providers.Should().Contain(mockProvider.Object);
    }

    [Fact]
    public void AddProvider_WithNullProvider_ShouldThrowArgumentNullException()
    {
        // Arrange
        var factory = new LiamLoggerFactory();

        // Act & Assert
        var act = () => factory.AddProvider(null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void RemoveProvider_ShouldRemoveProviderFromCollection()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        var mockProvider = new Mock<ILogProvider>();
        mockProvider.Setup(x => x.Name).Returns("TestProvider");

        factory.AddProvider(mockProvider.Object);

        // Act
        factory.RemoveProvider(mockProvider.Object);

        // Assert
        var providers = factory.GetProviders();
        providers.Should().NotContain(mockProvider.Object);
    }

    [Fact]
    public void GetProviders_ShouldReturnAllAddedProviders()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        var mockProvider1 = new Mock<ILogProvider>();
        var mockProvider2 = new Mock<ILogProvider>();
        mockProvider1.Setup(x => x.Name).Returns("Provider1");
        mockProvider2.Setup(x => x.Name).Returns("Provider2");

        factory.AddProvider(mockProvider1.Object);
        factory.AddProvider(mockProvider2.Object);

        // Act
        var providers = factory.GetProviders();

        // Assert
        providers.Should().HaveCount(2);
        providers.Should().Contain(mockProvider1.Object);
        providers.Should().Contain(mockProvider2.Object);
    }

    [Fact]
    public void Flush_ShouldCallFlushOnAllProviders()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        var mockProvider1 = new Mock<ILogProvider>();
        var mockProvider2 = new Mock<ILogProvider>();

        factory.AddProvider(mockProvider1.Object);
        factory.AddProvider(mockProvider2.Object);

        // Act
        factory.Flush();

        // Assert
        mockProvider1.Verify(x => x.Flush(), Times.Once);
        mockProvider2.Verify(x => x.Flush(), Times.Once);
    }

    [Fact]
    public async Task FlushAsync_ShouldCallFlushAsyncOnAllProviders()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        var mockProvider1 = new Mock<ILogProvider>();
        var mockProvider2 = new Mock<ILogProvider>();

        factory.AddProvider(mockProvider1.Object);
        factory.AddProvider(mockProvider2.Object);

        // Act
        await factory.FlushAsync();

        // Assert
        mockProvider1.Verify(x => x.FlushAsync(It.IsAny<CancellationToken>()), Times.Once);
        mockProvider2.Verify(x => x.FlushAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void GetFactoryStats_ShouldReturnCorrectStatistics()
    {
        // Arrange
        var config = new LogConfiguration
        {
            MinimumLevel = LogLevel.Debug,
            EnableAsync = true,
            AsyncQueueSize = 5000
        };
        var factory = new LiamLoggerFactory(config);
        var mockProvider = new Mock<ILogProvider>();
        mockProvider.Setup(x => x.IsEnabled).Returns(true);

        factory.AddProvider(mockProvider.Object);
        factory.CreateLogger("TestLogger1");
        factory.CreateLogger("TestLogger2");

        // Act
        var stats = factory.GetFactoryStats();

        // Assert
        stats.Should().ContainKey("totalLoggers");
        stats.Should().ContainKey("totalProviders");
        stats.Should().ContainKey("enabledProviders");
        stats.Should().ContainKey("configuration");

        stats["totalLoggers"].Should().Be(2);
        stats["totalProviders"].Should().Be(1);
        stats["enabledProviders"].Should().Be(1);
    }

    [Fact]
    public void Reconfigure_ShouldUpdateConfigurationAndLoggers()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        var logger = factory.CreateLogger("TestLogger");
        
        var newConfig = new LogConfiguration
        {
            MinimumLevel = LogLevel.Error,
            EnableAsync = false
        };

        // Act
        factory.Reconfigure(newConfig);

        // Assert
        logger.MinimumLevel.Should().Be(LogLevel.Error);
    }

    [Fact]
    public void Reconfigure_WithNullConfiguration_ShouldThrowArgumentNullException()
    {
        // Arrange
        var factory = new LiamLoggerFactory();

        // Act & Assert
        var act = () => factory.Reconfigure(null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void ClearLoggers_ShouldRemoveAllCachedLoggers()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        factory.CreateLogger("Logger1");
        factory.CreateLogger("Logger2");

        var statsBefore = factory.GetFactoryStats();
        statsBefore["totalLoggers"].Should().Be(2);

        // Act
        factory.ClearLoggers();

        // Assert
        var statsAfter = factory.GetFactoryStats();
        statsAfter["totalLoggers"].Should().Be(0);
    }

    [Fact]
    public void Dispose_ShouldDisposeAllProviders()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        var mockProvider1 = new Mock<ILogProvider>();
        var mockProvider2 = new Mock<ILogProvider>();

        factory.AddProvider(mockProvider1.Object);
        factory.AddProvider(mockProvider2.Object);

        // Act
        factory.Dispose();

        // Assert
        mockProvider1.Verify(x => x.Dispose(), Times.Once);
        mockProvider2.Verify(x => x.Dispose(), Times.Once);
    }

    [Fact]
    public void Dispose_AfterDispose_ShouldThrowObjectDisposedException()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        factory.Dispose();

        // Act & Assert
        var act = () => factory.CreateLogger("TestLogger");
        act.Should().Throw<ObjectDisposedException>();
    }

    [Fact]
    public void CreateLogger_WithMinimumLevelConfiguration_ShouldSetCorrectLevel()
    {
        // Arrange
        var config = new LogConfiguration
        {
            MinimumLevel = LogLevel.Warning
        };
        var factory = new LiamLoggerFactory(config);

        // Act
        var logger = factory.CreateLogger("TestLogger");

        // Assert
        logger.MinimumLevel.Should().Be(LogLevel.Warning);
    }

    [Fact]
    public void Integration_LoggerShouldWriteToAddedProviders()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        var mockProvider = new Mock<ILogProvider>();
        mockProvider.Setup(x => x.IsEnabled).Returns(true);

        factory.AddProvider(mockProvider.Object);
        var logger = factory.CreateLogger("IntegrationTest");

        // Act
        logger.LogInformation("Test message");

        // Assert
        mockProvider.Verify(x => x.WriteLog(It.Is<LogEvent>(e => 
            e.Message == "Test message" && 
            e.Level == LogLevel.Information)), Times.Once);
    }

    [Fact]
    public async Task Integration_LoggerShouldWriteToAddedProvidersAsync()
    {
        // Arrange
        var factory = new LiamLoggerFactory();
        var mockProvider = new Mock<ILogProvider>();
        mockProvider.Setup(x => x.IsEnabled).Returns(true);

        factory.AddProvider(mockProvider.Object);
        var logger = factory.CreateLogger("AsyncIntegrationTest");

        // Act
        await logger.LogInformationAsync("Async test message");

        // Assert
        mockProvider.Verify(x => x.WriteLogAsync(It.Is<LogEvent>(e => 
            e.Message == "Async test message" && 
            e.Level == LogLevel.Information), It.IsAny<CancellationToken>()), Times.Once);
    }
}
