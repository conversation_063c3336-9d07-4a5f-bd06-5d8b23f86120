using Microsoft.Extensions.Logging;
using Liam.TcpServer.Constants;
using Liam.TcpServer.Interfaces;
using Liam.TcpServer.Models;

namespace Liam.TcpServer.Handlers;

/// <summary>
/// 默认消息处理器实现
/// </summary>
public class MessageHandler : IMessageHandler
{
    private readonly ILogger<MessageHandler>? _logger;
    private readonly IMessageHandlerRegistry? _handlerRegistry;

    /// <summary>
    /// 初始化消息处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="handlerRegistry">消息处理器注册表</param>
    public MessageHandler(ILogger<MessageHandler>? logger = null, IMessageHandlerRegistry? handlerRegistry = null)
    {
        _logger = logger;
        _handlerRegistry = handlerRegistry;
    }

    /// <summary>
    /// 处理接收到的消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">接收到的消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public virtual async Task HandleMessageAsync(ClientConnection connection, TcpMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(connection);
        ArgumentNullException.ThrowIfNull(message);

        try
        {
            _logger?.LogDebug("处理来自连接 {ConnectionId} 的消息 {MessageId}，类型: {MessageType}", 
                connection.Id, message.Id, message.MessageType);

            // 验证消息
            var validationResult = ValidateMessage(connection, message);
            if (!validationResult.IsValid)
            {
                _logger?.LogWarning("消息验证失败: {ErrorMessage}", validationResult.ErrorMessage);
                await SendErrorResponseAsync(connection, validationResult.ErrorMessage ?? "消息验证失败", cancellationToken);
                return;
            }

            // 更新连接活动时间
            connection.UpdateLastActivity();

            // 根据消息类型处理
            switch (message.MessageType)
            {
                case TcpServerConstants.MessageTypes.Data:
                    await HandleDataMessageAsync(connection, message, cancellationToken);
                    break;

                case TcpServerConstants.MessageTypes.HeartbeatRequest:
                    await HandleHeartbeatAsync(connection, true, cancellationToken);
                    break;

                case TcpServerConstants.MessageTypes.HeartbeatResponse:
                    await HandleHeartbeatAsync(connection, false, cancellationToken);
                    break;

                case TcpServerConstants.MessageTypes.ConnectionAck:
                    await HandleConnectionAckAsync(connection, message, cancellationToken);
                    break;

                case TcpServerConstants.MessageTypes.Disconnect:
                    await HandleDisconnectMessageAsync(connection, message, cancellationToken);
                    break;

                case TcpServerConstants.MessageTypes.Error:
                    await HandleErrorMessageAsync(connection, message, cancellationToken);
                    break;

                default:
                    await HandleCustomMessageAsync(connection, message, cancellationToken);
                    break;
            }

            _logger?.LogDebug("消息 {MessageId} 处理完成", message.Id);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理消息 {MessageId} 时发生异常", message.Id);
            await HandleErrorAsync(connection, ex, cancellationToken);
        }
    }

    /// <summary>
    /// 处理连接建立
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public virtual async Task HandleConnectionAsync(ClientConnection connection, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(connection);

        try
        {
            _logger?.LogInformation("处理新连接 {ConnectionId} from {ClientAddress}", 
                connection.Id, connection.ClientIpAddress);

            // 发送连接确认消息
            var ackMessage = TcpMessage.CreateConnectionAck();
            await SendMessageAsync(connection, ackMessage, cancellationToken);

            _logger?.LogDebug("连接 {ConnectionId} 处理完成", connection.Id);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理连接 {ConnectionId} 时发生异常", connection.Id);
            await HandleErrorAsync(connection, ex, cancellationToken);
        }
    }

    /// <summary>
    /// 处理连接断开
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public virtual async Task HandleDisconnectionAsync(ClientConnection connection, string? reason = null, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(connection);

        try
        {
            _logger?.LogInformation("处理连接 {ConnectionId} 断开，原因: {Reason}", 
                connection.Id, reason ?? "未指定");

            // 清理连接相关资源
            await CleanupConnectionResourcesAsync(connection, cancellationToken);

            _logger?.LogDebug("连接 {ConnectionId} 断开处理完成", connection.Id);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理连接 {ConnectionId} 断开时发生异常", connection.Id);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理心跳消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="isRequest">是否为心跳请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public virtual async Task HandleHeartbeatAsync(ClientConnection connection, bool isRequest, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(connection);

        try
        {
            _logger?.LogDebug("处理连接 {ConnectionId} 的心跳{Type}", 
                connection.Id, isRequest ? "请求" : "响应");

            // 更新心跳时间
            connection.UpdateLastHeartbeat();

            // 如果是心跳请求，发送心跳响应
            if (isRequest)
            {
                var responseMessage = TcpMessage.CreateHeartbeatResponse();
                await SendMessageAsync(connection, responseMessage, cancellationToken);
            }

            _logger?.LogDebug("心跳处理完成");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理心跳时发生异常");
            await HandleErrorAsync(connection, ex, cancellationToken);
        }
    }

    /// <summary>
    /// 处理错误
    /// </summary>
    /// <param name="connection">客户端连接（可选）</param>
    /// <param name="exception">异常信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public virtual async Task HandleErrorAsync(ClientConnection? connection, Exception exception, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(exception);

        try
        {
            _logger?.LogError(exception, "处理错误: {ErrorMessage}", exception.Message);

            if (connection != null)
            {
                // 发送错误消息给客户端
                await SendErrorResponseAsync(connection, exception.Message, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理错误时发生异常");
        }
    }

    /// <summary>
    /// 验证消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">消息</param>
    /// <returns>验证结果</returns>
    public virtual MessageValidationResult ValidateMessage(ClientConnection connection, TcpMessage message)
    {
        ArgumentNullException.ThrowIfNull(connection);
        ArgumentNullException.ThrowIfNull(message);

        // 检查消息长度
        if (message.Length > TcpServerConstants.Defaults.MaxMessageLength)
        {
            return MessageValidationResult.Failure($"消息长度超过限制: {message.Length} > {TcpServerConstants.Defaults.MaxMessageLength}");
        }

        // 检查连接状态
        if (!connection.IsConnected)
        {
            return MessageValidationResult.Failure("连接已断开");
        }

        // 检查认证状态（如果需要认证）
        // 注意：这里应该检查服务器的安全设置，但为了简化示例，我们暂时跳过认证检查
        // if (RequiresAuthentication(message) && !connection.IsAuthenticated)
        // {
        //     return MessageValidationResult.Failure("需要认证");
        // }

        return MessageValidationResult.Success();
    }

    /// <summary>
    /// 处理认证消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">认证消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证结果</returns>
    public virtual Task<AuthenticationResult> HandleAuthenticationAsync(ClientConnection connection, TcpMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(connection);
        ArgumentNullException.ThrowIfNull(message);

        try
        {
            _logger?.LogDebug("处理连接 {ConnectionId} 的认证请求", connection.Id);

            // 简单的基于密钥的认证示例
            var authKey = message.GetText();
            if (string.IsNullOrEmpty(authKey))
            {
                return Task.FromResult(AuthenticationResult.Failure("认证密钥不能为空"));
            }

            // 这里应该实现实际的认证逻辑
            // 示例：检查预设的认证密钥
            if (authKey == "default-auth-key")
            {
                connection.SetAuthentication(true, "authenticated-user");
                _logger?.LogInformation("连接 {ConnectionId} 认证成功", connection.Id);
                return Task.FromResult(AuthenticationResult.Success("authenticated-user"));
            }

            _logger?.LogWarning("连接 {ConnectionId} 认证失败", connection.Id);
            return Task.FromResult(AuthenticationResult.Failure("认证密钥无效"));
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理认证时发生异常");
            return Task.FromResult(AuthenticationResult.Failure($"认证处理异常: {ex.Message}"));
        }
    }

    /// <summary>
    /// 处理数据消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">数据消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    protected virtual async Task HandleDataMessageAsync(ClientConnection connection, TcpMessage message, CancellationToken cancellationToken = default)
    {
        _logger?.LogDebug("处理数据消息，长度: {Length}", message.Length);
        
        // 默认实现：记录接收到的数据
        connection.Statistics.RecordReceived(message.Length);
        
        // 这里可以添加自定义的数据处理逻辑
        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理自定义消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">自定义消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    protected virtual async Task HandleCustomMessageAsync(ClientConnection connection, TcpMessage message, CancellationToken cancellationToken = default)
    {
        // 尝试使用注册的自定义处理器
        if (_handlerRegistry?.HasHandler(message.MessageType) == true)
        {
            var handler = _handlerRegistry.GetHandler(message.MessageType);
            if (handler != null)
            {
                // 这里需要根据具体的处理器类型进行处理
                // 由于泛型限制，这里只是示例
                _logger?.LogDebug("使用自定义处理器处理消息类型 {MessageType}", message.MessageType);
            }
        }
        else
        {
            _logger?.LogWarning("未知的消息类型: {MessageType}", message.MessageType);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理连接确认消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">连接确认消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    protected virtual async Task HandleConnectionAckAsync(ClientConnection connection, TcpMessage message, CancellationToken cancellationToken = default)
    {
        _logger?.LogDebug("收到连接确认消息");
        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理断开连接消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">断开连接消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    protected virtual async Task HandleDisconnectMessageAsync(ClientConnection connection, TcpMessage message, CancellationToken cancellationToken = default)
    {
        var reason = message.GetText();
        _logger?.LogInformation("收到断开连接消息，原因: {Reason}", reason);
        
        connection.Disconnect();
        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理错误消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">错误消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    protected virtual async Task HandleErrorMessageAsync(ClientConnection connection, TcpMessage message, CancellationToken cancellationToken = default)
    {
        var errorText = message.GetText();
        _logger?.LogWarning("收到错误消息: {ErrorMessage}", errorText);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    protected virtual async Task SendMessageAsync(ClientConnection connection, TcpMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            var data = message.Serialize();
            await connection.NetworkStream.WriteAsync(data, cancellationToken);
            await connection.NetworkStream.FlushAsync(cancellationToken);
            
            connection.Statistics.RecordSent(data.Length);
            message.SentAt = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            connection.Statistics.RecordSendError();
            _logger?.LogError(ex, "发送消息失败");
            throw;
        }
    }

    /// <summary>
    /// 发送错误响应
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    protected virtual async Task SendErrorResponseAsync(ClientConnection connection, string errorMessage, CancellationToken cancellationToken = default)
    {
        try
        {
            var errorMsg = TcpMessage.CreateErrorMessage(errorMessage);
            await SendMessageAsync(connection, errorMsg, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "发送错误响应失败");
        }
    }

    /// <summary>
    /// 清理连接资源
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    protected virtual Task CleanupConnectionResourcesAsync(ClientConnection connection, CancellationToken cancellationToken = default)
    {
        // 这里可以添加连接断开时的清理逻辑
        return Task.CompletedTask;
    }

    /// <summary>
    /// 检查消息是否需要认证
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns>是否需要认证</returns>
    protected virtual bool RequiresAuthentication(TcpMessage message)
    {
        // 心跳消息和连接确认消息不需要认证
        return message.MessageType != TcpServerConstants.MessageTypes.HeartbeatRequest &&
               message.MessageType != TcpServerConstants.MessageTypes.HeartbeatResponse &&
               message.MessageType != TcpServerConstants.MessageTypes.ConnectionAck;
    }
}
