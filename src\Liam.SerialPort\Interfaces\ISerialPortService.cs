using Liam.SerialPort.Events;
using <PERSON>.SerialPort.Models;

namespace Liam.SerialPort.Interfaces;

/// <summary>
/// 串口服务主接口，提供串口通讯的核心功能
/// </summary>
public interface ISerialPortService : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 获取当前连接状态
    /// </summary>
    ConnectionStatus Status { get; }

    /// <summary>
    /// 获取当前串口设置
    /// </summary>
    SerialPortSettings? Settings { get; }

    /// <summary>
    /// 获取当前连接的串口信息
    /// </summary>
    SerialPortInfo? CurrentPort { get; }

    /// <summary>
    /// 获取是否已连接
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// 获取是否启用自动重连
    /// </summary>
    bool AutoReconnectEnabled { get; set; }

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    event EventHandler<ConnectionStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    event EventHandler<DataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 错误发生事件
    /// </summary>
    event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 设备热插拔事件
    /// </summary>
    event EventHandler<DeviceChangedEventArgs>? DeviceChanged;

    /// <summary>
    /// 获取可用的串口列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>串口信息列表</returns>
    Task<IEnumerable<SerialPortInfo>> GetAvailablePortsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 连接到指定串口
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="settings">串口设置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否成功</returns>
    Task<bool> ConnectAsync(string portName, SerialPortSettings settings, CancellationToken cancellationToken = default);

    /// <summary>
    /// 连接到指定串口
    /// </summary>
    /// <param name="portInfo">串口信息</param>
    /// <param name="settings">串口设置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否成功</returns>
    Task<bool> ConnectAsync(SerialPortInfo portInfo, SerialPortSettings settings, CancellationToken cancellationToken = default);

    /// <summary>
    /// 断开连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task DisconnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据（字节数组）
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task SendAsync(byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据（字符串）
    /// </summary>
    /// <param name="data">要发送的字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task SendAsync(string data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据并等待响应
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收到的响应数据</returns>
    Task<byte[]> SendAndReceiveAsync(byte[] data, TimeSpan timeout, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据并等待响应
    /// </summary>
    /// <param name="data">要发送的字符串</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收到的响应字符串</returns>
    Task<string> SendAndReceiveAsync(string data, TimeSpan timeout, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清空接收缓冲区
    /// </summary>
    void ClearReceiveBuffer();

    /// <summary>
    /// 清空发送缓冲区
    /// </summary>
    void ClearSendBuffer();

    /// <summary>
    /// 获取接收缓冲区中的字节数
    /// </summary>
    int BytesToRead { get; }

    /// <summary>
    /// 获取发送缓冲区中的字节数
    /// </summary>
    int BytesToWrite { get; }
}
