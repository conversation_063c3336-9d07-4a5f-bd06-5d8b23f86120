using Liam.Logging.Formatters;
using Liam.Logging.Interfaces;
using Liam.Logging.Models;
using Liam.Logging.Providers;
using Liam.Logging.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Liam.Logging.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加Liam日志记录服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddLiamLogging(this IServiceCollection services, LogConfiguration? configuration = null)
    {
        configuration ??= new LogConfiguration();

        // 注册核心服务
        services.TryAddSingleton<ILogScopeProvider, LogScopeProvider>();
        services.TryAddSingleton<ILogPerformanceMonitor, LogPerformanceMonitor>();
        services.TryAddSingleton(configuration);
        services.TryAddSingleton<ILiamLoggerFactory, LiamLoggerFactory>();

        // 注册泛型日志记录器
        services.TryAddTransient(typeof(ILiamLogger<>), typeof(LiamLogger<>));

        // 注册日志记录器工厂方法
        services.TryAddTransient<ILiamLogger>(provider =>
        {
            var factory = provider.GetRequiredService<ILiamLoggerFactory>();
            return factory.CreateLogger("Default");
        });

        return services;
    }

    /// <summary>
    /// 添加Liam日志记录服务（从配置文件）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置根</param>
    /// <param name="sectionName">配置节名称</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddLiamLogging(this IServiceCollection services, IConfiguration configuration, string sectionName = "Logging")
    {
        var logConfiguration = new LogConfiguration();
        configuration.GetSection(sectionName).Bind(logConfiguration);

        return services.AddLiamLogging(logConfiguration);
    }

    /// <summary>
    /// 添加控制台日志提供程序
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configure">配置委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddConsoleLogging(this IServiceCollection services, Action<ConsoleLogConfiguration>? configure = null)
    {
        var config = new ConsoleLogConfiguration();
        configure?.Invoke(config);

        services.AddSingleton<ILogProvider>(provider =>
        {
            ILogFormatter formatter = config.Settings.TryGetValue("FormatterType", out var formatterType) && formatterType?.ToString() == "Json"
                ? new JsonLogFormatter()
                : new TextLogFormatter(config.TimestampFormat, true, true, false);

            return new ConsoleLogProvider(formatter, config.EnableColors, config.UseStandardError);
        });

        return services;
    }

    /// <summary>
    /// 添加文件日志提供程序
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configure">配置委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddFileLogging(this IServiceCollection services, Action<FileLogConfiguration>? configure = null)
    {
        var config = new FileLogConfiguration();
        configure?.Invoke(config);

        services.AddSingleton<ILogProvider>(provider =>
        {
            ILogFormatter formatter = config.Settings.TryGetValue("FormatterType", out var formatterType) && formatterType?.ToString() == "Json"
                ? new JsonLogFormatter()
                : new TextLogFormatter();

            var encoding = System.Text.Encoding.GetEncoding(config.Encoding);

            return new FileLogProvider(
                config.FilePath,
                formatter,
                config.EnableRotation,
                config.MaxFileSize,
                config.RetainedFileCount,
                config.RotationInterval,
                encoding,
                config.AutoFlush,
                config.BufferSize);
        });

        return services;
    }

    /// <summary>
    /// 配置日志记录
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configure">配置委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ConfigureLiamLogging(this IServiceCollection services, Action<LoggingBuilder> configure)
    {
        var builder = new LoggingBuilder(services);
        configure(builder);
        return services;
    }
}

/// <summary>
/// 日志记录构建器
/// </summary>
public class LoggingBuilder
{
    /// <summary>
    /// 服务集合
    /// </summary>
    public IServiceCollection Services { get; }

    /// <summary>
    /// 初始化日志记录构建器
    /// </summary>
    /// <param name="services">服务集合</param>
    public LoggingBuilder(IServiceCollection services)
    {
        Services = services ?? throw new ArgumentNullException(nameof(services));
    }

    /// <summary>
    /// 添加控制台日志
    /// </summary>
    /// <param name="configure">配置委托</param>
    /// <returns>构建器</returns>
    public LoggingBuilder AddConsole(Action<ConsoleLogConfiguration>? configure = null)
    {
        Services.AddConsoleLogging(configure);
        return this;
    }

    /// <summary>
    /// 添加文件日志
    /// </summary>
    /// <param name="configure">配置委托</param>
    /// <returns>构建器</returns>
    public LoggingBuilder AddFile(Action<FileLogConfiguration>? configure = null)
    {
        Services.AddFileLogging(configure);
        return this;
    }

    /// <summary>
    /// 添加自定义提供程序
    /// </summary>
    /// <typeparam name="T">提供程序类型</typeparam>
    /// <returns>构建器</returns>
    public LoggingBuilder AddProvider<T>() where T : class, ILogProvider
    {
        Services.AddSingleton<ILogProvider, T>();
        return this;
    }

    /// <summary>
    /// 添加自定义提供程序
    /// </summary>
    /// <param name="provider">提供程序实例</param>
    /// <returns>构建器</returns>
    public LoggingBuilder AddProvider(ILogProvider provider)
    {
        Services.AddSingleton(provider);
        return this;
    }

    /// <summary>
    /// 添加自定义过滤器
    /// </summary>
    /// <typeparam name="T">过滤器类型</typeparam>
    /// <returns>构建器</returns>
    public LoggingBuilder AddFilter<T>() where T : class, ILogFilter
    {
        Services.AddSingleton<ILogFilter, T>();
        return this;
    }

    /// <summary>
    /// 添加自定义过滤器
    /// </summary>
    /// <param name="filter">过滤器实例</param>
    /// <returns>构建器</returns>
    public LoggingBuilder AddFilter(ILogFilter filter)
    {
        Services.AddSingleton(filter);
        return this;
    }

    /// <summary>
    /// 设置最小日志级别
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <returns>构建器</returns>
    public LoggingBuilder SetMinimumLevel(Constants.LogLevel level)
    {
        Services.Configure<LogConfiguration>(config => config.MinimumLevel = level);
        return this;
    }

    /// <summary>
    /// 启用异步日志记录
    /// </summary>
    /// <param name="queueSize">队列大小</param>
    /// <param name="batchSize">批处理大小</param>
    /// <param name="batchTimeout">批处理超时时间</param>
    /// <returns>构建器</returns>
    public LoggingBuilder EnableAsync(int queueSize = 10000, int batchSize = 100, int batchTimeout = 1000)
    {
        Services.Configure<LogConfiguration>(config =>
        {
            config.EnableAsync = true;
            config.AsyncQueueSize = queueSize;
            config.BatchSize = batchSize;
            config.BatchTimeoutMs = batchTimeout;
        });
        return this;
    }

    /// <summary>
    /// 启用性能监控
    /// </summary>
    /// <returns>构建器</returns>
    public LoggingBuilder EnablePerformanceMonitoring()
    {
        Services.TryAddSingleton<ILogPerformanceMonitor, LogPerformanceMonitor>();
        return this;
    }

    /// <summary>
    /// 禁用性能监控
    /// </summary>
    /// <returns>构建器</returns>
    public LoggingBuilder DisablePerformanceMonitoring()
    {
        Services.TryAddSingleton<ILogPerformanceMonitor, NullLogPerformanceMonitor>();
        return this;
    }

    /// <summary>
    /// 启用作用域
    /// </summary>
    /// <returns>构建器</returns>
    public LoggingBuilder EnableScopes()
    {
        Services.Configure<LogConfiguration>(config => config.IncludeScopes = true);
        Services.TryAddSingleton<ILogScopeProvider, LogScopeProvider>();
        return this;
    }

    /// <summary>
    /// 禁用作用域
    /// </summary>
    /// <returns>构建器</returns>
    public LoggingBuilder DisableScopes()
    {
        Services.Configure<LogConfiguration>(config => config.IncludeScopes = false);
        Services.TryAddSingleton<ILogScopeProvider, NullLogScopeProvider>();
        return this;
    }
}
