using Liam.Logging.Interfaces;
using System.Collections.Concurrent;

namespace Liam.Logging.Services;

/// <summary>
/// 日志作用域提供程序实现
/// </summary>
public class LogScopeProvider : ILogScopeProvider
{
    private readonly AsyncLocal<LogScope?> _currentScope = new();

    /// <summary>
    /// 开始作用域
    /// </summary>
    /// <typeparam name="TState">状态类型</typeparam>
    /// <param name="state">状态对象</param>
    /// <returns>作用域释放器</returns>
    public IDisposable Push<TState>(TState state) where TState : notnull
    {
        var parent = _currentScope.Value;
        var newScope = new LogScope(state, parent);
        _currentScope.Value = newScope;
        return newScope;
    }

    /// <summary>
    /// 获取当前作用域信息
    /// </summary>
    /// <returns>作用域信息字典</returns>
    public Dictionary<string, object?> GetCurrentScopes()
    {
        var scopes = new Dictionary<string, object?>();
        var current = _currentScope.Value;
        var index = 0;

        while (current != null)
        {
            var key = $"Scope{index}";
            scopes[key] = current.State;
            current = current.Parent;
            index++;
        }

        return scopes;
    }

    /// <summary>
    /// 日志作用域实现
    /// </summary>
    private class LogScope : IDisposable
    {
        private readonly LogScopeProvider _provider;
        private bool _disposed;

        /// <summary>
        /// 状态对象
        /// </summary>
        public object State { get; }

        /// <summary>
        /// 父作用域
        /// </summary>
        public LogScope? Parent { get; }

        /// <summary>
        /// 初始化日志作用域
        /// </summary>
        /// <param name="state">状态对象</param>
        /// <param name="parent">父作用域</param>
        public LogScope(object state, LogScope? parent)
        {
            State = state;
            Parent = parent;
            _provider = null!; // 在实际使用中会被正确设置
        }

        /// <summary>
        /// 释放作用域
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                // 恢复父作用域
                if (_provider != null)
                {
                    _provider._currentScope.Value = Parent;
                }
                _disposed = true;
            }
        }
    }
}

/// <summary>
/// 线程安全的日志作用域提供程序
/// </summary>
public class ThreadSafeLogScopeProvider : ILogScopeProvider
{
    private readonly ConcurrentDictionary<int, LogScopeStack> _threadScopes = new();

    /// <summary>
    /// 开始作用域
    /// </summary>
    /// <typeparam name="TState">状态类型</typeparam>
    /// <param name="state">状态对象</param>
    /// <returns>作用域释放器</returns>
    public IDisposable Push<TState>(TState state) where TState : notnull
    {
        var threadId = Environment.CurrentManagedThreadId;
        var scopeStack = _threadScopes.GetOrAdd(threadId, _ => new LogScopeStack());
        
        return scopeStack.Push(state);
    }

    /// <summary>
    /// 获取当前作用域信息
    /// </summary>
    /// <returns>作用域信息字典</returns>
    public Dictionary<string, object?> GetCurrentScopes()
    {
        var threadId = Environment.CurrentManagedThreadId;
        if (_threadScopes.TryGetValue(threadId, out var scopeStack))
        {
            return scopeStack.GetCurrentScopes();
        }

        return new Dictionary<string, object?>();
    }

    /// <summary>
    /// 线程作用域栈
    /// </summary>
    private class LogScopeStack
    {
        private readonly Stack<object> _scopes = new();
        private readonly object _lock = new();

        /// <summary>
        /// 推入作用域
        /// </summary>
        /// <param name="state">状态对象</param>
        /// <returns>作用域释放器</returns>
        public IDisposable Push(object state)
        {
            lock (_lock)
            {
                _scopes.Push(state);
                return new ScopeDisposer(this);
            }
        }

        /// <summary>
        /// 弹出作用域
        /// </summary>
        public void Pop()
        {
            lock (_lock)
            {
                if (_scopes.Count > 0)
                {
                    _scopes.Pop();
                }
            }
        }

        /// <summary>
        /// 获取当前作用域信息
        /// </summary>
        /// <returns>作用域信息字典</returns>
        public Dictionary<string, object?> GetCurrentScopes()
        {
            lock (_lock)
            {
                var scopes = new Dictionary<string, object?>();
                var scopeArray = _scopes.ToArray();
                
                for (int i = 0; i < scopeArray.Length; i++)
                {
                    scopes[$"Scope{i}"] = scopeArray[i];
                }

                return scopes;
            }
        }

        /// <summary>
        /// 作用域释放器
        /// </summary>
        private class ScopeDisposer : IDisposable
        {
            private readonly LogScopeStack _stack;
            private bool _disposed;

            public ScopeDisposer(LogScopeStack stack)
            {
                _stack = stack;
            }

            public void Dispose()
            {
                if (!_disposed)
                {
                    _stack.Pop();
                    _disposed = true;
                }
            }
        }
    }
}

/// <summary>
/// 空的日志作用域提供程序（用于禁用作用域功能）
/// </summary>
public class NullLogScopeProvider : ILogScopeProvider
{
    /// <summary>
    /// 单例实例
    /// </summary>
    public static readonly NullLogScopeProvider Instance = new();

    private NullLogScopeProvider() { }

    /// <summary>
    /// 开始作用域（空实现）
    /// </summary>
    /// <typeparam name="TState">状态类型</typeparam>
    /// <param name="state">状态对象</param>
    /// <returns>空的释放器</returns>
    public IDisposable Push<TState>(TState state) where TState : notnull
    {
        return NullDisposable.Instance;
    }

    /// <summary>
    /// 获取当前作用域信息（空实现）
    /// </summary>
    /// <returns>空的字典</returns>
    public Dictionary<string, object?> GetCurrentScopes()
    {
        return new Dictionary<string, object?>();
    }

    /// <summary>
    /// 空的释放器
    /// </summary>
    private class NullDisposable : IDisposable
    {
        public static readonly NullDisposable Instance = new();
        private NullDisposable() { }
        public void Dispose() { }
    }
}
