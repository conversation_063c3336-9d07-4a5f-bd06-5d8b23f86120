using Liam.Cryptography.Models;

namespace Liam.Cryptography.Interfaces;

/// <summary>
/// 密钥管理接口
/// </summary>
public interface IKeyManager
{
    /// <summary>
    /// 生成对称密钥
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>生成的密钥</returns>
    byte[] GenerateSymmetricKey(int keySize = 256);

    /// <summary>
    /// 生成非对称密钥对
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>密钥对</returns>
    KeyPair GenerateAsymmetricKeyPair(int keySize = 2048);

    /// <summary>
    /// 导出密钥到文件
    /// </summary>
    /// <param name="key">密钥数据</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="password">保护密码（可选）</param>
    void ExportKey(byte[] key, string filePath, string? password = null);

    /// <summary>
    /// 导出密钥对到文件
    /// </summary>
    /// <param name="keyPair">密钥对</param>
    /// <param name="privateKeyPath">私钥文件路径</param>
    /// <param name="publicKeyPath">公钥文件路径</param>
    /// <param name="password">保护密码（可选）</param>
    void ExportKeyPair(KeyPair keyPair, string privateKeyPath, string publicKeyPath, string? password = null);

    /// <summary>
    /// 从文件导入密钥
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="password">保护密码（可选）</param>
    /// <returns>密钥数据</returns>
    byte[] ImportKey(string filePath, string? password = null);

    /// <summary>
    /// 从文件导入私钥
    /// </summary>
    /// <param name="filePath">私钥文件路径</param>
    /// <param name="password">保护密码（可选）</param>
    /// <returns>私钥</returns>
    string ImportPrivateKey(string filePath, string? password = null);

    /// <summary>
    /// 从文件导入公钥
    /// </summary>
    /// <param name="filePath">公钥文件路径</param>
    /// <returns>公钥</returns>
    string ImportPublicKey(string filePath);

    /// <summary>
    /// 验证密钥格式
    /// </summary>
    /// <param name="key">密钥</param>
    /// <param name="keyType">密钥类型</param>
    /// <returns>验证结果</returns>
    bool ValidateKey(string key, KeyType keyType);

    /// <summary>
    /// 从密钥对中提取公钥
    /// </summary>
    /// <param name="privateKey">私钥</param>
    /// <returns>公钥</returns>
    string ExtractPublicKey(string privateKey);
}
