using Liam.Cryptography.Services;
using <PERSON>.Cryptography.Models;

namespace Liam.Cryptography.Tests.Fixtures;

/// <summary>
/// 加密测试固定装置，提供共享的测试资源
/// </summary>
public class CryptoTestFixture : IDisposable
{
    private readonly List<string> _tempFiles = new();
    private bool _disposed = false;

    /// <summary>
    /// AES对称加密服务实例
    /// </summary>
    public AesSymmetricCrypto AesService { get; }

    /// <summary>
    /// RSA非对称加密服务实例
    /// </summary>
    public RsaAsymmetricCrypto RsaService { get; }

    /// <summary>
    /// SHA256哈希服务实例
    /// </summary>
    public Sha256HashProvider HashService { get; }

    /// <summary>
    /// RSA数字签名服务实例
    /// </summary>
    public RsaDigitalSignature SignatureService { get; }

    /// <summary>
    /// 密钥管理服务实例
    /// </summary>
    public CryptoKeyManager KeyManagerService { get; }

    /// <summary>
    /// 测试用的RSA密钥对
    /// </summary>
    public KeyPair TestRsaKeyPair { get; }

    /// <summary>
    /// 测试用的AES密钥
    /// </summary>
    public byte[] TestAesKey { get; }

    /// <summary>
    /// 测试用的AES初始化向量
    /// </summary>
    public byte[] TestAesIV { get; }

    public CryptoTestFixture()
    {
        // 初始化服务实例
        AesService = new AesSymmetricCrypto();
        RsaService = new RsaAsymmetricCrypto();
        HashService = new Sha256HashProvider();
        SignatureService = new RsaDigitalSignature();
        KeyManagerService = new CryptoKeyManager();

        // 生成测试用的密钥
        TestRsaKeyPair = RsaService.GenerateKeyPair(2048);
        TestAesKey = AesService.GenerateKey(256);
        TestAesIV = AesService.GenerateIV();
    }

    /// <summary>
    /// 创建临时测试文件
    /// </summary>
    /// <param name="content">文件内容</param>
    /// <returns>临时文件路径</returns>
    public string CreateTempFile(string content)
    {
        var tempFile = Path.GetTempFileName();
        File.WriteAllText(tempFile, content);
        _tempFiles.Add(tempFile);
        return tempFile;
    }

    /// <summary>
    /// 创建临时测试文件
    /// </summary>
    /// <param name="content">文件内容</param>
    /// <returns>临时文件路径</returns>
    public string CreateTempFile(byte[] content)
    {
        var tempFile = Path.GetTempFileName();
        File.WriteAllBytes(tempFile, content);
        _tempFiles.Add(tempFile);
        return tempFile;
    }

    /// <summary>
    /// 获取测试用的不同长度的AES密钥
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>AES密钥</returns>
    public byte[] GetTestAesKey(int keySize)
    {
        return AesService.GenerateKey(keySize);
    }

    /// <summary>
    /// 获取测试用的不同长度的RSA密钥对
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>RSA密钥对</returns>
    public KeyPair GetTestRsaKeyPair(int keySize)
    {
        return RsaService.GenerateKeyPair(keySize);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 清理临时文件
                foreach (var tempFile in _tempFiles)
                {
                    try
                    {
                        if (File.Exists(tempFile))
                        {
                            File.Delete(tempFile);
                        }
                    }
                    catch
                    {
                        // 忽略删除失败的情况
                    }
                }

                // 清理敏感数据
                // 注意：不要在这里清理TestRsaKeyPair，因为它可能被多个测试使用
                // TestRsaKeyPair?.ClearPrivateKey();
                Array.Clear(TestAesKey);
                Array.Clear(TestAesIV);
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~CryptoTestFixture()
    {
        Dispose(false);
    }
}

/// <summary>
/// 加密测试集合定义
/// </summary>
[CollectionDefinition("Crypto Tests")]
public class CryptoTestCollection : ICollectionFixture<CryptoTestFixture>
{
    // 这个类不需要实现任何内容，它只是用来定义测试集合
}
