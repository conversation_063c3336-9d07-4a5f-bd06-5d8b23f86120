using FluentAssertions;
using Liam.SerialPort.Models;
using Liam.SerialPort.Tests.TestHelpers;
using Xunit;

namespace Liam.SerialPort.Tests.Models;

/// <summary>
/// SerialPortInfo 测试类
/// </summary>
public class SerialPortInfoTests : SerialPortTestBase
{
    [Fact]
    public void Constructor_ShouldSetDefaultValues()
    {
        // Arrange & Act
        var portInfo = new SerialPortInfo();

        // Assert
        portInfo.PortName.Should().BeEmpty();
        portInfo.Description.Should().BeEmpty();
        portInfo.Manufacturer.Should().BeEmpty();
        portInfo.ProductId.Should().BeEmpty();
        portInfo.VendorId.Should().BeEmpty();
        portInfo.SerialNumber.Should().BeEmpty();
        portInfo.DeviceType.Should().BeEmpty();
        portInfo.IsAvailable.Should().BeTrue();
        portInfo.IsInUse.Should().BeFalse();
        portInfo.DevicePath.Should().BeEmpty();
        portInfo.FriendlyName.Should().BeEmpty();
        portInfo.DeviceInstanceId.Should().BeEmpty();
        portInfo.HardwareIds.Should().NotBeNull().And.BeEmpty();
        portInfo.CompatibleIds.Should().NotBeNull().And.BeEmpty();
        portInfo.Properties.Should().NotBeNull().And.BeEmpty();
        portInfo.DiscoveredAt.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
        portInfo.LastChecked.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("COM1", "", "COM1")]
    [InlineData("COM1", "USB Serial Port", "USB Serial Port")]
    [InlineData("COM1", "Test Device", "Test Device")]
    public void DisplayName_ShouldReturnCorrectValue(string portName, string friendlyName, string expected)
    {
        // Arrange
        var portInfo = new SerialPortInfo
        {
            PortName = portName,
            FriendlyName = friendlyName
        };

        // Act
        var displayName = portInfo.DisplayName;

        // Assert
        displayName.Should().Be(expected);
    }

    [Fact]
    public void DisplayName_WithDescriptionButNoFriendlyName_ShouldReturnPortNameAndDescription()
    {
        // Arrange
        var portInfo = new SerialPortInfo
        {
            PortName = "COM1",
            Description = "USB Serial Port",
            FriendlyName = ""
        };

        // Act
        var displayName = portInfo.DisplayName;

        // Assert
        displayName.Should().Be("COM1 - USB Serial Port");
    }

    [Theory]
    [InlineData("DEVICE123", "", "", "DEVICE123")]
    [InlineData("", "SN123", "", "SN123")]
    [InlineData("", "", "/dev/ttyUSB0", "/dev/ttyUSB0")]
    [InlineData("", "", "", "COM1")]
    public void UniqueId_ShouldReturnCorrectValue(string deviceInstanceId, string serialNumber, string devicePath, string expected)
    {
        // Arrange
        var portInfo = new SerialPortInfo
        {
            PortName = "COM1",
            DeviceInstanceId = deviceInstanceId,
            SerialNumber = serialNumber,
            DevicePath = devicePath
        };

        // Act
        var uniqueId = portInfo.UniqueId;

        // Assert
        uniqueId.Should().Be(expected);
    }

    [Theory]
    [InlineData("", "", false)]
    [InlineData("1234", "", false)]
    [InlineData("", "5678", false)]
    [InlineData("1234", "5678", true)]
    public void IsUsbDevice_ShouldReturnCorrectValue(string vendorId, string productId, bool expected)
    {
        // Arrange
        var portInfo = new SerialPortInfo
        {
            VendorId = vendorId,
            ProductId = productId
        };

        // Act
        var isUsbDevice = portInfo.IsUsbDevice;

        // Assert
        isUsbDevice.Should().Be(expected);
    }

    [Theory]
    [InlineData("Virtual COM Port", "", true)]
    [InlineData("", "Virtual", true)]
    [InlineData("USB Serial Port", "USB", false)]
    [InlineData("", "", false)]
    public void IsVirtualPort_ShouldReturnCorrectValue(string description, string deviceType, bool expected)
    {
        // Arrange
        var portInfo = new SerialPortInfo
        {
            Description = description,
            DeviceType = deviceType
        };

        // Act
        var isVirtualPort = portInfo.IsVirtualPort;

        // Assert
        isVirtualPort.Should().Be(expected);
    }

    [Fact]
    public void ToString_ShouldReturnDisplayName()
    {
        // Arrange
        var portInfo = CreateTestPortInfo("COM1", "Test Port");

        // Act
        var result = portInfo.ToString();

        // Assert
        result.Should().Be(portInfo.DisplayName);
    }

    [Fact]
    public void Equals_WithSameUniqueId_ShouldReturnTrue()
    {
        // Arrange
        var portInfo1 = new SerialPortInfo
        {
            PortName = "COM1",
            DeviceInstanceId = "DEVICE123"
        };

        var portInfo2 = new SerialPortInfo
        {
            PortName = "COM2", // 不同的端口名
            DeviceInstanceId = "DEVICE123" // 相同的设备实例ID
        };

        // Act & Assert
        portInfo1.Equals(portInfo2).Should().BeTrue();
        portInfo2.Equals(portInfo1).Should().BeTrue();
    }

    [Fact]
    public void Equals_WithDifferentUniqueId_ShouldReturnFalse()
    {
        // Arrange
        var portInfo1 = new SerialPortInfo
        {
            PortName = "COM1",
            DeviceInstanceId = "DEVICE123"
        };

        var portInfo2 = new SerialPortInfo
        {
            PortName = "COM1",
            DeviceInstanceId = "DEVICE456"
        };

        // Act & Assert
        portInfo1.Equals(portInfo2).Should().BeFalse();
        portInfo2.Equals(portInfo1).Should().BeFalse();
    }

    [Fact]
    public void Equals_WithNull_ShouldReturnFalse()
    {
        // Arrange
        var portInfo = CreateTestPortInfo();

        // Act & Assert
        portInfo.Equals(null).Should().BeFalse();
    }

    [Fact]
    public void Equals_WithDifferentType_ShouldReturnFalse()
    {
        // Arrange
        var portInfo = CreateTestPortInfo();
        var otherObject = "not a SerialPortInfo";

        // Act & Assert
        portInfo.Equals(otherObject).Should().BeFalse();
    }

    [Fact]
    public void GetHashCode_WithSameUniqueId_ShouldReturnSameValue()
    {
        // Arrange
        var portInfo1 = new SerialPortInfo
        {
            PortName = "COM1",
            DeviceInstanceId = "DEVICE123"
        };

        var portInfo2 = new SerialPortInfo
        {
            PortName = "COM2",
            DeviceInstanceId = "DEVICE123"
        };

        // Act
        var hashCode1 = portInfo1.GetHashCode();
        var hashCode2 = portInfo2.GetHashCode();

        // Assert
        hashCode1.Should().Be(hashCode2);
    }

    [Fact]
    public void GetHashCode_WithDifferentUniqueId_ShouldReturnDifferentValue()
    {
        // Arrange
        var portInfo1 = new SerialPortInfo
        {
            PortName = "COM1",
            DeviceInstanceId = "DEVICE123"
        };

        var portInfo2 = new SerialPortInfo
        {
            PortName = "COM1",
            DeviceInstanceId = "DEVICE456"
        };

        // Act
        var hashCode1 = portInfo1.GetHashCode();
        var hashCode2 = portInfo2.GetHashCode();

        // Assert
        hashCode1.Should().NotBe(hashCode2);
    }

    [Fact]
    public void Clone_ShouldCreateDeepCopy()
    {
        // Arrange
        var original = CreateTestPortInfo();
        original.HardwareIds.Add("USB\\VID_1234&PID_5678");
        original.CompatibleIds.Add("USB\\Class_02");
        original.Properties.Add("TestKey", "TestValue");

        // Act
        var cloned = original.Clone();

        // Assert
        cloned.Should().NotBeSameAs(original);
        cloned.PortName.Should().Be(original.PortName);
        cloned.Description.Should().Be(original.Description);
        cloned.Manufacturer.Should().Be(original.Manufacturer);
        cloned.ProductId.Should().Be(original.ProductId);
        cloned.VendorId.Should().Be(original.VendorId);
        cloned.SerialNumber.Should().Be(original.SerialNumber);
        cloned.DeviceType.Should().Be(original.DeviceType);
        cloned.IsAvailable.Should().Be(original.IsAvailable);
        cloned.IsInUse.Should().Be(original.IsInUse);
        cloned.DevicePath.Should().Be(original.DevicePath);
        cloned.FriendlyName.Should().Be(original.FriendlyName);
        cloned.DeviceInstanceId.Should().Be(original.DeviceInstanceId);
        cloned.HardwareIds.Should().BeEquivalentTo(original.HardwareIds);
        cloned.HardwareIds.Should().NotBeSameAs(original.HardwareIds);
        cloned.CompatibleIds.Should().BeEquivalentTo(original.CompatibleIds);
        cloned.CompatibleIds.Should().NotBeSameAs(original.CompatibleIds);
        cloned.Properties.Should().BeEquivalentTo(original.Properties);
        cloned.Properties.Should().NotBeSameAs(original.Properties);
        cloned.DiscoveredAt.Should().Be(original.DiscoveredAt);
        cloned.LastChecked.Should().Be(original.LastChecked);
    }

    [Fact]
    public void Clone_ModifyingClone_ShouldNotAffectOriginal()
    {
        // Arrange
        var original = CreateTestPortInfo();
        original.HardwareIds.Add("Original");
        original.Properties.Add("OriginalKey", "OriginalValue");

        // Act
        var cloned = original.Clone();
        cloned.PortName = "ModifiedPort";
        cloned.HardwareIds.Add("Modified");
        cloned.Properties.Add("ModifiedKey", "ModifiedValue");

        // Assert
        original.PortName.Should().NotBe(cloned.PortName);
        original.HardwareIds.Should().NotContain("Modified");
        original.Properties.Should().NotContainKey("ModifiedKey");
    }
}
