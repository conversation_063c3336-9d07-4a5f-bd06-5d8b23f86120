namespace Liam.SerialPort.Constants;

/// <summary>
/// 串口常量定义
/// </summary>
public static class SerialPortConstants
{
    /// <summary>
    /// 默认配置常量
    /// </summary>
    public static class Defaults
    {
        /// <summary>
        /// 默认波特率
        /// </summary>
        public const int BaudRate = 9600;

        /// <summary>
        /// 默认数据位
        /// </summary>
        public const int DataBits = 8;

        /// <summary>
        /// 默认读取超时时间（毫秒）
        /// </summary>
        public const int ReadTimeout = 5000;

        /// <summary>
        /// 默认写入超时时间（毫秒）
        /// </summary>
        public const int WriteTimeout = 5000;

        /// <summary>
        /// 默认接收缓冲区大小
        /// </summary>
        public const int ReceiveBufferSize = 4096;

        /// <summary>
        /// 默认发送缓冲区大小
        /// </summary>
        public const int SendBufferSize = 2048;

        /// <summary>
        /// 默认连接超时时间（毫秒）
        /// </summary>
        public const int ConnectionTimeout = 10000;

        /// <summary>
        /// 默认重试次数
        /// </summary>
        public const int RetryCount = 3;

        /// <summary>
        /// 默认重试间隔（毫秒）
        /// </summary>
        public const int RetryInterval = 1000;

        /// <summary>
        /// 默认自动重连间隔（毫秒）
        /// </summary>
        public const int AutoReconnectInterval = 5000;

        /// <summary>
        /// 默认设备监控间隔（毫秒）
        /// </summary>
        public const int DeviceMonitorInterval = 2000;

        /// <summary>
        /// 默认心跳间隔（毫秒）
        /// </summary>
        public const int HeartbeatInterval = 30000;

        /// <summary>
        /// 默认日志最大大小（字节）
        /// </summary>
        public const long MaxLogSize = 1024 * 1024; // 1MB
    }

    /// <summary>
    /// 常用波特率
    /// </summary>
    public static class BaudRates
    {
        public const int Baud300 = 300;
        public const int Baud600 = 600;
        public const int Baud1200 = 1200;
        public const int Baud2400 = 2400;
        public const int Baud4800 = 4800;
        public const int Baud9600 = 9600;
        public const int Baud14400 = 14400;
        public const int Baud19200 = 19200;
        public const int Baud28800 = 28800;
        public const int Baud38400 = 38400;
        public const int Baud57600 = 57600;
        public const int Baud115200 = 115200;
        public const int Baud230400 = 230400;
        public const int Baud460800 = 460800;
        public const int Baud921600 = 921600;

        /// <summary>
        /// 获取所有支持的波特率
        /// </summary>
        public static readonly int[] All = {
            Baud300, Baud600, Baud1200, Baud2400, Baud4800, Baud9600,
            Baud14400, Baud19200, Baud28800, Baud38400, Baud57600,
            Baud115200, Baud230400, Baud460800, Baud921600
        };

        /// <summary>
        /// 常用波特率
        /// </summary>
        public static readonly int[] Common = {
            Baud9600, Baud19200, Baud38400, Baud57600, Baud115200
        };
    }

    /// <summary>
    /// 错误代码
    /// </summary>
    public static class ErrorCodes
    {
        public const string PortNotFound = "PORT_NOT_FOUND";
        public const string PortInUse = "PORT_IN_USE";
        public const string ConnectionFailed = "CONNECTION_FAILED";
        public const string ConnectionTimeout = "CONNECTION_TIMEOUT";
        public const string ReadTimeout = "READ_TIMEOUT";
        public const string WriteTimeout = "WRITE_TIMEOUT";
        public const string InvalidConfiguration = "INVALID_CONFIGURATION";
        public const string DeviceUnavailable = "DEVICE_UNAVAILABLE";
        public const string BufferOverflow = "BUFFER_OVERFLOW";
        public const string DataCorrupted = "DATA_CORRUPTED";
        public const string OperationCancelled = "OPERATION_CANCELLED";
        public const string UnknownError = "UNKNOWN_ERROR";
    }

    /// <summary>
    /// 操作类型
    /// </summary>
    public static class OperationTypes
    {
        public const string Connect = "连接";
        public const string Disconnect = "断开连接";
        public const string Read = "读取";
        public const string Write = "写入";
        public const string SendAndReceive = "发送并接收";
        public const string Discovery = "设备发现";
        public const string Monitoring = "设备监控";
        public const string Heartbeat = "心跳检测";
    }

    /// <summary>
    /// 平台相关常量
    /// </summary>
    public static class Platform
    {
        /// <summary>
        /// Windows串口名称前缀
        /// </summary>
        public const string WindowsPortPrefix = "COM";

        /// <summary>
        /// Linux串口设备路径前缀
        /// </summary>
        public static readonly string[] LinuxPortPrefixes = {
            "/dev/ttyUSB",
            "/dev/ttyACM",
            "/dev/ttyS",
            "/dev/ttyAMA"
        };

        /// <summary>
        /// macOS串口设备路径前缀
        /// </summary>
        public static readonly string[] MacOSPortPrefixes = {
            "/dev/tty.usbserial",
            "/dev/tty.usbmodem",
            "/dev/tty.SLAB_USBtoUART",
            "/dev/tty.wchusbserial"
        };

        /// <summary>
        /// 设备监控注册表路径（Windows）
        /// </summary>
        public const string WindowsDeviceRegistryPath = @"SYSTEM\CurrentControlSet\Enum\USB";

        /// <summary>
        /// 设备监控文件路径（Linux）
        /// </summary>
        public const string LinuxDevicePath = "/dev";

        /// <summary>
        /// 系统信息路径（Linux）
        /// </summary>
        public const string LinuxSysPath = "/sys/class/tty";
    }

    /// <summary>
    /// 数据格式常量
    /// </summary>
    public static class DataFormats
    {
        /// <summary>
        /// 十六进制分隔符
        /// </summary>
        public static readonly string[] HexSeparators = { " ", "-", ":", "," };

        /// <summary>
        /// 常用换行符
        /// </summary>
        public static class NewLines
        {
            public const string CRLF = "\r\n";
            public const string LF = "\n";
            public const string CR = "\r";
        }

        /// <summary>
        /// 常用终止符
        /// </summary>
        public static class Terminators
        {
            public static readonly byte[] CRLF = { 0x0D, 0x0A };
            public static readonly byte[] LF = { 0x0A };
            public static readonly byte[] CR = { 0x0D };
            public static readonly byte[] NULL = { 0x00 };
            public static readonly byte[] ETX = { 0x03 };
            public static readonly byte[] EOT = { 0x04 };
        }
    }

    /// <summary>
    /// 性能相关常量
    /// </summary>
    public static class Performance
    {
        /// <summary>
        /// 最小缓冲区大小
        /// </summary>
        public const int MinBufferSize = 256;

        /// <summary>
        /// 最大缓冲区大小
        /// </summary>
        public const int MaxBufferSize = 65536;

        /// <summary>
        /// 默认读取块大小
        /// </summary>
        public const int DefaultReadChunkSize = 1024;

        /// <summary>
        /// 最大读取块大小
        /// </summary>
        public const int MaxReadChunkSize = 8192;

        /// <summary>
        /// 异步操作延迟（毫秒）
        /// </summary>
        public const int AsyncDelay = 10;

        /// <summary>
        /// 设备扫描间隔（毫秒）
        /// </summary>
        public const int DeviceScanInterval = 100;
    }

    /// <summary>
    /// 日志相关常量
    /// </summary>
    public static class Logging
    {
        /// <summary>
        /// 日志类别
        /// </summary>
        public const string Category = "Liam.SerialPort";

        /// <summary>
        /// 事件ID
        /// </summary>
        public static class EventIds
        {
            public const int PortDiscovered = 1001;
            public const int PortConnected = 1002;
            public const int PortDisconnected = 1003;
            public const int DataSent = 1004;
            public const int DataReceived = 1005;
            public const int ErrorOccurred = 1006;
            public const int DeviceChanged = 1007;
            public const int ConfigurationChanged = 1008;
            public const int PerformanceWarning = 1009;
        }
    }

    /// <summary>
    /// 验证相关常量
    /// </summary>
    public static class Validation
    {
        /// <summary>
        /// 最小波特率
        /// </summary>
        public const int MinBaudRate = 75;

        /// <summary>
        /// 最大波特率
        /// </summary>
        public const int MaxBaudRate = 4000000;

        /// <summary>
        /// 最小数据位
        /// </summary>
        public const int MinDataBits = 5;

        /// <summary>
        /// 最大数据位
        /// </summary>
        public const int MaxDataBits = 8;

        /// <summary>
        /// 最小超时时间（毫秒）
        /// </summary>
        public const int MinTimeout = 100;

        /// <summary>
        /// 最大超时时间（毫秒）
        /// </summary>
        public const int MaxTimeout = 300000; // 5分钟

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public const int MaxRetryCount = 10;

        /// <summary>
        /// 最大端口名称长度
        /// </summary>
        public const int MaxPortNameLength = 50;
    }
}
