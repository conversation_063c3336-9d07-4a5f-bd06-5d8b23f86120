using System.Text;
using <PERSON>.TcpServer.Constants;

namespace Liam.TcpServer.Models;

/// <summary>
/// TCP消息
/// </summary>
public class TcpMessage
{
    /// <summary>
    /// 消息唯一标识符
    /// </summary>
    public string Id { get; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public byte MessageType { get; set; }

    /// <summary>
    /// 消息数据
    /// </summary>
    public byte[] Data { get; set; }

    /// <summary>
    /// 消息长度
    /// </summary>
    public int Length => Data?.Length ?? 0;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime? ReceivedAt { get; set; }

    /// <summary>
    /// 消息属性
    /// </summary>
    public Dictionary<string, object> Properties { get; }

    /// <summary>
    /// 是否为心跳消息
    /// </summary>
    public bool IsHeartbeat => MessageType == TcpServerConstants.MessageTypes.HeartbeatRequest ||
                               MessageType == TcpServerConstants.MessageTypes.HeartbeatResponse;

    /// <summary>
    /// 是否为控制消息
    /// </summary>
    public bool IsControlMessage => MessageType != TcpServerConstants.MessageTypes.Data;

    /// <summary>
    /// 初始化TCP消息
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <param name="data">消息数据</param>
    public TcpMessage(byte messageType, byte[] data)
    {
        Id = Guid.NewGuid().ToString("N");
        MessageType = messageType;
        Data = data ?? Array.Empty<byte>();
        CreatedAt = DateTime.UtcNow;
        Properties = new Dictionary<string, object>();
    }

    /// <summary>
    /// 初始化TCP消息
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <param name="text">文本数据</param>
    /// <param name="encoding">文本编码</param>
    public TcpMessage(byte messageType, string text, Encoding? encoding = null)
        : this(messageType, (encoding ?? Encoding.UTF8).GetBytes(text ?? string.Empty))
    {
    }

    /// <summary>
    /// 获取文本数据
    /// </summary>
    /// <param name="encoding">文本编码</param>
    /// <returns>文本数据</returns>
    public string GetText(Encoding? encoding = null)
    {
        if (Data == null || Data.Length == 0)
        {
            return string.Empty;
        }

        return (encoding ?? Encoding.UTF8).GetString(Data);
    }

    /// <summary>
    /// 设置文本数据
    /// </summary>
    /// <param name="text">文本数据</param>
    /// <param name="encoding">文本编码</param>
    public void SetText(string text, Encoding? encoding = null)
    {
        Data = (encoding ?? Encoding.UTF8).GetBytes(text ?? string.Empty);
    }

    /// <summary>
    /// 获取消息属性
    /// </summary>
    /// <typeparam name="T">属性类型</typeparam>
    /// <param name="key">属性键</param>
    /// <returns>属性值</returns>
    public T? GetProperty<T>(string key)
    {
        ArgumentNullException.ThrowIfNull(key);
        
        if (Properties.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        
        return default;
    }

    /// <summary>
    /// 设置消息属性
    /// </summary>
    /// <param name="key">属性键</param>
    /// <param name="value">属性值</param>
    public void SetProperty(string key, object value)
    {
        ArgumentNullException.ThrowIfNull(key);
        ArgumentNullException.ThrowIfNull(value);
        
        Properties[key] = value;
    }

    /// <summary>
    /// 移除消息属性
    /// </summary>
    /// <param name="key">属性键</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveProperty(string key)
    {
        ArgumentNullException.ThrowIfNull(key);
        return Properties.Remove(key);
    }

    /// <summary>
    /// 序列化消息为字节数组
    /// </summary>
    /// <returns>序列化后的字节数组</returns>
    public byte[] Serialize()
    {
        // 消息格式：[消息类型(1字节)] + [数据长度(4字节)] + [数据]
        var lengthBytes = BitConverter.GetBytes(Length);
        var result = new byte[1 + 4 + Length];
        
        result[0] = MessageType;
        Array.Copy(lengthBytes, 0, result, 1, 4);
        
        if (Data != null && Data.Length > 0)
        {
            Array.Copy(Data, 0, result, 5, Data.Length);
        }
        
        return result;
    }

    /// <summary>
    /// 从字节数组反序列化消息
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <returns>TCP消息</returns>
    /// <exception cref="ArgumentException">字节数组格式无效</exception>
    public static TcpMessage Deserialize(byte[] bytes)
    {
        ArgumentNullException.ThrowIfNull(bytes);
        
        if (bytes.Length < 5)
        {
            throw new ArgumentException("字节数组长度不足，至少需要5个字节", nameof(bytes));
        }

        var messageType = bytes[0];
        var dataLength = BitConverter.ToInt32(bytes, 1);
        
        if (bytes.Length < 5 + dataLength)
        {
            throw new ArgumentException($"字节数组长度不足，期望{5 + dataLength}字节，实际{bytes.Length}字节", nameof(bytes));
        }

        var data = new byte[dataLength];
        if (dataLength > 0)
        {
            Array.Copy(bytes, 5, data, 0, dataLength);
        }

        return new TcpMessage(messageType, data);
    }

    /// <summary>
    /// 尝试从字节数组反序列化消息
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="message">输出的TCP消息</param>
    /// <returns>是否成功反序列化</returns>
    public static bool TryDeserialize(byte[] bytes, out TcpMessage? message)
    {
        message = null;
        
        try
        {
            message = Deserialize(bytes);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 创建数据消息
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>数据消息</returns>
    public static TcpMessage CreateDataMessage(byte[] data)
    {
        return new TcpMessage(TcpServerConstants.MessageTypes.Data, data);
    }

    /// <summary>
    /// 创建文本消息
    /// </summary>
    /// <param name="text">文本</param>
    /// <param name="encoding">编码</param>
    /// <returns>文本消息</returns>
    public static TcpMessage CreateTextMessage(string text, Encoding? encoding = null)
    {
        return new TcpMessage(TcpServerConstants.MessageTypes.Data, text, encoding);
    }

    /// <summary>
    /// 创建心跳请求消息
    /// </summary>
    /// <returns>心跳请求消息</returns>
    public static TcpMessage CreateHeartbeatRequest()
    {
        return new TcpMessage(TcpServerConstants.MessageTypes.HeartbeatRequest, Array.Empty<byte>());
    }

    /// <summary>
    /// 创建心跳响应消息
    /// </summary>
    /// <returns>心跳响应消息</returns>
    public static TcpMessage CreateHeartbeatResponse()
    {
        return new TcpMessage(TcpServerConstants.MessageTypes.HeartbeatResponse, Array.Empty<byte>());
    }

    /// <summary>
    /// 创建连接确认消息
    /// </summary>
    /// <returns>连接确认消息</returns>
    public static TcpMessage CreateConnectionAck()
    {
        return new TcpMessage(TcpServerConstants.MessageTypes.ConnectionAck, Array.Empty<byte>());
    }

    /// <summary>
    /// 创建断开连接消息
    /// </summary>
    /// <param name="reason">断开原因</param>
    /// <returns>断开连接消息</returns>
    public static TcpMessage CreateDisconnectMessage(string? reason = null)
    {
        var data = string.IsNullOrEmpty(reason) ? Array.Empty<byte>() : Encoding.UTF8.GetBytes(reason);
        return new TcpMessage(TcpServerConstants.MessageTypes.Disconnect, data);
    }

    /// <summary>
    /// 创建错误消息
    /// </summary>
    /// <param name="error">错误信息</param>
    /// <returns>错误消息</returns>
    public static TcpMessage CreateErrorMessage(string error)
    {
        return new TcpMessage(TcpServerConstants.MessageTypes.Error, error, Encoding.UTF8);
    }

    /// <summary>
    /// 重写ToString方法
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        var typeStr = MessageType switch
        {
            TcpServerConstants.MessageTypes.Data => "Data",
            TcpServerConstants.MessageTypes.HeartbeatRequest => "HeartbeatReq",
            TcpServerConstants.MessageTypes.HeartbeatResponse => "HeartbeatResp",
            TcpServerConstants.MessageTypes.ConnectionAck => "ConnAck",
            TcpServerConstants.MessageTypes.Disconnect => "Disconnect",
            TcpServerConstants.MessageTypes.Error => "Error",
            _ => $"Unknown({MessageType:X2})"
        };

        return $"TcpMessage[{Id[..8]}] Type:{typeStr} Length:{Length} Created:{CreatedAt:HH:mm:ss.fff}";
    }
}
