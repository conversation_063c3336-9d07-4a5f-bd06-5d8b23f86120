using System.Net;
using Liam.TcpClient.Constants;

namespace Liam.TcpClient.Models;

/// <summary>
/// 连接信息
/// </summary>
public class ConnectionInfo
{
    /// <summary>
    /// 连接唯一标识符
    /// </summary>
    public string Id { get; }

    /// <summary>
    /// 客户端标识符
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// 客户端名称
    /// </summary>
    public string? ClientName { get; set; }

    /// <summary>
    /// 远程终结点
    /// </summary>
    public EndPoint? RemoteEndPoint { get; set; }

    /// <summary>
    /// 本地终结点
    /// </summary>
    public EndPoint? LocalEndPoint { get; set; }

    /// <summary>
    /// 连接状态
    /// </summary>
    public string State { get; set; } = TcpClientConstants.ConnectionStates.Disconnected;

    /// <summary>
    /// 连接建立时间
    /// </summary>
    public DateTime? ConnectedAt { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivityAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 连接持续时间
    /// </summary>
    public TimeSpan? Duration => ConnectedAt.HasValue ? DateTime.UtcNow - ConnectedAt.Value : null;

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected => State == TcpClientConstants.ConnectionStates.Connected;

    /// <summary>
    /// 是否正在连接
    /// </summary>
    public bool IsConnecting => State == TcpClientConstants.ConnectionStates.Connecting;

    /// <summary>
    /// 是否正在重连
    /// </summary>
    public bool IsReconnecting => State == TcpClientConstants.ConnectionStates.Reconnecting;

    /// <summary>
    /// 是否正在断开连接
    /// </summary>
    public bool IsDisconnecting => State == TcpClientConstants.ConnectionStates.Disconnecting;

    /// <summary>
    /// 是否已断开连接
    /// </summary>
    public bool IsDisconnected => State == TcpClientConstants.ConnectionStates.Disconnected;

    /// <summary>
    /// 是否处于错误状态
    /// </summary>
    public bool IsError => State == TcpClientConstants.ConnectionStates.Error;

    /// <summary>
    /// 是否启用SSL
    /// </summary>
    public bool IsSslEnabled { get; set; }

    /// <summary>
    /// SSL信息
    /// </summary>
    public SslInfo? SslInfo { get; set; }

    /// <summary>
    /// 连接统计信息
    /// </summary>
    public ConnectionStatistics Statistics { get; }

    /// <summary>
    /// 连接属性
    /// </summary>
    public Dictionary<string, object> Properties { get; }

    /// <summary>
    /// 重连次数
    /// </summary>
    public int ReconnectCount { get; set; }

    /// <summary>
    /// 最后错误信息
    /// </summary>
    public string? LastError { get; set; }

    /// <summary>
    /// 最后错误时间
    /// </summary>
    public DateTime? LastErrorAt { get; set; }

    /// <summary>
    /// 初始化连接信息
    /// </summary>
    public ConnectionInfo()
    {
        Id = Guid.NewGuid().ToString("N");
        Statistics = new ConnectionStatistics();
        Properties = new Dictionary<string, object>();
    }

    /// <summary>
    /// 初始化连接信息
    /// </summary>
    /// <param name="clientId">客户端标识符</param>
    /// <param name="clientName">客户端名称</param>
    public ConnectionInfo(string? clientId, string? clientName = null) : this()
    {
        ClientId = clientId;
        ClientName = clientName;
    }

    /// <summary>
    /// 更新连接状态
    /// </summary>
    /// <param name="newState">新状态</param>
    public void UpdateState(string newState)
    {
        State = newState;
        LastActivityAt = DateTime.UtcNow;

        if (newState == TcpClientConstants.ConnectionStates.Connected && !ConnectedAt.HasValue)
        {
            ConnectedAt = DateTime.UtcNow;
        }
        else if (newState == TcpClientConstants.ConnectionStates.Disconnected)
        {
            ConnectedAt = null;
        }
    }

    /// <summary>
    /// 更新最后活动时间
    /// </summary>
    public void UpdateLastActivity()
    {
        LastActivityAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 设置错误信息
    /// </summary>
    /// <param name="error">错误信息</param>
    public void SetError(string error)
    {
        LastError = error;
        LastErrorAt = DateTime.UtcNow;
        UpdateState(TcpClientConstants.ConnectionStates.Error);
    }

    /// <summary>
    /// 清除错误信息
    /// </summary>
    public void ClearError()
    {
        LastError = null;
        LastErrorAt = null;
    }

    /// <summary>
    /// 获取连接属性
    /// </summary>
    /// <typeparam name="T">属性类型</typeparam>
    /// <param name="key">属性键</param>
    /// <returns>属性值</returns>
    public T? GetProperty<T>(string key)
    {
        ArgumentNullException.ThrowIfNull(key);
        
        if (Properties.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        
        return default;
    }

    /// <summary>
    /// 设置连接属性
    /// </summary>
    /// <param name="key">属性键</param>
    /// <param name="value">属性值</param>
    public void SetProperty(string key, object value)
    {
        ArgumentNullException.ThrowIfNull(key);
        ArgumentNullException.ThrowIfNull(value);
        
        Properties[key] = value;
    }

    /// <summary>
    /// 移除连接属性
    /// </summary>
    /// <param name="key">属性键</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveProperty(string key)
    {
        ArgumentNullException.ThrowIfNull(key);
        return Properties.Remove(key);
    }

    /// <summary>
    /// 重写ToString方法
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        var endpoint = RemoteEndPoint?.ToString() ?? "Unknown";
        var duration = Duration?.ToString(@"hh\:mm\:ss") ?? "N/A";
        var ssl = IsSslEnabled ? " (SSL)" : "";
        
        return $"Connection[{Id[..8]}] {endpoint}{ssl} State:{State} Duration:{duration}";
    }
}

/// <summary>
/// SSL信息
/// </summary>
public class SslInfo
{
    /// <summary>
    /// SSL协议版本
    /// </summary>
    public string? Protocol { get; set; }

    /// <summary>
    /// 加密算法
    /// </summary>
    public string? CipherAlgorithm { get; set; }

    /// <summary>
    /// 哈希算法
    /// </summary>
    public string? HashAlgorithm { get; set; }

    /// <summary>
    /// 密钥交换算法
    /// </summary>
    public string? KeyExchangeAlgorithm { get; set; }

    /// <summary>
    /// 服务器证书
    /// </summary>
    public string? ServerCertificate { get; set; }

    /// <summary>
    /// 客户端证书
    /// </summary>
    public string? ClientCertificate { get; set; }

    /// <summary>
    /// 是否相互认证
    /// </summary>
    public bool IsMutuallyAuthenticated { get; set; }

    /// <summary>
    /// 是否已认证
    /// </summary>
    public bool IsAuthenticated { get; set; }

    /// <summary>
    /// 是否已加密
    /// </summary>
    public bool IsEncrypted { get; set; }

    /// <summary>
    /// 是否已签名
    /// </summary>
    public bool IsSigned { get; set; }

    /// <summary>
    /// SSL握手完成时间
    /// </summary>
    public DateTime? HandshakeCompletedAt { get; set; }

    /// <summary>
    /// 重写ToString方法
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $"SSL Protocol:{Protocol} Cipher:{CipherAlgorithm} Authenticated:{IsAuthenticated} Encrypted:{IsEncrypted}";
    }
}
