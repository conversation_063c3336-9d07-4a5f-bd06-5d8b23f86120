namespace Liam.Cryptography.Models;

/// <summary>
/// 密钥类型枚举
/// </summary>
public enum KeyType
{
    /// <summary>
    /// 对称密钥
    /// </summary>
    Symmetric,

    /// <summary>
    /// 非对称公钥
    /// </summary>
    AsymmetricPublic,

    /// <summary>
    /// 非对称私钥
    /// </summary>
    AsymmetricPrivate
}

/// <summary>
/// 对称加密算法枚举
/// </summary>
public enum SymmetricAlgorithm
{
    /// <summary>
    /// AES-128
    /// </summary>
    AES128,

    /// <summary>
    /// AES-192
    /// </summary>
    AES192,

    /// <summary>
    /// AES-256
    /// </summary>
    AES256,

    /// <summary>
    /// DES
    /// </summary>
    DES,

    /// <summary>
    /// 3DES
    /// </summary>
    TripleDES
}

/// <summary>
/// 非对称加密算法枚举
/// </summary>
public enum AsymmetricAlgorithm
{
    /// <summary>
    /// RSA
    /// </summary>
    RSA,

    /// <summary>
    /// DSA
    /// </summary>
    DSA,

    /// <summary>
    /// ECDSA
    /// </summary>
    ECDSA
}

/// <summary>
/// 哈希算法枚举
/// </summary>
public enum HashAlgorithm
{
    /// <summary>
    /// MD5
    /// </summary>
    MD5,

    /// <summary>
    /// SHA1
    /// </summary>
    SHA1,

    /// <summary>
    /// SHA256
    /// </summary>
    SHA256,

    /// <summary>
    /// SHA384
    /// </summary>
    SHA384,

    /// <summary>
    /// SHA512
    /// </summary>
    SHA512
}

/// <summary>
/// 填充模式枚举
/// </summary>
public enum PaddingMode
{
    /// <summary>
    /// PKCS7填充
    /// </summary>
    PKCS7,

    /// <summary>
    /// 零填充
    /// </summary>
    Zeros,

    /// <summary>
    /// 无填充
    /// </summary>
    None,

    /// <summary>
    /// ANSIX923填充
    /// </summary>
    ANSIX923,

    /// <summary>
    /// ISO10126填充
    /// </summary>
    ISO10126
}

/// <summary>
/// 加密模式枚举
/// </summary>
public enum CipherMode
{
    /// <summary>
    /// 电子密码本模式
    /// </summary>
    ECB,

    /// <summary>
    /// 密码块链接模式
    /// </summary>
    CBC,

    /// <summary>
    /// 密码反馈模式
    /// </summary>
    CFB,

    /// <summary>
    /// 输出反馈模式
    /// </summary>
    OFB,

    /// <summary>
    /// 计数器模式
    /// </summary>
    CTR
}
