using Liam.Logging.Exceptions;
using Liam.Logging.Formatters;
using Liam.Logging.Interfaces;
using Liam.Logging.Models;
using System.Text;

namespace Liam.Logging.Providers;

/// <summary>
/// 文件日志提供程序
/// </summary>
public class FileLogProvider : ILogProvider, IAsyncDisposable
{
    private readonly object _lock = new();
    private readonly ILogFormatter _formatter;
    private readonly string _baseFilePath;
    private readonly bool _enableRotation;
    private readonly long _maxFileSize;
    private readonly int _retainedFileCount;
    private readonly TimeSpan? _rotationInterval;
    private readonly Encoding _encoding;
    private readonly bool _autoFlush;
    private readonly int _bufferSize;

    private FileStream? _currentFileStream;
    private StreamWriter? _currentWriter;
    private string _currentFilePath = string.Empty;
    private DateTime _lastRotationCheck = DateTime.UtcNow;
    private bool _disposed;

    /// <summary>
    /// 提供程序名称
    /// </summary>
    public string Name => "File";

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 是否支持异步写入
    /// </summary>
    public bool SupportsAsync => true;

    /// <summary>
    /// 初始化文件日志提供程序
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="formatter">日志格式化器</param>
    /// <param name="enableRotation">是否启用日志轮转</param>
    /// <param name="maxFileSize">最大文件大小（字节）</param>
    /// <param name="retainedFileCount">保留文件数量</param>
    /// <param name="rotationInterval">轮转间隔</param>
    /// <param name="encoding">文件编码</param>
    /// <param name="autoFlush">是否自动刷新</param>
    /// <param name="bufferSize">缓冲区大小</param>
    public FileLogProvider(
        string filePath,
        ILogFormatter? formatter = null,
        bool enableRotation = true,
        long maxFileSize = 10 * 1024 * 1024, // 10MB
        int retainedFileCount = 10,
        TimeSpan? rotationInterval = null,
        Encoding? encoding = null,
        bool autoFlush = true,
        int bufferSize = 4096)
    {
        _baseFilePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
        _formatter = formatter ?? new TextLogFormatter();
        _enableRotation = enableRotation;
        _maxFileSize = maxFileSize;
        _retainedFileCount = retainedFileCount;
        _rotationInterval = rotationInterval ?? TimeSpan.FromDays(1);
        _encoding = encoding ?? Encoding.UTF8;
        _autoFlush = autoFlush;
        _bufferSize = bufferSize;
    }

    /// <summary>
    /// 初始化提供程序
    /// </summary>
    /// <param name="configuration">配置信息</param>
    /// <returns>异步任务</returns>
    public async Task InitializeAsync(LogProviderConfiguration configuration)
    {
        try
        {
            await EnsureFileStreamAsync();
        }
        catch (Exception ex)
        {
            throw new LogProviderException(Name, "初始化文件日志提供程序失败", ex);
        }
    }

    /// <summary>
    /// 写入日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    public void WriteLog(LogEvent logEvent)
    {
        if (!IsEnabled || _disposed)
            return;

        try
        {
            lock (_lock)
            {
                EnsureFileStream();
                CheckRotation();

                var formattedMessage = _formatter.Format(logEvent);
                _currentWriter!.WriteLine(formattedMessage);

                if (_autoFlush)
                {
                    _currentWriter.Flush();
                }
            }
        }
        catch (Exception ex)
        {
            throw new LogFileException(_currentFilePath, "写入文件日志失败", ex);
        }
    }

    /// <summary>
    /// 异步写入日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public async Task WriteLogAsync(LogEvent logEvent, CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();

        if (!IsEnabled || _disposed)
            return;

        try
        {
            await EnsureFileStreamAsync().ConfigureAwait(false);

            cancellationToken.ThrowIfCancellationRequested();

            var formattedMessage = _formatter.Format(logEvent);

            lock (_lock)
            {
                CheckRotation();
            }

            await _currentWriter!.WriteLineAsync(formattedMessage).ConfigureAwait(false);

            if (_autoFlush)
            {
                await _currentWriter.FlushAsync().ConfigureAwait(false);
            }
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw new LogFileException(_currentFilePath, "异步写入文件日志失败", ex);
        }
    }

    /// <summary>
    /// 批量写入日志事件
    /// </summary>
    /// <param name="logEvents">日志事件集合</param>
    public void WriteLogs(IEnumerable<LogEvent> logEvents)
    {
        if (!IsEnabled || _disposed)
            return;

        try
        {
            lock (_lock)
            {
                EnsureFileStream();
                CheckRotation();

                foreach (var logEvent in logEvents)
                {
                    var formattedMessage = _formatter.Format(logEvent);
                    _currentWriter!.WriteLine(formattedMessage);
                }

                if (_autoFlush)
                {
                    _currentWriter!.Flush();
                }
            }
        }
        catch (Exception ex)
        {
            throw new LogFileException(_currentFilePath, "批量写入文件日志失败", ex);
        }
    }

    /// <summary>
    /// 异步批量写入日志事件
    /// </summary>
    /// <param name="logEvents">日志事件集合</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public async Task WriteLogsAsync(IEnumerable<LogEvent> logEvents, CancellationToken cancellationToken = default)
    {
        if (!IsEnabled || _disposed)
            return;

        try
        {
            await EnsureFileStreamAsync().ConfigureAwait(false);

            lock (_lock)
            {
                CheckRotation();
            }

            foreach (var logEvent in logEvents)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var formattedMessage = _formatter.Format(logEvent);
                await _currentWriter!.WriteLineAsync(formattedMessage).ConfigureAwait(false);
            }

            if (_autoFlush)
            {
                await _currentWriter!.FlushAsync().ConfigureAwait(false);
            }
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw new LogFileException(_currentFilePath, "异步批量写入文件日志失败", ex);
        }
    }

    /// <summary>
    /// 刷新缓冲区
    /// </summary>
    public void Flush()
    {
        if (_disposed)
            return;

        lock (_lock)
        {
            _currentWriter?.Flush();
            _currentFileStream?.Flush();
        }
    }

    /// <summary>
    /// 异步刷新缓冲区
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public async Task FlushAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        if (_currentWriter != null)
        {
            await _currentWriter.FlushAsync().ConfigureAwait(false);
        }

        if (_currentFileStream != null)
        {
            await _currentFileStream.FlushAsync(cancellationToken).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                lock (_lock)
                {
                    try
                    {
                        _currentWriter?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"释放文件写入器失败: {ex.Message}");
                    }

                    try
                    {
                        _currentFileStream?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"释放文件流失败: {ex.Message}");
                    }
                }
            }
            _disposed = true;
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    /// <returns>异步任务</returns>
    public async ValueTask DisposeAsync()
    {
        if (!_disposed)
        {
            try
            {
                if (_currentWriter != null)
                {
                    await _currentWriter.FlushAsync().ConfigureAwait(false);
                    await _currentWriter.DisposeAsync().ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"异步释放文件写入器失败: {ex.Message}");
            }

            try
            {
                if (_currentFileStream != null)
                {
                    await _currentFileStream.FlushAsync().ConfigureAwait(false);
                    await _currentFileStream.DisposeAsync().ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"异步释放文件流失败: {ex.Message}");
            }

            _disposed = true;
        }

        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 确保文件流已创建
    /// </summary>
    private void EnsureFileStream()
    {
        if (_currentFileStream == null || _currentWriter == null)
        {
            CreateFileStream();
        }
    }

    /// <summary>
    /// 异步确保文件流已创建
    /// </summary>
    private Task EnsureFileStreamAsync()
    {
        return Task.Run(() =>
        {
            lock (_lock)
            {
                EnsureFileStream();
            }
        });
    }

    /// <summary>
    /// 创建文件流
    /// </summary>
    private void CreateFileStream()
    {
        try
        {
            _currentFilePath = GetCurrentFilePath();
            
            // 确保目录存在
            var directory = Path.GetDirectoryName(_currentFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            _currentFileStream = new FileStream(_currentFilePath, FileMode.Append, FileAccess.Write, FileShare.Read, _bufferSize);
            _currentWriter = new StreamWriter(_currentFileStream, _encoding);
        }
        catch (Exception ex)
        {
            throw new LogFileException(_currentFilePath, "创建文件流失败", ex);
        }
    }

    /// <summary>
    /// 获取当前文件路径
    /// </summary>
    /// <returns>文件路径</returns>
    private string GetCurrentFilePath()
    {
        if (!_enableRotation)
        {
            return _baseFilePath;
        }

        var directory = Path.GetDirectoryName(_baseFilePath) ?? "";
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(_baseFilePath);
        var extension = Path.GetExtension(_baseFilePath);
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd");

        return Path.Combine(directory, $"{fileNameWithoutExtension}_{timestamp}{extension}");
    }

    /// <summary>
    /// 检查是否需要轮转
    /// </summary>
    private void CheckRotation()
    {
        if (!_enableRotation)
            return;

        var now = DateTime.UtcNow;
        var shouldRotate = false;

        // 检查时间间隔
        if (_rotationInterval.HasValue && now - _lastRotationCheck >= _rotationInterval.Value)
        {
            shouldRotate = true;
        }

        // 检查文件大小
        if (_currentFileStream != null && _currentFileStream.Length >= _maxFileSize)
        {
            shouldRotate = true;
        }

        if (shouldRotate)
        {
            RotateFile();
            _lastRotationCheck = now;
        }
    }

    /// <summary>
    /// 轮转文件
    /// </summary>
    private void RotateFile()
    {
        try
        {
            // 关闭当前文件
            _currentWriter?.Dispose();
            _currentFileStream?.Dispose();

            // 清理旧文件
            CleanupOldFiles();

            // 创建新文件
            CreateFileStream();
        }
        catch (Exception ex)
        {
            throw new LogFileException(_currentFilePath, "文件轮转失败", ex);
        }
    }

    /// <summary>
    /// 清理旧文件
    /// </summary>
    private void CleanupOldFiles()
    {
        try
        {
            var directory = Path.GetDirectoryName(_baseFilePath);
            if (string.IsNullOrEmpty(directory) || !Directory.Exists(directory))
                return;

            var fileNamePattern = Path.GetFileNameWithoutExtension(_baseFilePath) + "_*" + Path.GetExtension(_baseFilePath);
            var files = Directory.GetFiles(directory, fileNamePattern)
                .OrderByDescending(f => File.GetCreationTime(f))
                .Skip(_retainedFileCount)
                .ToList();

            foreach (var file in files)
            {
                try
                {
                    File.Delete(file);
                }
                catch
                {
                    // 忽略删除失败的文件
                }
            }
        }
        catch
        {
            // 忽略清理失败
        }
    }
}
