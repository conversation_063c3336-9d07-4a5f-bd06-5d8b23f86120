using Liam.SerialPort.Events;
using <PERSON>.SerialPort.Models;

namespace Liam.SerialPort.Interfaces;

/// <summary>
/// 串口连接管理接口，提供串口连接的建立、维护和断开功能
/// </summary>
public interface ISerialPortConnection : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 获取当前连接状态
    /// </summary>
    ConnectionStatus Status { get; }

    /// <summary>
    /// 获取当前串口设置
    /// </summary>
    SerialPortSettings? Settings { get; }

    /// <summary>
    /// 获取当前连接的串口信息
    /// </summary>
    SerialPortInfo? PortInfo { get; }

    /// <summary>
    /// 获取是否已连接
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// 获取连接建立时间
    /// </summary>
    DateTime? ConnectedAt { get; }

    /// <summary>
    /// 获取最后活动时间
    /// </summary>
    DateTime? LastActivity { get; }

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    event EventHandler<ConnectionStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 连接错误事件
    /// </summary>
    event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 建立串口连接
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="settings">串口设置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否成功</returns>
    Task<bool> ConnectAsync(string portName, SerialPortSettings settings, CancellationToken cancellationToken = default);

    /// <summary>
    /// 建立串口连接
    /// </summary>
    /// <param name="portInfo">串口信息</param>
    /// <param name="settings">串口设置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否成功</returns>
    Task<bool> ConnectAsync(SerialPortInfo portInfo, SerialPortSettings settings, CancellationToken cancellationToken = default);

    /// <summary>
    /// 断开串口连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task DisconnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 重新连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重连是否成功</returns>
    Task<bool> ReconnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 测试连接是否正常
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否正常</returns>
    Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取连接统计信息
    /// </summary>
    /// <returns>连接统计信息</returns>
    ConnectionStatistics GetStatistics();
}
