using Liam.Logging.Interfaces;
using Liam.Logging.Models;
using System.Collections.Concurrent;

namespace Liam.Logging.Services;

/// <summary>
/// Liam日志工厂实现
/// </summary>
public class LiamLoggerFactory : ILiamLoggerFactory, IAsyncDisposable
{
    private readonly ConcurrentDictionary<string, ILiamLogger> _loggers = new();
    private readonly List<ILogProvider> _providers = new();
    private readonly List<ILogFilter> _filters = new();
    private readonly ILogScopeProvider _scopeProvider;
    private readonly ILogPerformanceMonitor _performanceMonitor;
    private readonly LogConfiguration _configuration;
    private readonly object _lock = new();
    private bool _disposed;

    /// <summary>
    /// 初始化日志工厂
    /// </summary>
    /// <param name="configuration">日志配置</param>
    /// <param name="scopeProvider">作用域提供程序</param>
    /// <param name="performanceMonitor">性能监控器</param>
    public LiamLoggerFactory(
        LogConfiguration? configuration = null,
        ILogScopeProvider? scopeProvider = null,
        ILogPerformanceMonitor? performanceMonitor = null)
    {
        _configuration = configuration ?? new LogConfiguration();
        _scopeProvider = scopeProvider ?? new LogScopeProvider();
        _performanceMonitor = performanceMonitor ?? new LogPerformanceMonitor();
    }

    /// <summary>
    /// 创建日志记录器
    /// </summary>
    /// <param name="categoryName">类别名称</param>
    /// <returns>日志记录器</returns>
    public ILiamLogger CreateLogger(string categoryName)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LiamLoggerFactory));

        return _loggers.GetOrAdd(categoryName, name =>
        {
            var logger = new LiamLogger(name, this, _scopeProvider, _filters);
            logger.MinimumLevel = _configuration.MinimumLevel;
            return logger;
        });
    }

    /// <summary>
    /// 创建泛型日志记录器
    /// </summary>
    /// <typeparam name="T">类型</typeparam>
    /// <returns>日志记录器</returns>
    public ILiamLogger<T> CreateLogger<T>()
    {
        var categoryName = typeof(T).FullName ?? typeof(T).Name;
        var logger = CreateLogger(categoryName);
        
        // 如果已存在的不是泛型版本，创建新的泛型版本
        if (logger is not ILiamLogger<T> genericLogger)
        {
            genericLogger = new LiamLogger<T>(this, _scopeProvider, _filters);
            genericLogger.MinimumLevel = _configuration.MinimumLevel;
            _loggers.TryUpdate(categoryName, genericLogger, logger);
        }

        return genericLogger;
    }

    /// <summary>
    /// 添加日志提供程序
    /// </summary>
    /// <param name="provider">日志提供程序</param>
    public void AddProvider(ILogProvider provider)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LiamLoggerFactory));

        if (provider == null)
            throw new ArgumentNullException(nameof(provider));

        lock (_lock)
        {
            if (!_providers.Contains(provider))
            {
                _providers.Add(provider);
            }
        }
    }

    /// <summary>
    /// 移除日志提供程序
    /// </summary>
    /// <param name="provider">日志提供程序</param>
    public void RemoveProvider(ILogProvider provider)
    {
        if (_disposed)
            return;

        if (provider == null)
            return;

        lock (_lock)
        {
            _providers.Remove(provider);
        }
    }

    /// <summary>
    /// 获取所有日志提供程序
    /// </summary>
    /// <returns>日志提供程序集合</returns>
    public IEnumerable<ILogProvider> GetProviders()
    {
        lock (_lock)
        {
            return _providers.ToList();
        }
    }

    /// <summary>
    /// 添加日志过滤器
    /// </summary>
    /// <param name="filter">日志过滤器</param>
    public void AddFilter(ILogFilter filter)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LiamLoggerFactory));

        if (filter == null)
            throw new ArgumentNullException(nameof(filter));

        lock (_lock)
        {
            if (!_filters.Contains(filter))
            {
                _filters.Add(filter);
            }
        }
    }

    /// <summary>
    /// 移除日志过滤器
    /// </summary>
    /// <param name="filter">日志过滤器</param>
    public void RemoveFilter(ILogFilter filter)
    {
        if (_disposed)
            return;

        if (filter == null)
            return;

        lock (_lock)
        {
            _filters.Remove(filter);
        }
    }

    /// <summary>
    /// 获取所有日志过滤器
    /// </summary>
    /// <returns>日志过滤器集合</returns>
    public IEnumerable<ILogFilter> GetFilters()
    {
        lock (_lock)
        {
            return _filters.ToList();
        }
    }

    /// <summary>
    /// 刷新所有提供程序
    /// </summary>
    public void Flush()
    {
        if (_disposed)
            return;

        var providers = GetProviders();
        foreach (var provider in providers)
        {
            try
            {
                provider.Flush();
            }
            catch
            {
                // 忽略刷新失败
            }
        }
    }

    /// <summary>
    /// 异步刷新所有提供程序
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public async Task FlushAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        var providers = GetProviders();
        var tasks = providers.Select(async provider =>
        {
            try
            {
                await provider.FlushAsync(cancellationToken).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                // 记录刷新失败，但不抛出异常
                System.Diagnostics.Debug.WriteLine($"刷新日志提供程序失败: {provider.Name}, 错误: {ex.Message}");
            }
        });

        await Task.WhenAll(tasks).ConfigureAwait(false);
    }

    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    /// <returns>性能统计信息</returns>
    public Dictionary<string, object> GetPerformanceStats()
    {
        return _performanceMonitor.GetPerformanceStats();
    }

    /// <summary>
    /// 获取工厂统计信息
    /// </summary>
    /// <returns>工厂统计信息</returns>
    public Dictionary<string, object> GetFactoryStats()
    {
        lock (_lock)
        {
            return new Dictionary<string, object>
            {
                ["totalLoggers"] = _loggers.Count,
                ["totalProviders"] = _providers.Count,
                ["totalFilters"] = _filters.Count,
                ["enabledProviders"] = _providers.Count(p => p.IsEnabled),
                ["configuration"] = new Dictionary<string, object>
                {
                    ["minimumLevel"] = _configuration.MinimumLevel.ToString(),
                    ["enableAsync"] = _configuration.EnableAsync,
                    ["asyncQueueSize"] = _configuration.AsyncQueueSize,
                    ["batchSize"] = _configuration.BatchSize,
                    ["batchTimeoutMs"] = _configuration.BatchTimeoutMs,
                    ["includeScopes"] = _configuration.IncludeScopes,
                    ["includeExceptionDetails"] = _configuration.IncludeExceptionDetails,
                    ["includeSourceInfo"] = _configuration.IncludeSourceInfo
                }
            };
        }
    }

    /// <summary>
    /// 重新配置工厂
    /// </summary>
    /// <param name="configuration">新的配置</param>
    public void Reconfigure(LogConfiguration configuration)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(LiamLoggerFactory));

        if (configuration == null)
            throw new ArgumentNullException(nameof(configuration));

        lock (_lock)
        {
            // 更新配置
            _configuration.MinimumLevel = configuration.MinimumLevel;
            _configuration.EnableAsync = configuration.EnableAsync;
            _configuration.AsyncQueueSize = configuration.AsyncQueueSize;
            _configuration.BatchSize = configuration.BatchSize;
            _configuration.BatchTimeoutMs = configuration.BatchTimeoutMs;
            _configuration.IncludeScopes = configuration.IncludeScopes;
            _configuration.IncludeExceptionDetails = configuration.IncludeExceptionDetails;
            _configuration.IncludeSourceInfo = configuration.IncludeSourceInfo;

            // 更新所有现有日志记录器的最小级别
            foreach (var logger in _loggers.Values)
            {
                logger.MinimumLevel = configuration.MinimumLevel;
            }
        }
    }

    /// <summary>
    /// 清除所有缓存的日志记录器
    /// </summary>
    public void ClearLoggers()
    {
        if (_disposed)
            return;

        _loggers.Clear();
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 刷新所有提供程序
                try
                {
                    Flush();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"刷新日志提供程序时发生错误: {ex.Message}");
                }

                // 释放所有提供程序
                lock (_lock)
                {
                    foreach (var provider in _providers)
                    {
                        try
                        {
                            provider.Dispose();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"释放日志提供程序失败: {provider.Name}, 错误: {ex.Message}");
                        }
                    }
                    _providers.Clear();
                    _filters.Clear();
                }

                // 清除所有日志记录器
                _loggers.Clear();
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    /// <returns>异步任务</returns>
    public async ValueTask DisposeAsync()
    {
        if (!_disposed)
        {
            try
            {
                await FlushAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"异步刷新日志提供程序时发生错误: {ex.Message}");
            }

            // 释放所有提供程序
            lock (_lock)
            {
                foreach (var provider in _providers)
                {
                    try
                    {
                        if (provider is IAsyncDisposable asyncDisposable)
                        {
                            // 注意：这里不能在lock中使用await，所以收集后在外面处理
                            continue;
                        }
                        provider.Dispose();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"释放日志提供程序失败: {provider.Name}, 错误: {ex.Message}");
                    }
                }
            }

            // 处理异步释放的提供程序
            var asyncProviders = GetProviders().OfType<IAsyncDisposable>().ToList();
            foreach (var provider in asyncProviders)
            {
                try
                {
                    await provider.DisposeAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"异步释放日志提供程序失败: 错误: {ex.Message}");
                }
            }

            lock (_lock)
            {
                _providers.Clear();
                _filters.Clear();
            }

            // 清除所有日志记录器
            _loggers.Clear();
            _disposed = true;
        }

        GC.SuppressFinalize(this);
    }
}
