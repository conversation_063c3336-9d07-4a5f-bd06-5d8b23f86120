namespace Liam.Logging.Constants;

/// <summary>
/// 定义日志级别枚举
/// </summary>
public enum LogLevel
{
    /// <summary>
    /// 跟踪级别 - 最详细的日志信息，通常只在开发时使用
    /// </summary>
    Trace = 0,

    /// <summary>
    /// 调试级别 - 调试信息，通常只在开发和测试时使用
    /// </summary>
    Debug = 1,

    /// <summary>
    /// 信息级别 - 一般信息，记录程序的正常运行流程
    /// </summary>
    Information = 2,

    /// <summary>
    /// 警告级别 - 警告信息，表示可能存在问题但不影响程序运行
    /// </summary>
    Warning = 3,

    /// <summary>
    /// 错误级别 - 错误信息，表示发生了错误但程序可以继续运行
    /// </summary>
    Error = 4,

    /// <summary>
    /// 严重错误级别 - 严重错误，可能导致程序崩溃
    /// </summary>
    Critical = 5,

    /// <summary>
    /// 无日志 - 禁用所有日志记录
    /// </summary>
    None = 6
}

/// <summary>
/// 日志级别相关的常量和工具方法
/// </summary>
public static class LogLevelConstants
{
    /// <summary>
    /// 日志级别名称映射
    /// </summary>
    public static readonly Dictionary<LogLevel, string> LevelNames = new()
    {
        { LogLevel.Trace, "TRACE" },
        { LogLevel.Debug, "DEBUG" },
        { LogLevel.Information, "INFO" },
        { LogLevel.Warning, "WARN" },
        { LogLevel.Error, "ERROR" },
        { LogLevel.Critical, "FATAL" },
        { LogLevel.None, "NONE" }
    };

    /// <summary>
    /// 日志级别颜色映射（用于控制台输出）
    /// </summary>
    public static readonly Dictionary<LogLevel, ConsoleColor> LevelColors = new()
    {
        { LogLevel.Trace, ConsoleColor.Gray },
        { LogLevel.Debug, ConsoleColor.Blue },
        { LogLevel.Information, ConsoleColor.Green },
        { LogLevel.Warning, ConsoleColor.Yellow },
        { LogLevel.Error, ConsoleColor.Red },
        { LogLevel.Critical, ConsoleColor.Magenta },
        { LogLevel.None, ConsoleColor.White }
    };

    /// <summary>
    /// 获取日志级别的显示名称
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <returns>显示名称</returns>
    public static string GetLevelName(LogLevel level)
    {
        return LevelNames.TryGetValue(level, out var name) ? name : level.ToString().ToUpper();
    }

    /// <summary>
    /// 获取日志级别的控制台颜色
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <returns>控制台颜色</returns>
    public static ConsoleColor GetLevelColor(LogLevel level)
    {
        return LevelColors.TryGetValue(level, out var color) ? color : ConsoleColor.White;
    }

    /// <summary>
    /// 判断指定级别是否应该记录日志
    /// </summary>
    /// <param name="currentLevel">当前日志级别</param>
    /// <param name="minimumLevel">最小日志级别</param>
    /// <returns>是否应该记录</returns>
    public static bool IsEnabled(LogLevel currentLevel, LogLevel minimumLevel)
    {
        return currentLevel >= minimumLevel && minimumLevel != LogLevel.None;
    }
}
