using System.Net;
using System.Security.Cryptography.X509Certificates;
using Liam.TcpServer.Constants;

namespace Liam.TcpServer.Models;

/// <summary>
/// TCP服务器配置
/// </summary>
public class TcpServerConfig
{
    /// <summary>
    /// 服务器监听端口
    /// </summary>
    public int Port { get; set; } = TcpServerConstants.Defaults.Port;

    /// <summary>
    /// 服务器监听IP地址，默认为所有可用地址
    /// </summary>
    public IPAddress ListenAddress { get; set; } = IPAddress.Any;

    /// <summary>
    /// 最大并发连接数
    /// </summary>
    public int MaxConnections { get; set; } = TcpServerConstants.Defaults.MaxConnections;

    /// <summary>
    /// 接收缓冲区大小（字节）
    /// </summary>
    public int ReceiveBufferSize { get; set; } = TcpServerConstants.Defaults.ReceiveBufferSize;

    /// <summary>
    /// 发送缓冲区大小（字节）
    /// </summary>
    public int SendBufferSize { get; set; } = TcpServerConstants.Defaults.SendBufferSize;

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    public int ConnectionTimeoutSeconds { get; set; } = TcpServerConstants.Defaults.ConnectionTimeoutSeconds;

    /// <summary>
    /// 连接队列长度
    /// </summary>
    public int ConnectionBacklog { get; set; } = TcpServerConstants.Defaults.ConnectionBacklog;

    /// <summary>
    /// 是否启用心跳检测
    /// </summary>
    public bool EnableHeartbeat { get; set; } = true;

    /// <summary>
    /// 心跳间隔（秒）
    /// </summary>
    public int HeartbeatIntervalSeconds { get; set; } = TcpServerConstants.Defaults.HeartbeatIntervalSeconds;

    /// <summary>
    /// 心跳超时时间（秒）
    /// </summary>
    public int HeartbeatTimeoutSeconds { get; set; } = TcpServerConstants.Defaults.HeartbeatTimeoutSeconds;

    /// <summary>
    /// 消息最大长度（字节）
    /// </summary>
    public int MaxMessageLength { get; set; } = TcpServerConstants.Defaults.MaxMessageLength;

    /// <summary>
    /// 是否启用SSL/TLS
    /// </summary>
    public bool EnableSsl { get; set; } = false;

    /// <summary>
    /// SSL证书
    /// </summary>
    public X509Certificate2? SslCertificate { get; set; }

    /// <summary>
    /// SSL握手超时时间（秒）
    /// </summary>
    public int SslHandshakeTimeoutSeconds { get; set; } = TcpServerConstants.Security.SslHandshakeTimeoutSeconds;

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 统计信息更新间隔（秒）
    /// </summary>
    public int StatisticsUpdateIntervalSeconds { get; set; } = TcpServerConstants.Performance.StatisticsUpdateIntervalSeconds;

    /// <summary>
    /// 是否启用日志记录
    /// </summary>
    public bool EnableLogging { get; set; } = true;

    /// <summary>
    /// 安全设置
    /// </summary>
    public SecuritySettings Security { get; set; } = new();

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        if (Port <= 0 || Port > 65535)
        {
            errors.Add($"端口号必须在1-65535范围内，当前值：{Port}");
        }

        if (MaxConnections <= 0)
        {
            errors.Add($"最大连接数必须大于0，当前值：{MaxConnections}");
        }

        if (ReceiveBufferSize <= 0)
        {
            errors.Add($"接收缓冲区大小必须大于0，当前值：{ReceiveBufferSize}");
        }

        if (SendBufferSize <= 0)
        {
            errors.Add($"发送缓冲区大小必须大于0，当前值：{SendBufferSize}");
        }

        if (ConnectionTimeoutSeconds <= 0)
        {
            errors.Add($"连接超时时间必须大于0，当前值：{ConnectionTimeoutSeconds}");
        }

        if (EnableHeartbeat)
        {
            if (HeartbeatIntervalSeconds <= 0)
            {
                errors.Add($"心跳间隔必须大于0，当前值：{HeartbeatIntervalSeconds}");
            }

            if (HeartbeatTimeoutSeconds <= 0)
            {
                errors.Add($"心跳超时时间必须大于0，当前值：{HeartbeatTimeoutSeconds}");
            }

            if (HeartbeatTimeoutSeconds >= HeartbeatIntervalSeconds)
            {
                errors.Add($"心跳超时时间({HeartbeatTimeoutSeconds})必须小于心跳间隔({HeartbeatIntervalSeconds})");
            }
        }

        if (MaxMessageLength <= 0)
        {
            errors.Add($"消息最大长度必须大于0，当前值：{MaxMessageLength}");
        }

        if (EnableSsl && SslCertificate == null)
        {
            errors.Add("启用SSL时必须提供SSL证书");
        }

        if (SslHandshakeTimeoutSeconds <= 0)
        {
            errors.Add($"SSL握手超时时间必须大于0，当前值：{SslHandshakeTimeoutSeconds}");
        }

        if (StatisticsUpdateIntervalSeconds <= 0)
        {
            errors.Add($"统计信息更新间隔必须大于0，当前值：{StatisticsUpdateIntervalSeconds}");
        }

        // 验证安全设置
        var securityValidation = Security.Validate();
        if (!securityValidation.IsValid)
        {
            errors.AddRange(securityValidation.Errors);
        }

        return new ValidationResult(errors.Count == 0, errors);
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <returns>默认配置实例</returns>
    public static TcpServerConfig CreateDefault()
    {
        return new TcpServerConfig();
    }

    /// <summary>
    /// 创建SSL配置
    /// </summary>
    /// <param name="certificate">SSL证书</param>
    /// <param name="port">端口号</param>
    /// <returns>SSL配置实例</returns>
    public static TcpServerConfig CreateSslConfig(X509Certificate2 certificate, int port = 8443)
    {
        return new TcpServerConfig
        {
            Port = port,
            EnableSsl = true,
            SslCertificate = certificate
        };
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; }

    /// <summary>
    /// 验证错误列表
    /// </summary>
    public IReadOnlyList<string> Errors { get; }

    /// <summary>
    /// 初始化验证结果
    /// </summary>
    /// <param name="isValid">是否验证通过</param>
    /// <param name="errors">验证错误列表</param>
    public ValidationResult(bool isValid, IEnumerable<string> errors)
    {
        IsValid = isValid;
        Errors = errors.ToList().AsReadOnly();
    }

    /// <summary>
    /// 创建成功的验证结果
    /// </summary>
    /// <returns>成功的验证结果</returns>
    public static ValidationResult Success()
    {
        return new ValidationResult(true, Array.Empty<string>());
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="errors">错误列表</param>
    /// <returns>失败的验证结果</returns>
    public static ValidationResult Failure(params string[] errors)
    {
        return new ValidationResult(false, errors);
    }
}
