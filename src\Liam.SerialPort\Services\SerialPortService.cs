using Microsoft.Extensions.Logging;
using Liam.SerialPort.Events;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;

namespace Liam.SerialPort.Services;

/// <summary>
/// 串口服务主实现类
/// </summary>
public class SerialPortService : ISerialPortService
{
    private readonly ILogger<SerialPortService> _logger;
    private readonly ISerialPortDiscovery _discovery;
    private readonly ISerialPortConnection _connection;
    private readonly ISerialPortDataHandler _dataHandler;
    private readonly object _lock = new();
    
    private bool _disposed;

    /// <summary>
    /// 获取当前连接状态
    /// </summary>
    public ConnectionStatus Status => _connection.Status;

    /// <summary>
    /// 获取当前串口设置
    /// </summary>
    public SerialPortSettings? Settings => _connection.Settings;

    /// <summary>
    /// 获取当前连接的串口信息
    /// </summary>
    public SerialPortInfo? CurrentPort => _connection.PortInfo;

    /// <summary>
    /// 获取是否已连接
    /// </summary>
    public bool IsConnected => _connection.IsConnected;

    /// <summary>
    /// 获取或设置是否启用自动重连
    /// </summary>
    public bool AutoReconnectEnabled { get; set; } = true;

    /// <summary>
    /// 获取接收缓冲区中的字节数
    /// </summary>
    public int BytesToRead => _dataHandler.BytesToRead;

    /// <summary>
    /// 获取发送缓冲区中的字节数
    /// </summary>
    public int BytesToWrite => _dataHandler.BytesToWrite;

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    public event EventHandler<ConnectionStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    public event EventHandler<DataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 错误发生事件
    /// </summary>
    public event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 设备热插拔事件
    /// </summary>
    public event EventHandler<DeviceChangedEventArgs>? DeviceChanged;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="discovery">设备发现服务</param>
    /// <param name="connection">连接管理服务</param>
    /// <param name="dataHandler">数据处理服务</param>
    public SerialPortService(
        ILogger<SerialPortService> logger,
        ISerialPortDiscovery discovery,
        ISerialPortConnection connection,
        ISerialPortDataHandler dataHandler)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _discovery = discovery ?? throw new ArgumentNullException(nameof(discovery));
        _connection = connection ?? throw new ArgumentNullException(nameof(connection));
        _dataHandler = dataHandler ?? throw new ArgumentNullException(nameof(dataHandler));

        // 订阅事件
        _discovery.DeviceChanged += OnDeviceChanged;
        _connection.StatusChanged += OnStatusChanged;
        _connection.ErrorOccurred += OnConnectionError;
        _dataHandler.DataReceived += OnDataReceived;
        _dataHandler.ErrorOccurred += OnDataHandlerError;

        _logger.LogInformation("串口服务已初始化");
    }

    /// <summary>
    /// 获取可用的串口列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>串口信息列表</returns>
    public async Task<IEnumerable<SerialPortInfo>> GetAvailablePortsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _discovery.GetAvailablePortsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可用串口列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 连接到指定串口
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="settings">串口设置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否成功</returns>
    public async Task<bool> ConnectAsync(string portName, SerialPortSettings settings, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(portName))
            throw new ArgumentException("串口名称不能为空", nameof(portName));

        if (settings == null)
            throw new ArgumentNullException(nameof(settings));

        try
        {
            _logger.LogInformation("开始连接串口 {PortName}", portName);

            // 应用自动重连设置
            settings.AutoReconnect = AutoReconnectEnabled;

            var success = await _connection.ConnectAsync(portName, settings, cancellationToken);
            
            if (success)
            {
                // 通过连接对象获取串口并设置到数据处理器
                if (_connection is SerialPortConnection connection &&
                    _dataHandler is SerialPortDataHandler dataHandler)
                {
                    var serialPort = connection.GetSerialPort();
                    if (serialPort != null)
                    {
                        dataHandler.SetSerialPort(serialPort, portName);
                    }
                }

                // 开始数据监听
                _dataHandler.StartListening();

                // 开始设备监控
                if (!_discovery.IsMonitoring)
                {
                    await _discovery.StartMonitoringAsync(cancellationToken);
                }

                _logger.LogInformation("串口 {PortName} 连接成功", portName);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接串口 {PortName} 时发生错误", portName);
            throw;
        }
    }

    /// <summary>
    /// 连接到指定串口
    /// </summary>
    /// <param name="portInfo">串口信息</param>
    /// <param name="settings">串口设置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否成功</returns>
    public async Task<bool> ConnectAsync(SerialPortInfo portInfo, SerialPortSettings settings, CancellationToken cancellationToken = default)
    {
        if (portInfo == null)
            throw new ArgumentNullException(nameof(portInfo));

        return await ConnectAsync(portInfo.PortName, settings, cancellationToken);
    }

    /// <summary>
    /// 断开连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task DisconnectAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始断开串口连接");

            // 停止数据监听
            _dataHandler.StopListening();

            // 断开连接
            await _connection.DisconnectAsync(cancellationToken);

            _logger.LogInformation("串口连接已断开");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "断开串口连接时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 发送数据（字节数组）
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task SendAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
            throw new InvalidOperationException("串口未连接");

        await _dataHandler.SendAsync(data, cancellationToken);
    }

    /// <summary>
    /// 发送数据（字符串）
    /// </summary>
    /// <param name="data">要发送的字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task SendAsync(string data, CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
            throw new InvalidOperationException("串口未连接");

        await _dataHandler.SendAsync(data, null, cancellationToken);
    }

    /// <summary>
    /// 发送数据并等待响应
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收到的响应数据</returns>
    public async Task<byte[]> SendAndReceiveAsync(byte[] data, TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
            throw new InvalidOperationException("串口未连接");

        return await _dataHandler.SendAndReceiveAsync(data, timeout, cancellationToken);
    }

    /// <summary>
    /// 发送数据并等待响应
    /// </summary>
    /// <param name="data">要发送的字符串</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收到的响应字符串</returns>
    public async Task<string> SendAndReceiveAsync(string data, TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
            throw new InvalidOperationException("串口未连接");

        return await _dataHandler.SendAndReceiveAsync(data, timeout, null, cancellationToken);
    }

    /// <summary>
    /// 清空接收缓冲区
    /// </summary>
    public void ClearReceiveBuffer()
    {
        _dataHandler.ClearReceiveBuffer();
    }

    /// <summary>
    /// 清空发送缓冲区
    /// </summary>
    public void ClearSendBuffer()
    {
        _dataHandler.ClearSendBuffer();
    }

    /// <summary>
    /// 设备变化事件处理
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnDeviceChanged(object? sender, DeviceChangedEventArgs e)
    {
        try
        {
            DeviceChanged?.Invoke(this, e);
            
            // 如果当前连接的设备被移除，触发断开连接
            if (e.ChangeType == DeviceChangeType.Removed && 
                IsConnected && 
                CurrentPort?.PortName == e.DeviceInfo.PortName)
            {
                _logger.LogWarning("当前连接的设备 {PortName} 已被移除", e.DeviceInfo.PortName);
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await DisconnectAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "设备移除后断开连接时发生错误");
                    }
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备变化事件时发生错误");
        }
    }

    /// <summary>
    /// 连接状态变化事件处理
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnStatusChanged(object? sender, ConnectionStatusChangedEventArgs e)
    {
        try
        {
            StatusChanged?.Invoke(this, e);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理状态变化事件时发生错误");
        }
    }

    /// <summary>
    /// 数据接收事件处理
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnDataReceived(object? sender, DataReceivedEventArgs e)
    {
        try
        {
            DataReceived?.Invoke(this, e);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理数据接收事件时发生错误");
        }
    }

    /// <summary>
    /// 连接错误事件处理
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnConnectionError(object? sender, SerialPortErrorEventArgs e)
    {
        try
        {
            ErrorOccurred?.Invoke(this, e);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理连接错误事件时发生错误");
        }
    }

    /// <summary>
    /// 数据处理错误事件处理
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnDataHandlerError(object? sender, SerialPortErrorEventArgs e)
    {
        try
        {
            ErrorOccurred?.Invoke(this, e);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理数据处理错误事件时发生错误");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            DisconnectAsync().Wait(5000);
            
            // 取消事件订阅
            _discovery.DeviceChanged -= OnDeviceChanged;
            _connection.StatusChanged -= OnStatusChanged;
            _connection.ErrorOccurred -= OnConnectionError;
            _dataHandler.DataReceived -= OnDataReceived;
            _dataHandler.ErrorOccurred -= OnDataHandlerError;

            // 释放子服务
            _discovery.Dispose();
            _connection.Dispose();
            _dataHandler.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放SerialPortService资源时发生错误");
        }
        finally
        {
            _disposed = true;
        }
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        if (_disposed)
            return;

        try
        {
            await DisconnectAsync();
            
            // 取消事件订阅
            _discovery.DeviceChanged -= OnDeviceChanged;
            _connection.StatusChanged -= OnStatusChanged;
            _connection.ErrorOccurred -= OnConnectionError;
            _dataHandler.DataReceived -= OnDataReceived;
            _dataHandler.ErrorOccurred -= OnDataHandlerError;

            // 释放子服务
            _discovery.Dispose();
            await _connection.DisposeAsync();
            _dataHandler.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "异步释放SerialPortService资源时发生错误");
        }
        finally
        {
            _disposed = true;
        }
    }
}
