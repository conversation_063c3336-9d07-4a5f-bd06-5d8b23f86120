using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Liam.SerialPort.Extensions;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;
using Liam.SerialPort.Services;

namespace Liam.Examples;

/// <summary>
/// Liam.SerialPort 使用示例
/// </summary>
public class SerialPortExample
{
    /// <summary>
    /// 基础使用示例
    /// </summary>
    public static async Task BasicUsageExample()
    {
        Console.WriteLine("=== Liam.SerialPort 基础使用示例 ===\n");

        // 创建日志记录器
        using var loggerFactory = LoggerFactory.Create(builder => 
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        // 创建串口服务
        var discovery = new SerialPortDiscovery(loggerFactory.CreateLogger<SerialPortDiscovery>());
        var connection = new SerialPortConnection(loggerFactory.CreateLogger<SerialPortConnection>());
        var dataHandler = new SerialPortDataHandler(loggerFactory.CreateLogger<SerialPortDataHandler>());
        var serialPortService = new SerialPortService(
            loggerFactory.CreateLogger<SerialPortService>(),
            discovery, connection, dataHandler);

        try
        {
            // 1. 获取可用串口
            Console.WriteLine("1. 获取可用串口列表：");
            var ports = await serialPortService.GetAvailablePortsAsync();
            
            if (!ports.Any())
            {
                Console.WriteLine("   未发现可用的串口设备");
                return;
            }

            foreach (var port in ports)
            {
                Console.WriteLine($"   - {port.DisplayName}");
                Console.WriteLine($"     描述：{port.Description}");
                Console.WriteLine($"     制造商：{port.Manufacturer}");
                Console.WriteLine($"     是否USB设备：{port.IsUsbDevice}");
                Console.WriteLine();
            }

            // 2. 选择第一个可用串口进行连接
            var selectedPort = ports.First();
            Console.WriteLine($"2. 连接到串口：{selectedPort.PortName}");

            // 创建串口设置
            var settings = SerialPortExtensions.CreateSettings(115200);
            settings.AutoReconnect = true;
            settings.ReadTimeout = 3000;
            settings.WriteTimeout = 3000;

            // 订阅事件
            serialPortService.StatusChanged += (sender, e) =>
            {
                Console.WriteLine($"   状态变化：{e.OldStatus} -> {e.NewStatus}");
            };

            serialPortService.DataReceived += (sender, e) =>
            {
                Console.WriteLine($"   接收数据：{e.DataAsString}");
                Console.WriteLine($"   十六进制：{e.DataAsHex}");
            };

            serialPortService.ErrorOccurred += (sender, e) =>
            {
                Console.WriteLine($"   发生错误：{e.ErrorType} - {e.Message}");
            };

            // 连接串口
            var connected = await serialPortService.ConnectAsync(selectedPort, settings);
            
            if (connected)
            {
                Console.WriteLine("   连接成功！");

                // 3. 发送测试数据
                Console.WriteLine("\n3. 发送测试数据：");
                
                // 发送字符串
                await serialPortService.SendAsync("Hello, Serial Port!");
                Console.WriteLine("   已发送：Hello, Serial Port!");

                // 发送十六进制数据
                await serialPortService.SendHexAsync("48 65 6C 6C 6F");
                Console.WriteLine("   已发送十六进制：48 65 6C 6C 6F");

                // 等待一段时间接收数据
                await Task.Delay(2000);

                // 4. 断开连接
                Console.WriteLine("\n4. 断开连接");
                await serialPortService.DisconnectAsync();
                Console.WriteLine("   连接已断开");
            }
            else
            {
                Console.WriteLine("   连接失败！");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"发生错误：{ex.Message}");
        }
        finally
        {
            serialPortService.Dispose();
        }
    }

    /// <summary>
    /// 依赖注入使用示例
    /// </summary>
    public static async Task DependencyInjectionExample()
    {
        Console.WriteLine("\n=== 依赖注入使用示例 ===\n");

        // 创建主机
        var host = Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                // 添加串口服务
                services.AddSerialPort();
                
                // 配置日志
                services.AddLogging(builder =>
                    builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            })
            .Build();

        try
        {
            // 获取串口服务
            var serialPortService = host.Services.GetRequiredService<ISerialPortService>();

            // 获取可用串口
            var ports = await serialPortService.GetAvailablePortsAsync();
            Console.WriteLine($"发现 {ports.Count()} 个可用串口");

            foreach (var port in ports)
            {
                Console.WriteLine($"- {port.PortName}: {port.Description}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"发生错误：{ex.Message}");
        }
        finally
        {
            await host.StopAsync();
            host.Dispose();
        }
    }

    /// <summary>
    /// 高级功能示例
    /// </summary>
    public static async Task AdvancedFeaturesExample()
    {
        Console.WriteLine("\n=== 高级功能示例 ===\n");

        using var loggerFactory = LoggerFactory.Create(builder => 
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        var discovery = new SerialPortDiscovery(loggerFactory.CreateLogger<SerialPortDiscovery>());
        var connection = new SerialPortConnection(loggerFactory.CreateLogger<SerialPortConnection>());
        var dataHandler = new SerialPortDataHandler(loggerFactory.CreateLogger<SerialPortDataHandler>());
        var serialPortService = new SerialPortService(
            loggerFactory.CreateLogger<SerialPortService>(),
            discovery, connection, dataHandler);

        try
        {
            // 1. 设备热插拔监控
            Console.WriteLine("1. 启动设备热插拔监控：");
            
            serialPortService.DeviceChanged += (sender, e) =>
            {
                switch (e.ChangeType)
                {
                    case DeviceChangeType.Inserted:
                        Console.WriteLine($"   设备插入：{e.DeviceInfo.PortName}");
                        break;
                    case DeviceChangeType.Removed:
                        Console.WriteLine($"   设备移除：{e.DeviceInfo.PortName}");
                        break;
                    case DeviceChangeType.StatusChanged:
                        Console.WriteLine($"   设备状态变化：{e.DeviceInfo.PortName}");
                        break;
                }
            };

            // 2. 高性能设置
            Console.WriteLine("\n2. 使用高性能设置：");
            var highSpeedSettings = SerialPortExtensions.CreateHighSpeedSettings();
            highSpeedSettings.ReceiveBufferSize = 16384;  // 16KB
            highSpeedSettings.SendBufferSize = 8192;      // 8KB
            highSpeedSettings.HeartbeatInterval = 10000;  // 10秒心跳
            
            Console.WriteLine($"   波特率：{highSpeedSettings.BaudRate}");
            Console.WriteLine($"   接收缓冲区：{highSpeedSettings.ReceiveBufferSize} 字节");
            Console.WriteLine($"   发送缓冲区：{highSpeedSettings.SendBufferSize} 字节");

            // 3. 批量数据发送
            Console.WriteLine("\n3. 批量数据发送示例：");
            var commands = new[] { "CMD1", "CMD2", "CMD3", "CMD4", "CMD5" };
            
            // 模拟批量发送（需要实际连接的串口）
            Console.WriteLine("   准备发送命令：");
            foreach (var cmd in commands)
            {
                Console.WriteLine($"   - {cmd}");
            }

            // 4. 数据格式转换
            Console.WriteLine("\n4. 数据格式转换示例：");
            var testData = new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F };
            var hexString = testData.ToHexString();
            var backToBytes = SerialPortExtensions.HexStringToBytes(hexString);
            
            Console.WriteLine($"   原始数据：{string.Join(", ", testData.Select(b => $"0x{b:X2}"))}");
            Console.WriteLine($"   十六进制字符串：{hexString}");
            Console.WriteLine($"   转换回字节：{string.Join(", ", backToBytes.Select(b => $"0x{b:X2}"))}");

            // 5. 校验和计算
            Console.WriteLine("\n5. 校验和计算示例：");
            var dataWithChecksum = new byte[] { 0x01, 0x02, 0x03, 0x04 };
            var checksum = dataWithChecksum.CalculateChecksum(ChecksumType.Sum);
            
            Console.WriteLine($"   数据：{dataWithChecksum.ToHexString()}");
            Console.WriteLine($"   校验和：0x{checksum:X2}");

            Console.WriteLine("\n高级功能演示完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"发生错误：{ex.Message}");
        }
        finally
        {
            serialPortService.Dispose();
        }
    }

    /// <summary>
    /// 主入口点
    /// </summary>
    public static async Task Main(string[] args)
    {
        Console.WriteLine("Liam.SerialPort 功能演示");
        Console.WriteLine("========================\n");

        try
        {
            // 基础使用示例
            await BasicUsageExample();

            // 依赖注入示例
            await DependencyInjectionExample();

            // 高级功能示例
            await AdvancedFeaturesExample();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"程序执行出错：{ex.Message}");
        }

        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}
