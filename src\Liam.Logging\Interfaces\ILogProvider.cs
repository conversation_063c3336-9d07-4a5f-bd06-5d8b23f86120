using Liam.Logging.Models;

namespace Liam.Logging.Interfaces;

/// <summary>
/// 日志提供程序接口，定义日志输出目标
/// </summary>
public interface ILogProvider : IDisposable
{
    /// <summary>
    /// 提供程序名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 是否启用
    /// </summary>
    bool IsEnabled { get; set; }

    /// <summary>
    /// 是否支持异步写入
    /// </summary>
    bool SupportsAsync { get; }

    /// <summary>
    /// 初始化提供程序
    /// </summary>
    /// <param name="configuration">配置信息</param>
    /// <returns>异步任务</returns>
    Task InitializeAsync(LogProviderConfiguration configuration);

    /// <summary>
    /// 写入日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    void WriteLog(LogEvent logEvent);

    /// <summary>
    /// 异步写入日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    Task WriteLogAsync(LogEvent logEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量写入日志事件
    /// </summary>
    /// <param name="logEvents">日志事件集合</param>
    void WriteLogs(IEnumerable<LogEvent> logEvents);

    /// <summary>
    /// 异步批量写入日志事件
    /// </summary>
    /// <param name="logEvents">日志事件集合</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    Task WriteLogsAsync(IEnumerable<LogEvent> logEvents, CancellationToken cancellationToken = default);

    /// <summary>
    /// 刷新缓冲区
    /// </summary>
    void Flush();

    /// <summary>
    /// 异步刷新缓冲区
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    Task FlushAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 日志格式化器接口
/// </summary>
public interface ILogFormatter
{
    /// <summary>
    /// 格式化器名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 格式化日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>格式化后的字符串</returns>
    string Format(LogEvent logEvent);

    /// <summary>
    /// 格式化日志事件为字节数组
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>格式化后的字节数组</returns>
    byte[] FormatToBytes(LogEvent logEvent);
}

/// <summary>
/// 日志过滤器接口
/// </summary>
public interface ILogFilter
{
    /// <summary>
    /// 过滤器名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 判断是否应该记录日志
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>是否应该记录</returns>
    bool ShouldLog(LogEvent logEvent);
}

/// <summary>
/// 日志作用域提供程序接口
/// </summary>
public interface ILogScopeProvider
{
    /// <summary>
    /// 开始作用域
    /// </summary>
    /// <typeparam name="TState">状态类型</typeparam>
    /// <param name="state">状态对象</param>
    /// <returns>作用域释放器</returns>
    IDisposable Push<TState>(TState state) where TState : notnull;

    /// <summary>
    /// 获取当前作用域信息
    /// </summary>
    /// <returns>作用域信息字典</returns>
    Dictionary<string, object?> GetCurrentScopes();
}

/// <summary>
/// 日志工厂接口
/// </summary>
public interface ILiamLoggerFactory : IDisposable
{
    /// <summary>
    /// 创建日志记录器
    /// </summary>
    /// <param name="categoryName">类别名称</param>
    /// <returns>日志记录器</returns>
    ILiamLogger CreateLogger(string categoryName);

    /// <summary>
    /// 创建泛型日志记录器
    /// </summary>
    /// <typeparam name="T">类型</typeparam>
    /// <returns>日志记录器</returns>
    ILiamLogger<T> CreateLogger<T>();

    /// <summary>
    /// 添加日志提供程序
    /// </summary>
    /// <param name="provider">日志提供程序</param>
    void AddProvider(ILogProvider provider);

    /// <summary>
    /// 移除日志提供程序
    /// </summary>
    /// <param name="provider">日志提供程序</param>
    void RemoveProvider(ILogProvider provider);

    /// <summary>
    /// 获取所有日志提供程序
    /// </summary>
    /// <returns>日志提供程序集合</returns>
    IEnumerable<ILogProvider> GetProviders();

    /// <summary>
    /// 刷新所有提供程序
    /// </summary>
    void Flush();

    /// <summary>
    /// 异步刷新所有提供程序
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    Task FlushAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 泛型日志记录器接口
/// </summary>
/// <typeparam name="T">类型</typeparam>
public interface ILiamLogger<T> : ILiamLogger
{
}

/// <summary>
/// 日志性能监控接口
/// </summary>
public interface ILogPerformanceMonitor
{
    /// <summary>
    /// 记录日志写入性能
    /// </summary>
    /// <param name="providerName">提供程序名称</param>
    /// <param name="duration">耗时</param>
    /// <param name="logCount">日志数量</param>
    void RecordWritePerformance(string providerName, TimeSpan duration, int logCount);

    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    /// <returns>性能统计信息</returns>
    Dictionary<string, object> GetPerformanceStats();
}
