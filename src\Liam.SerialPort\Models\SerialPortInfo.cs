namespace Liam.SerialPort.Models;

/// <summary>
/// 串口设备信息
/// </summary>
public class SerialPortInfo
{
    /// <summary>
    /// 串口名称（如 COM1, /dev/ttyUSB0）
    /// </summary>
    public string PortName { get; set; } = string.Empty;

    /// <summary>
    /// 设备描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 制造商
    /// </summary>
    public string Manufacturer { get; set; } = string.Empty;

    /// <summary>
    /// 产品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 供应商ID
    /// </summary>
    public string VendorId { get; set; } = string.Empty;

    /// <summary>
    /// 序列号
    /// </summary>
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 设备类型
    /// </summary>
    public string DeviceType { get; set; } = string.Empty;

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool IsAvailable { get; set; } = true;

    /// <summary>
    /// 是否正在使用
    /// </summary>
    public bool IsInUse { get; set; } = false;

    /// <summary>
    /// 设备路径（Linux/macOS）
    /// </summary>
    public string DevicePath { get; set; } = string.Empty;

    /// <summary>
    /// 友好名称
    /// </summary>
    public string FriendlyName { get; set; } = string.Empty;

    /// <summary>
    /// 设备实例ID（Windows）
    /// </summary>
    public string DeviceInstanceId { get; set; } = string.Empty;

    /// <summary>
    /// 硬件ID列表
    /// </summary>
    public List<string> HardwareIds { get; set; } = new();

    /// <summary>
    /// 兼容ID列表
    /// </summary>
    public List<string> CompatibleIds { get; set; } = new();

    /// <summary>
    /// 设备属性字典
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();

    /// <summary>
    /// 发现时间
    /// </summary>
    public DateTime DiscoveredAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后检查时间
    /// </summary>
    public DateTime LastChecked { get; set; } = DateTime.Now;

    /// <summary>
    /// 获取显示名称
    /// </summary>
    public string DisplayName => !string.IsNullOrEmpty(FriendlyName) ? FriendlyName : 
                                !string.IsNullOrEmpty(Description) ? $"{PortName} - {Description}" : PortName;

    /// <summary>
    /// 获取唯一标识符
    /// </summary>
    public string UniqueId => !string.IsNullOrEmpty(DeviceInstanceId) ? DeviceInstanceId :
                             !string.IsNullOrEmpty(SerialNumber) ? SerialNumber :
                             !string.IsNullOrEmpty(DevicePath) ? DevicePath : PortName;

    /// <summary>
    /// 检查是否为USB串口设备
    /// </summary>
    public bool IsUsbDevice => !string.IsNullOrEmpty(VendorId) && !string.IsNullOrEmpty(ProductId);

    /// <summary>
    /// 检查是否为虚拟串口
    /// </summary>
    public bool IsVirtualPort => Description.Contains("Virtual", StringComparison.OrdinalIgnoreCase) ||
                                DeviceType.Contains("Virtual", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// 重写ToString方法
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString() => DisplayName;

    /// <summary>
    /// 重写Equals方法
    /// </summary>
    /// <param name="obj">比较对象</param>
    /// <returns>是否相等</returns>
    public override bool Equals(object? obj)
    {
        if (obj is not SerialPortInfo other) return false;
        return UniqueId.Equals(other.UniqueId, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 重写GetHashCode方法
    /// </summary>
    /// <returns>哈希码</returns>
    public override int GetHashCode() => UniqueId.GetHashCode(StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// 克隆对象
    /// </summary>
    /// <returns>克隆的对象</returns>
    public SerialPortInfo Clone()
    {
        return new SerialPortInfo
        {
            PortName = PortName,
            Description = Description,
            Manufacturer = Manufacturer,
            ProductId = ProductId,
            VendorId = VendorId,
            SerialNumber = SerialNumber,
            DeviceType = DeviceType,
            IsAvailable = IsAvailable,
            IsInUse = IsInUse,
            DevicePath = DevicePath,
            FriendlyName = FriendlyName,
            DeviceInstanceId = DeviceInstanceId,
            HardwareIds = new List<string>(HardwareIds),
            CompatibleIds = new List<string>(CompatibleIds),
            Properties = new Dictionary<string, object>(Properties),
            DiscoveredAt = DiscoveredAt,
            LastChecked = LastChecked
        };
    }
}
