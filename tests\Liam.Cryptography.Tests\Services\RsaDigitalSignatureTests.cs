using Liam.Cryptography.Services;
using Liam.Cryptography.Exceptions;
using Liam.Cryptography.Tests.Fixtures;
using Liam.Cryptography.Tests.TestHelpers;

namespace Liam.Cryptography.Tests.Services;

/// <summary>
/// RSA数字签名服务测试
/// </summary>
[Collection("Crypto Tests")]
public class RsaDigitalSignatureTests
{
    private readonly CryptoTestFixture _fixture;
    private readonly RsaDigitalSignature _signatureService;

    public RsaDigitalSignatureTests(CryptoTestFixture fixture)
    {
        _fixture = fixture;
        _signatureService = _fixture.SignatureService;
    }

    #region 数字签名生成测试

    [Fact]
    public void Sign_ValidInput_ShouldReturnValidSignature()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var signature = _signatureService.Sign(data, keyPair.PrivateKey!);

        // Assert
        signature.Should().NotBeNull();
        signature.Length.Should().BeGreater<PERSON>han(0);
        Convert.ToBase64String(signature).Should().MatchRegex("^[A-Za-z0-9+/]*={0,2}$"); // Base64格式
    }

    [Theory]
    [InlineData("")]
    [InlineData("Hello World")]
    [InlineData("你好世界")]
    [InlineData("!@#$%^&*()_+-=[]{}|;':\",./<>?`~")]
    [InlineData("🌟🚀💻🔐🛡️")]
    public void Sign_DifferentInputs_ShouldReturnConsistentSignatures(string data)
    {
        // Arrange
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var signature1 = _signatureService.Sign(data, keyPair.PrivateKey!);
        var signature2 = _signatureService.Sign(data, keyPair.PrivateKey!);

        // Assert
        signature1.Should().Equal(signature2); // 相同数据和密钥应该产生相同签名
    }

    [Fact]
    public void Sign_DifferentData_ShouldReturnDifferentSignatures()
    {
        // Arrange
        var data1 = "Hello World";
        var data2 = "Hello world";
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var signature1 = _signatureService.Sign(data1, keyPair.PrivateKey!);
        var signature2 = _signatureService.Sign(data2, keyPair.PrivateKey!);

        // Assert
        signature1.Should().NotEqual(signature2);
    }

    [Fact]
    public void Sign_DifferentKeys_ShouldReturnDifferentSignatures()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair1 = _fixture.TestRsaKeyPair;
        var keyPair2 = _fixture.GetTestRsaKeyPair(2048);

        // Act
        var signature1 = _signatureService.Sign(data, keyPair1.PrivateKey!);
        var signature2 = _signatureService.Sign(data, keyPair2.PrivateKey!);

        // Assert
        signature1.Should().NotEqual(signature2);
    }

    [Fact]
    public void Sign_LongData_ShouldWorkCorrectly()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.LongText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var signature = _signatureService.Sign(data, keyPair.PrivateKey!);

        // Assert
        signature.Should().NotBeNull();
        signature.Length.Should().BeGreaterThan(0);
        Convert.ToBase64String(signature).Should().MatchRegex("^[A-Za-z0-9+/]*={0,2}$");
    }

    #endregion

    #region 数字签名验证测试

    [Fact]
    public void Verify_ValidSignature_ShouldReturnTrue()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;
        var signature = _signatureService.Sign(data, keyPair.PrivateKey!);

        // Act
        var isValid = _signatureService.Verify(data, signature, keyPair.PublicKey!);

        // Assert
        isValid.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData("Hello World")]
    [InlineData("你好世界")]
    [InlineData("!@#$%^&*()_+-=[]{}|;':\",./<>?`~")]
    [InlineData("🌟🚀💻🔐🛡️")]
    public void SignAndVerify_DifferentInputs_ShouldWorkCorrectly(string data)
    {
        // Arrange
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var signature = _signatureService.Sign(data, keyPair.PrivateKey!);
        var isValid = _signatureService.Verify(data, signature, keyPair.PublicKey!);

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void Verify_TamperedData_ShouldReturnFalse()
    {
        // Arrange
        var originalData = "Hello World";
        var tamperedData = "Hello world"; // 数据被篡改
        var keyPair = _fixture.TestRsaKeyPair;
        var signature = _signatureService.Sign(originalData, keyPair.PrivateKey!);

        // Act
        var isValid = _signatureService.Verify(tamperedData, signature, keyPair.PublicKey!);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void Verify_TamperedSignature_ShouldReturnFalse()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;
        var signature = _signatureService.Sign(data, keyPair.PrivateKey!);

        // 简单地修改签名的最后一个字节
        var tamperedSignature = new byte[signature.Length];
        Array.Copy(signature, tamperedSignature, signature.Length);
        tamperedSignature[tamperedSignature.Length - 1] = (byte)(tamperedSignature[tamperedSignature.Length - 1] ^ 0xFF);

        // Act
        var isValid = _signatureService.Verify(data, tamperedSignature, keyPair.PublicKey!);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void Verify_WrongPublicKey_ShouldReturnFalse()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair1 = _fixture.TestRsaKeyPair;
        var keyPair2 = _fixture.GetTestRsaKeyPair(2048);
        var signature = _signatureService.Sign(data, keyPair1.PrivateKey!);

        // Act
        var isValid = _signatureService.Verify(data, signature, keyPair2.PublicKey!);

        // Assert
        isValid.Should().BeFalse();
    }

    #endregion

    #region 异步操作测试

    [Fact]
    public async Task SignAsync_ValidInput_ShouldReturnValidSignature()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var signature = await _signatureService.SignAsync(data, keyPair.PrivateKey!);

        // Assert
        signature.Should().NotBeNull();
        signature.Length.Should().BeGreaterThan(0);
        Convert.ToBase64String(signature).Should().MatchRegex("^[A-Za-z0-9+/]*={0,2}$");
    }

    [Fact]
    public async Task VerifyAsync_ValidSignature_ShouldReturnTrue()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;
        var signature = await _signatureService.SignAsync(data, keyPair.PrivateKey!);

        // Act
        var isValid = await _signatureService.VerifyAsync(data, signature, keyPair.PublicKey!);

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public async Task SignVerifyAsync_WithCancellation_ShouldRespectCancellationToken()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;
        using var cts = new CancellationTokenSource();

        // Act & Assert
        var signature = await _signatureService.SignAsync(data, keyPair.PrivateKey, cts.Token);
        var isValid = await _signatureService.VerifyAsync(data, signature, keyPair.PublicKey, cts.Token);

        isValid.Should().BeTrue();
    }

    [Fact]
    public async Task SignAsync_SyncAndAsync_ShouldReturnSameResult()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var syncSignature = _signatureService.Sign(data, keyPair.PrivateKey!);
        var asyncSignature = await _signatureService.SignAsync(data, keyPair.PrivateKey!);

        // Assert
        syncSignature.Should().Equal(asyncSignature);
    }

    #endregion

    #region 异常处理测试

    [Fact]
    public void Sign_NullData_ShouldThrowArgumentException()
    {
        // Arrange
        var keyPair = _fixture.TestRsaKeyPair;

        // Act & Assert
        var action = () => _signatureService.Sign((string)null!, keyPair.PrivateKey!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Sign_NullPrivateKey_ShouldThrowArgumentException()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;

        // Act & Assert
        var action = () => _signatureService.Sign(data, null!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Sign_InvalidPrivateKey_ShouldThrowCryptographyException()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var invalidPrivateKey = "invalid-private-key";

        // Act & Assert
        var action = () => _signatureService.Sign(data, invalidPrivateKey);
        action.Should().Throw<CryptographyException>();
    }

    [Fact]
    public void Verify_NullData_ShouldThrowArgumentException()
    {
        // Arrange
        var keyPair = _fixture.TestRsaKeyPair;
        var testData = "test";
        var signature = _signatureService.Sign((string)testData, keyPair.PrivateKey!);

        // Act & Assert
        var action = () => _signatureService.Verify((string)null!, signature, keyPair.PublicKey!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Verify_NullSignature_ShouldThrowArgumentNullException()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act & Assert
        var action = () => _signatureService.Verify(data, null!, keyPair.PublicKey!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Verify_NullPublicKey_ShouldThrowArgumentException()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;
        var signature = _signatureService.Sign(data, keyPair.PrivateKey!);

        // Act & Assert
        var action = () => _signatureService.Verify(data, signature, null!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Verify_InvalidPublicKey_ShouldReturnFalse()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;
        var signature = _signatureService.Sign(data, keyPair.PrivateKey!);
        var invalidPublicKey = "invalid-public-key";

        // Act
        var result = _signatureService.Verify(data, signature, invalidPublicKey);

        // Assert - 无效公钥应该返回false而不是抛出异常
        result.Should().BeFalse();
    }

    [Fact]
    public void Verify_InvalidSignatureFormat_ShouldReturnFalse()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;
        var invalidSignatureBytes = new byte[] { 1, 2, 3, 4, 5 }; // 无效的签名字节

        // Act
        var isValid = _signatureService.Verify(data, invalidSignatureBytes, keyPair.PublicKey!);

        // Assert
        isValid.Should().BeFalse();
    }

    #endregion

    #region 边界条件测试

    [Fact]
    public void SignVerify_EmptyString_ShouldWorkCorrectly()
    {
        // Arrange
        var data = string.Empty;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var signature = _signatureService.Sign(data, keyPair.PrivateKey!);
        var isValid = _signatureService.Verify(data, signature, keyPair.PublicKey!);

        // Assert
        isValid.Should().BeTrue();
    }

    [Theory]
    [InlineData(1024)]
    [InlineData(2048)]
    [InlineData(3072)]
    public void SignVerify_DifferentKeySizes_ShouldWorkCorrectly(int keySize)
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.GetTestRsaKeyPair(keySize);

        // Act
        var signature = _signatureService.Sign(data, keyPair.PrivateKey!);
        var isValid = _signatureService.Verify(data, signature, keyPair.PublicKey!);

        // Assert
        isValid.Should().BeTrue();
    }

    #endregion

    #region 性能测试

    [Fact]
    public void Sign_LargeData_ShouldCompleteInReasonableTime()
    {
        // Arrange
        var largeData = new string('A', 100000); // 100KB数据
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var signature = _signatureService.Sign(largeData, keyPair.PrivateKey!);
        stopwatch.Stop();

        // Assert
        signature.Should().NotBeNullOrEmpty();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // 应该在5秒内完成
    }

    [Fact]
    public void Verify_LargeData_ShouldCompleteInReasonableTime()
    {
        // Arrange
        var largeData = new string('A', 100000); // 100KB数据
        var keyPair = _fixture.TestRsaKeyPair;
        var signature = _signatureService.Sign(largeData, keyPair.PrivateKey!);

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var isValid = _signatureService.Verify(largeData, signature, keyPair.PublicKey!);
        stopwatch.Stop();

        // Assert
        isValid.Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // 应该在5秒内完成
    }

    #endregion
}
