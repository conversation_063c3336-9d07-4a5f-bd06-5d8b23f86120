using Liam.Cryptography.Extensions;
using Liam.Cryptography.Services;
using Liam.Cryptography.Tests.Fixtures;
using Liam.Cryptography.Tests.TestHelpers;

namespace Liam.Cryptography.Tests.Extensions;

/// <summary>
/// 字符串加密扩展方法测�?/// </summary>
[Collection("Crypto Tests")]
public class StringCryptoExtensionsTests
{
    private readonly CryptoTestFixture _fixture;

    public StringCryptoExtensionsTests(CryptoTestFixture fixture)
    {
        _fixture = fixture;
    }

    #region 哈希扩展方法测试

    [Fact]
    public void ToSha256Hash_ValidString_ShouldReturnCorrectHash()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.SimpleText;
        var expectedHash = "a591a6d40bf420404a011733cfb7b190d62c65bf0bcda32b57b277d9ad9f146e";

        // Act
        var hash = input.ToSha256Hash();

        // Assert
        hash.Should().Be(expectedHash);
    }

    [Theory]
    [InlineData("")]
    [InlineData("Hello World")]
    [InlineData("你好世界")]
    [InlineData("!@#$%^&*()_+-=[]{}|;':\",./<>?`~")]
    [InlineData("🌟🚀💻🔐🛡️")]
    public void ToSha256Hash_DifferentInputs_ShouldReturnConsistentHashes(string input)
    {
        // Act
        var hash1 = input.ToSha256Hash();
        var hash2 = input.ToSha256Hash();

        // Assert
        hash1.Should().Be(hash2);
        hash1.Length.Should().Be(64);
        hash1.Should().MatchRegex("^[a-f0-9]{64}$");
    }

    [Fact]
    public void ToSha256Hash_NullString_ShouldThrowArgumentException()
    {
        // Arrange
        string? nullString = null;

        // Act & Assert
        var action = () => nullString!.ToSha256Hash();
        action.Should().Throw<ArgumentException>();
    }

    #endregion

    #region AES加密扩展方法测试

    [Fact]
    public void EncryptAes_ValidInput_ShouldReturnEncryptedData()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var key = _fixture.TestAesKey;

        // Act
        var encrypted = plainText.EncryptAes(key);

        // Assert
        encrypted.Should().NotBeNull();
        encrypted.Length.Should().BeGreaterThan(0);
        var encryptedBytes = Convert.FromBase64String(encrypted);
        encryptedBytes.Should().NotEqual(System.Text.Encoding.UTF8.GetBytes(plainText));
    }

    [Fact]
    public void DecryptAes_ValidInput_ShouldReturnOriginalText()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var key = _fixture.TestAesKey;

        // Act
        var encrypted = plainText.EncryptAes(key);
        var decrypted = encrypted.DecryptAes(key);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Theory]
    [InlineData("")]
    [InlineData("Hello World")]
    [InlineData("你好世界")]
    [InlineData("!@#$%^&*()_+-=[]{}|;':\",./<>?`~")]
    [InlineData("🌟🚀💻🔐🛡️")]
    public void EncryptDecryptAes_DifferentTexts_ShouldPreserveOriginal(string plainText)
    {
        // Arrange
        var key = _fixture.TestAesKey;

        // Act
        var encrypted = plainText.EncryptAes(key);
        var decrypted = encrypted.DecryptAes(key);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Fact]
    public void EncryptAes_WithIV_ShouldWorkCorrectly()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act
        var encrypted = plainText.EncryptAes(key, iv);
        var decrypted = encrypted.DecryptAes(key, iv);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Fact]
    public void EncryptAes_NullString_ShouldThrowArgumentException()
    {
        // Arrange
        string? nullString = null;
        var key = _fixture.TestAesKey;

        // Act & Assert
        var action = () => nullString!.EncryptAes(key);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void EncryptAes_NullKey_ShouldThrowArgumentNullException()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;

        // Act & Assert
        var action = () => plainText.EncryptAes(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    #endregion

    #region RSA加密扩展方法测试

    [Fact]
    public void EncryptRsa_ValidInput_ShouldReturnEncryptedData()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = plainText.EncryptRsa(keyPair.PublicKey!);

        // Assert
        encrypted.Should().NotBeNull();
        encrypted.Length.Should().BeGreaterThan(0);
        var encryptedBytes = Convert.FromBase64String(encrypted);
        encryptedBytes.Should().NotEqual(System.Text.Encoding.UTF8.GetBytes(plainText));
    }

    [Fact]
    public void DecryptRsa_ValidInput_ShouldReturnOriginalText()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = plainText.EncryptRsa(keyPair.PublicKey!);
        var decrypted = encrypted.DecryptRsa(keyPair.PrivateKey!);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Theory]
    [InlineData("")]
    [InlineData("Hello World")]
    [InlineData("你好世界")]
    [InlineData("A")]
    [InlineData("123456")]
    public void EncryptDecryptRsa_DifferentTexts_ShouldPreserveOriginal(string plainText)
    {
        // Arrange
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = plainText.EncryptRsa(keyPair.PublicKey!);
        var decrypted = encrypted.DecryptRsa(keyPair.PrivateKey!);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Fact]
    public void EncryptRsa_NullString_ShouldThrowArgumentException()
    {
        // Arrange
        string? nullString = null;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act & Assert
        var action = () => nullString!.EncryptRsa(keyPair.PublicKey!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void EncryptRsa_NullPublicKey_ShouldThrowArgumentException()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;

        // Act & Assert
        var action = () => plainText.EncryptRsa(null!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void EncryptRsa_TextTooLong_ShouldThrowException()
    {
        // Arrange
        var longText = new string('A', 1000); // 超过RSA加密限制的长文本
        var keyPair = _fixture.TestRsaKeyPair;

        // Act & Assert
        var action = () => longText.EncryptRsa(keyPair.PublicKey!);
        action.Should().Throw<Exception>();
    }

    #endregion

    #region RSA数字签名扩展方法测试

    [Fact]
    public void SignRsa_ValidInput_ShouldReturnValidSignature()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var signature = data.SignRsa(keyPair.PrivateKey!);

        // Assert
        signature.Should().NotBeNullOrEmpty();
        signature.Should().MatchRegex("^[A-Za-z0-9+/]*={0,2}$"); // Base64格式
    }

    [Fact]
    public void VerifyRsaSignature_ValidSignature_ShouldReturnTrue()
    {
        // Arrange
        var data = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;
        var signature = data.SignRsa(keyPair.PrivateKey!);

        // Act
        var isValid = data.VerifyRsaSignature(signature, keyPair.PublicKey!);

        // Assert
        isValid.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData("Hello World")]
    [InlineData("你好世界")]
    [InlineData("!@#$%^&*()_+-=[]{}|;':\",./<>?`~")]
    [InlineData("🌟🚀💻🔐🛡️")]
    public void SignVerifyRsa_DifferentInputs_ShouldWorkCorrectly(string data)
    {
        // Arrange
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var signature = data.SignRsa(keyPair.PrivateKey!);
        var isValid = data.VerifyRsaSignature(signature, keyPair.PublicKey!);

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void VerifyRsaSignature_TamperedData_ShouldReturnFalse()
    {
        // Arrange
        var originalData = "Hello World";
        var tamperedData = "Hello world";
        var keyPair = _fixture.TestRsaKeyPair;
        var signature = originalData.SignRsa(keyPair.PrivateKey!);

        // Act
        var isValid = tamperedData.VerifyRsaSignature(signature, keyPair.PublicKey!);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void SignRsa_NullString_ShouldThrowArgumentException()
    {
        // Arrange
        string? nullString = null;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act & Assert
        var action = () => nullString!.SignRsa(keyPair.PrivateKey!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void VerifyRsaSignature_NullString_ShouldThrowArgumentException()
    {
        // Arrange
        string? nullString = null;
        var keyPair = _fixture.TestRsaKeyPair;
        var signature = "test-signature";

        // Act & Assert
        var action = () => nullString!.VerifyRsaSignature(signature, keyPair.PublicKey!);
        action.Should().Throw<ArgumentException>();
    }

    #endregion

    #region 集成测试

    [Fact]
    public void CryptoExtensions_CompleteWorkflow_ShouldWorkCorrectly()
    {
        // Arrange
        var originalText = "This is a test message for complete crypto workflow.";
        var aesService = new AesSymmetricCrypto();
        var rsaService = new RsaAsymmetricCrypto();
        
        var aesKey = aesService.GenerateKey(256);
        var rsaKeyPair = rsaService.GenerateKeyPair(2048);

        // Act - 完整的加密工作流�?        // 1. 计算原文哈希
        var originalHash = originalText.ToSha256Hash();

        // 2. AES加密原文
        var aesEncrypted = originalText.EncryptAes(aesKey);

        // 3. RSA加密AES密钥
        var keyString = Convert.ToBase64String(aesKey);
        var rsaEncryptedKey = keyString.EncryptRsa(rsaKeyPair.PublicKey!);

        // 4. 对原文进行数字签名
        var signature = originalText.SignRsa(rsaKeyPair.PrivateKey!);

        // 5. 解密流程
        var decryptedKeyString = rsaEncryptedKey.DecryptRsa(rsaKeyPair.PrivateKey!);
        var decryptedAesKey = Convert.FromBase64String(decryptedKeyString);
        var decryptedText = aesEncrypted.DecryptAes(decryptedAesKey);

        // 6. 验证
        var decryptedHash = decryptedText.ToSha256Hash();
        var signatureValid = decryptedText.VerifyRsaSignature(signature, rsaKeyPair.PublicKey!);

        // Assert
        decryptedText.Should().Be(originalText);
        decryptedHash.Should().Be(originalHash);
        signatureValid.Should().BeTrue();
    }

    #endregion
}
