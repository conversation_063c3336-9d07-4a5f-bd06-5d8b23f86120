using Liam.Cryptography.Extensions;
using Liam.Cryptography.Tests.Fixtures;
using Liam.Cryptography.Tests.TestHelpers;

namespace Liam.Cryptography.Tests.Extensions;

/// <summary>
/// 字节数组加密扩展方法测试
/// </summary>
[Collection("Crypto Tests")]
public class ByteArrayCryptoExtensionsTests
{
    private readonly CryptoTestFixture _fixture;

    public ByteArrayCryptoExtensionsTests(CryptoTestFixture fixture)
    {
        _fixture = fixture;
    }

    #region 哈希扩展方法测试

    [Fact]
    public void ToSha256Hash_ValidByteArray_ShouldReturnCorrectHash()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.SimpleBytes;
        var expectedHash = "a591a6d40bf420404a011733cfb7b190d62c65bf0bcda32b57b277d9ad9f146e";

        // Act
        var hash = input.ToSha256Hash();

        // Assert
        hash.Should().Be(expectedHash);
    }

    [Fact]
    public void ToSha256Hash_EmptyByteArray_ShouldReturnEmptyStringHash()
    {
        // Arrange
        var input = Array.Empty<byte>();
        var expectedHash = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";

        // Act
        var hash = input.ToSha256Hash();

        // Assert
        hash.Should().Be(expectedHash);
    }

    [Fact]
    public void ToSha256Hash_DifferentByteArrays_ShouldReturnConsistentHashes()
    {
        // Arrange
        var input1 = CryptoTestHelper.TestData.SimpleBytes;
        var input2 = CryptoTestHelper.TestData.ChineseBytes;
        var input3 = CryptoTestHelper.TestData.LongBytes;

        // Act
        var hash1a = input1.ToSha256Hash();
        var hash1b = input1.ToSha256Hash();
        var hash2 = input2.ToSha256Hash();
        var hash3 = input3.ToSha256Hash();

        // Assert
        hash1a.Should().Be(hash1b); // 相同输入应该产生相同哈希
        hash1a.Should().NotBe(hash2); // 不同输入应该产生不同哈希
        hash1a.Should().NotBe(hash3);
        hash2.Should().NotBe(hash3);
        
        // 所有哈希都应该�?4个十六进制字�?        hash1a.Length.Should().Be(64);
        hash2.Length.Should().Be(64);
        hash3.Length.Should().Be(64);
        
        hash1a.Should().MatchRegex("^[a-f0-9]{64}$");
        hash2.Should().MatchRegex("^[a-f0-9]{64}$");
        hash3.Should().MatchRegex("^[a-f0-9]{64}$");
    }

    [Fact]
    public void ToSha256Hash_NullByteArray_ShouldThrowArgumentNullException()
    {
        // Arrange
        byte[]? nullArray = null;

        // Act & Assert
        var action = () => nullArray!.ToSha256Hash();
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void ToSha256Hash_LargeByteArray_ShouldWorkCorrectly()
    {
        // Arrange
        var largeArray = new byte[1024 * 1024]; // 1MB数据
        new Random().NextBytes(largeArray);

        // Act
        var hash = largeArray.ToSha256Hash();

        // Assert
        hash.Should().NotBeNullOrEmpty();
        hash.Length.Should().Be(64);
        hash.Should().MatchRegex("^[a-f0-9]{64}$");
    }

    #endregion

    #region AES加密扩展方法测试

    [Fact]
    public void EncryptAes_ValidByteArray_ShouldReturnEncryptedData()
    {
        // Arrange
        var plainData = CryptoTestHelper.TestData.SimpleBytes;
        var key = _fixture.TestAesKey;

        // Act
        var encrypted = plainData.EncryptAes(key);

        // Assert
        encrypted.Should().NotBeNull();
        encrypted.Length.Should().BeGreaterThan(0);
        encrypted.Should().NotEqual(plainData);
    }

    [Fact]
    public void DecryptAes_ValidByteArray_ShouldReturnOriginalData()
    {
        // Arrange
        var plainData = CryptoTestHelper.TestData.SimpleBytes;
        var key = _fixture.TestAesKey;

        // Act
        var encrypted = plainData.EncryptAes(key);
        var decrypted = encrypted.DecryptAes(key);

        // Assert
        decrypted.Should().BeEquivalentTo(plainData);
    }

    [Theory]
    [MemberData(nameof(GetTestByteArrays))]
    public void EncryptDecryptAes_DifferentByteArrays_ShouldPreserveOriginal(byte[] plainData)
    {
        // Arrange
        var key = _fixture.TestAesKey;

        // Act
        var encrypted = plainData.EncryptAes(key);
        var decrypted = encrypted.DecryptAes(key);

        // Assert
        decrypted.Should().BeEquivalentTo(plainData);
    }

    public static IEnumerable<object[]> GetTestByteArrays()
    {
        yield return new object[] { Array.Empty<byte>() };
        yield return new object[] { CryptoTestHelper.TestData.SimpleBytes };
        yield return new object[] { CryptoTestHelper.TestData.ChineseBytes };
        yield return new object[] { CryptoTestHelper.TestData.SpecialBytes };
        yield return new object[] { CryptoTestHelper.TestData.UnicodeBytes };
        yield return new object[] { new byte[] { 0x00, 0xFF, 0x80, 0x7F } }; // 边界值
    }

    [Fact]
    public void EncryptAes_WithIV_ShouldWorkCorrectly()
    {
        // Arrange
        var plainData = CryptoTestHelper.TestData.SimpleBytes;
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act
        var encrypted = plainData.EncryptAes(key, iv);
        var decrypted = encrypted.DecryptAes(key, iv);

        // Assert
        decrypted.Should().BeEquivalentTo(plainData);
    }

    [Fact]
    public void EncryptAes_NullByteArray_ShouldThrowArgumentNullException()
    {
        // Arrange
        byte[]? nullArray = null;
        var key = _fixture.TestAesKey;

        // Act & Assert
        var action = () => nullArray!.EncryptAes(key);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void EncryptAes_NullKey_ShouldThrowArgumentNullException()
    {
        // Arrange
        var plainData = CryptoTestHelper.TestData.SimpleBytes;

        // Act & Assert
        var action = () => plainData.EncryptAes(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void EncryptAes_EmptyByteArray_ShouldWorkCorrectly()
    {
        // Arrange
        var emptyData = Array.Empty<byte>();
        var key = _fixture.TestAesKey;

        // Act
        var encrypted = emptyData.EncryptAes(key);
        var decrypted = encrypted.DecryptAes(key);

        // Assert
        decrypted.Should().BeEquivalentTo(emptyData);
    }

    #endregion

    #region 十六进制转换扩展方法测试

    [Fact]
    public void ToHexString_ValidByteArray_ShouldReturnCorrectHex()
    {
        // Arrange
        var input = new byte[] { 0x00, 0x01, 0x0F, 0x10, 0xFF };
        var expectedHex = "00010f10ff";

        // Act
        var hex = input.ToHexString();

        // Assert
        hex.Should().Be(expectedHex);
    }

    [Fact]
    public void ToHexString_EmptyByteArray_ShouldReturnEmptyString()
    {
        // Arrange
        var input = Array.Empty<byte>();

        // Act
        var hex = input.ToHexString();

        // Assert
        hex.Should().BeEmpty();
    }

    [Fact]
    public void ToHexString_NullByteArray_ShouldThrowArgumentNullException()
    {
        // Arrange
        byte[]? nullArray = null;

        // Act & Assert
        var action = () => nullArray!.ToHexString();
        action.Should().Throw<ArgumentNullException>();
    }

    [Theory]
    [InlineData(new byte[] { 0x00 }, "00")]
    [InlineData(new byte[] { 0xFF }, "ff")]
    [InlineData(new byte[] { 0x0F, 0xF0 }, "0ff0")]
    [InlineData(new byte[] { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0 }, "123456789abcdef0")]
    public void ToHexString_SpecificValues_ShouldReturnCorrectHex(byte[] input, string expectedHex)
    {
        // Act
        var hex = input.ToHexString();

        // Assert
        hex.Should().Be(expectedHex);
    }

    #endregion

    #region Base64转换扩展方法测试

    [Fact]
    public void ToBase64String_ValidByteArray_ShouldReturnCorrectBase64()
    {
        // Arrange
        var input = CryptoTestHelper.TestData.SimpleBytes;
        var expectedBase64 = Convert.ToBase64String(input);

        // Act
        var base64 = input.ToBase64String();

        // Assert
        base64.Should().Be(expectedBase64);
    }

    [Fact]
    public void ToBase64String_EmptyByteArray_ShouldReturnEmptyString()
    {
        // Arrange
        var input = Array.Empty<byte>();

        // Act
        var base64 = input.ToBase64String();

        // Assert
        base64.Should().BeEmpty();
    }

    [Fact]
    public void ToBase64String_NullByteArray_ShouldThrowArgumentNullException()
    {
        // Arrange
        byte[]? nullArray = null;

        // Act & Assert
        var action = () => nullArray!.ToBase64String();
        action.Should().Throw<ArgumentNullException>();
    }

    [Theory]
    [MemberData(nameof(GetTestByteArrays))]
    public void ToBase64String_DifferentByteArrays_ShouldReturnValidBase64(byte[] input)
    {
        // Act
        var base64 = input.ToBase64String();

        // Assert
        if (input.Length == 0)
        {
            base64.Should().BeEmpty();
        }
        else
        {
            base64.Should().NotBeNullOrEmpty();
            base64.Should().MatchRegex("^[A-Za-z0-9+/]*={0,2}$"); // Base64格式验证
            
            // 验证往返转换
            var roundTrip = Convert.FromBase64String(base64);
            roundTrip.Should().Equal(input);
        }
    }

    #endregion

    #region 安全清除扩展方法测试

    [Fact]
    public void SecureClear_ValidByteArray_ShouldClearAllBytes()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05 };
        var originalData = new byte[data.Length];
        Array.Copy(data, originalData, data.Length);

        // Act
        data.SecureClear();

        // Assert
        data.Should().OnlyContain(b => b == 0);
        data.Should().NotBeEquivalentTo(originalData);
    }

    [Fact]
    public void SecureClear_EmptyByteArray_ShouldNotThrow()
    {
        // Arrange
        var data = Array.Empty<byte>();

        // Act & Assert
        var action = () => data.SecureClear();
        action.Should().NotThrow();
    }

    [Fact]
    public void SecureClear_NullByteArray_ShouldThrowArgumentNullException()
    {
        // Arrange
        byte[]? nullArray = null;

        // Act & Assert
        var action = () => nullArray!.SecureClear();
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void SecureClear_LargeByteArray_ShouldClearAllBytes()
    {
        // Arrange
        var data = new byte[1024];
        new Random().NextBytes(data);
        var hasNonZero = data.Any(b => b != 0);
        hasNonZero.Should().BeTrue(); // 确保开始时有非零字节

        // Act
        data.SecureClear();

        // Assert
        data.Should().OnlyContain(b => b == 0);
    }

    #endregion

    #region 集成测试

    [Fact]
    public void ByteArrayExtensions_CompleteWorkflow_ShouldWorkCorrectly()
    {
        // Arrange
        var originalData = CryptoTestHelper.TestData.LongBytes;
        var key = _fixture.TestAesKey;

        // Act - 完整的字节数组处理工作流程
        // 1. 计算原始数据哈希
        var originalHash = originalData.ToSha256Hash();
        
        // 2. 转换为十六进制和Base64
        var hexString = originalData.ToHexString();
        var base64String = originalData.ToBase64String();
        
        // 3. AES加密
        var encrypted = originalData.EncryptAes(key);
        
        // 4. 解密
        var decrypted = encrypted.DecryptAes(key);
        
        // 5. 验证
        var decryptedHash = decrypted.ToSha256Hash();
        var decryptedHex = decrypted.ToHexString();
        var decryptedBase64 = decrypted.ToBase64String();

        // Assert
        decrypted.Should().BeEquivalentTo(originalData);
        decryptedHash.Should().Be(originalHash);
        decryptedHex.Should().Be(hexString);
        decryptedBase64.Should().Be(base64String);
    }

    [Fact]
    public void ByteArrayExtensions_SecurityWorkflow_ShouldWorkCorrectly()
    {
        // Arrange
        var sensitiveData = CryptoTestHelper.GenerateRandomBytes(256);
        var key = _fixture.TestAesKey;
        var originalHash = sensitiveData.ToSha256Hash();

        // Act - 安全处理工作流程
        // 1. 加密敏感数据
        var encrypted = sensitiveData.EncryptAes(key);
        
        // 2. 安全清除原始数据
        sensitiveData.SecureClear();

        // 3. 解密数据
        var decrypted = encrypted.DecryptAes(key);

        // 4. 验证数据完整性
        var decryptedHash = decrypted.ToSha256Hash();

        // Assert
        sensitiveData.Should().OnlyContain(b => b == 0); // 原始数据已被清除
        decryptedHash.Should().Be(originalHash); // 解密数据完整性正确

        // 清理解密数据
        decrypted.SecureClear();
        decrypted.Should().OnlyContain(b => b == 0);
    }

    #endregion
}
