using System.IO.Ports;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Logging;
using Liam.SerialPort.Constants;
using Liam.SerialPort.Events;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;

namespace Liam.SerialPort.Services;

/// <summary>
/// 串口设备发现服务实现
/// </summary>
public class SerialPortDiscovery : ISerialPortDiscovery
{
    private readonly ILogger<SerialPortDiscovery> _logger;
    private readonly Timer? _monitorTimer;
    private readonly object _lock = new();
    private readonly Dictionary<string, SerialPortInfo> _cachedPorts = new();
    
    private bool _isMonitoring;
    private bool _disposed;

    /// <summary>
    /// 设备变化事件
    /// </summary>
    public event EventHandler<DeviceChangedEventArgs>? DeviceChanged;

    /// <summary>
    /// 获取是否正在监控设备变化
    /// </summary>
    public bool IsMonitoring => _isMonitoring;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public SerialPortDiscovery(ILogger<SerialPortDiscovery> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 创建监控定时器
        _monitorTimer = new Timer(MonitorDeviceChanges, null, Timeout.Infinite, Timeout.Infinite);
    }

    /// <summary>
    /// 获取所有可用的串口设备
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>串口设备信息列表</returns>
    public async Task<IEnumerable<SerialPortInfo>> GetAvailablePortsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("开始获取可用串口设备列表");

            var ports = new List<SerialPortInfo>();
            var portNames = System.IO.Ports.SerialPort.GetPortNames();

            foreach (var portName in portNames)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var portInfo = await GetPortInfoAsync(portName, cancellationToken);
                if (portInfo != null)
                {
                    ports.Add(portInfo);
                }
            }

            // 根据平台获取额外的设备信息
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                await EnhancePortInfoForWindows(ports, cancellationToken);
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                await EnhancePortInfoForLinux(ports, cancellationToken);
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                await EnhancePortInfoForMacOS(ports, cancellationToken);
            }

            _logger.LogInformation("发现 {Count} 个可用串口设备", ports.Count);
            return ports.OrderBy(p => p.PortName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可用串口设备列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取指定串口的详细信息
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>串口详细信息，如果不存在则返回null</returns>
    public async Task<SerialPortInfo?> GetPortInfoAsync(string portName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(portName))
            return null;

        try
        {
            var portInfo = new SerialPortInfo
            {
                PortName = portName,
                IsAvailable = await IsPortAvailableAsync(portName, cancellationToken),
                DiscoveredAt = DateTime.Now,
                LastChecked = DateTime.Now
            };

            // 尝试获取更详细的设备信息
            await EnhancePortInfo(portInfo, cancellationToken);

            return portInfo;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取串口 {PortName} 信息时发生错误", portName);
            return null;
        }
    }

    /// <summary>
    /// 检查指定串口是否可用
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>串口是否可用</returns>
    public async Task<bool> IsPortAvailableAsync(string portName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(portName))
            return false;

        try
        {
            // 尝试打开串口来检查可用性
            using var port = new System.IO.Ports.SerialPort(portName);
            
            await Task.Run(() =>
            {
                try
                {
                    port.Open();
                    return true;
                }
                catch
                {
                    return false;
                }
                finally
                {
                    if (port.IsOpen)
                        port.Close();
                }
            }, cancellationToken);

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 开始监控设备变化
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task StartMonitoringAsync(CancellationToken cancellationToken = default)
    {
        if (_isMonitoring)
            return;

        try
        {
            _logger.LogInformation("开始监控串口设备变化");

            // 初始化当前设备列表
            var currentPorts = await GetAvailablePortsAsync(cancellationToken);
            lock (_lock)
            {
                _cachedPorts.Clear();
                foreach (var port in currentPorts)
                {
                    _cachedPorts[port.PortName] = port;
                }
            }

            // 启动监控定时器
            _monitorTimer?.Change(0, SerialPortConstants.Defaults.DeviceMonitorInterval);
            _isMonitoring = true;

            _logger.LogInformation("串口设备监控已启动，监控间隔：{Interval}ms", SerialPortConstants.Defaults.DeviceMonitorInterval);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动串口设备监控时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 停止监控设备变化
    /// </summary>
    public async Task StopMonitoringAsync()
    {
        if (!_isMonitoring)
            return;

        try
        {
            _logger.LogInformation("停止监控串口设备变化");

            // 停止监控定时器
            _monitorTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _isMonitoring = false;

            lock (_lock)
            {
                _cachedPorts.Clear();
            }

            _logger.LogInformation("串口设备监控已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止串口设备监控时发生错误");
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 刷新设备列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task RefreshAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("刷新串口设备列表");

            var currentPorts = await GetAvailablePortsAsync(cancellationToken);
            
            if (_isMonitoring)
            {
                await CheckForDeviceChanges(currentPorts);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新串口设备列表时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 监控设备变化的定时器回调
    /// </summary>
    /// <param name="state">状态对象</param>
    private async void MonitorDeviceChanges(object? state)
    {
        if (!_isMonitoring || _disposed)
            return;

        try
        {
            await RefreshAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "监控设备变化时发生错误");
        }
    }

    /// <summary>
    /// 检查设备变化
    /// </summary>
    /// <param name="currentPorts">当前设备列表</param>
    private async Task CheckForDeviceChanges(IEnumerable<SerialPortInfo> currentPorts)
    {
        var currentPortDict = currentPorts.ToDictionary(p => p.PortName);
        
        lock (_lock)
        {
            // 检查新增的设备
            foreach (var port in currentPortDict.Values)
            {
                if (!_cachedPorts.ContainsKey(port.PortName))
                {
                    _cachedPorts[port.PortName] = port;
                    OnDeviceChanged(new DeviceChangedEventArgs(DeviceChangeType.Inserted, port));
                }
            }

            // 检查移除的设备
            var removedPorts = _cachedPorts.Keys.Except(currentPortDict.Keys).ToList();
            foreach (var portName in removedPorts)
            {
                var removedPort = _cachedPorts[portName];
                _cachedPorts.Remove(portName);
                OnDeviceChanged(new DeviceChangedEventArgs(DeviceChangeType.Removed, removedPort));
            }

            // 检查状态变化的设备
            foreach (var port in currentPortDict.Values)
            {
                if (_cachedPorts.TryGetValue(port.PortName, out var cachedPort))
                {
                    if (cachedPort.IsAvailable != port.IsAvailable || cachedPort.IsInUse != port.IsInUse)
                    {
                        _cachedPorts[port.PortName] = port;
                        OnDeviceChanged(new DeviceChangedEventArgs(DeviceChangeType.StatusChanged, port));
                    }
                }
            }
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 触发设备变化事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnDeviceChanged(DeviceChangedEventArgs e)
    {
        try
        {
            DeviceChanged?.Invoke(this, e);
            
            _logger.LogInformation("设备变化：{ChangeType} - {PortName} ({Description})", 
                e.ChangeType, e.DeviceInfo.PortName, e.DeviceInfo.Description);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "触发设备变化事件时发生错误");
        }
    }

    /// <summary>
    /// 增强端口信息
    /// </summary>
    /// <param name="portInfo">端口信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task EnhancePortInfo(SerialPortInfo portInfo, CancellationToken cancellationToken)
    {
        // 基础信息设置
        portInfo.Description = $"串口设备 {portInfo.PortName}";
        portInfo.FriendlyName = portInfo.PortName;

        await Task.CompletedTask;
    }

    /// <summary>
    /// Windows平台增强端口信息
    /// </summary>
    /// <param name="ports">端口列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task EnhancePortInfoForWindows(List<SerialPortInfo> ports, CancellationToken cancellationToken)
    {
        // Windows平台可以通过WMI或注册表获取更详细的设备信息
        // 这里提供基础实现，可以根据需要扩展
        await Task.CompletedTask;
    }

    /// <summary>
    /// Linux平台增强端口信息
    /// </summary>
    /// <param name="ports">端口列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task EnhancePortInfoForLinux(List<SerialPortInfo> ports, CancellationToken cancellationToken)
    {
        // Linux平台可以通过/sys/class/tty获取设备信息
        await Task.CompletedTask;
    }

    /// <summary>
    /// macOS平台增强端口信息
    /// </summary>
    /// <param name="ports">端口列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task EnhancePortInfoForMacOS(List<SerialPortInfo> ports, CancellationToken cancellationToken)
    {
        // macOS平台可以通过IOKit获取设备信息
        await Task.CompletedTask;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            StopMonitoringAsync().Wait(5000);
            _monitorTimer?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放SerialPortDiscovery资源时发生错误");
        }
        finally
        {
            _disposed = true;
        }
    }
}
