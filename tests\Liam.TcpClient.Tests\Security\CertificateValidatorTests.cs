using System.Net.Security;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Liam.TcpClient.Models;
using Liam.TcpClient.Security;

namespace Liam.TcpClient.Tests.Security;

/// <summary>
/// 证书验证器测试
/// </summary>
public class CertificateValidatorTests
{
    private readonly Mock<ILogger<CertificateValidator>> _mockLogger;

    public CertificateValidatorTests()
    {
        _mockLogger = new Mock<ILogger<CertificateValidator>>();
    }

    [Fact]
    public void Constructor_WithNullConfig_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new CertificateValidator(null!, _mockLogger.Object));
    }

    [Fact]
    public void ValidateRemoteCertificate_WithNullCertificate_ShouldReturnFalse()
    {
        // Arrange
        var config = new SslConfig { ValidationMode = CertificateValidationMode.Strict };
        var validator = new CertificateValidator(config, _mockLogger.Object);

        // Act
        var result = validator.ValidateRemoteCertificate(this, null, null, SslPolicyErrors.None);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void ValidateRemoteCertificate_StrictMode_WithNoErrors_ShouldReturnTrue()
    {
        // Arrange
        var config = new SslConfig { ValidationMode = CertificateValidationMode.Strict };
        var validator = new CertificateValidator(config, _mockLogger.Object);
        var certificate = CreateTestCertificate();

        // Act
        var result = validator.ValidateRemoteCertificate(this, certificate, null, SslPolicyErrors.None);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void ValidateRemoteCertificate_StrictMode_WithErrors_ShouldReturnFalse()
    {
        // Arrange
        var config = new SslConfig { ValidationMode = CertificateValidationMode.Strict };
        var validator = new CertificateValidator(config, _mockLogger.Object);
        var certificate = CreateTestCertificate();

        // Act
        var result = validator.ValidateRemoteCertificate(this, certificate, null, SslPolicyErrors.RemoteCertificateNameMismatch);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void ValidateRemoteCertificate_DevelopmentMode_WithAllowedErrors_ShouldReturnTrue()
    {
        // Arrange
        var config = new SslConfig 
        { 
            ValidationMode = CertificateValidationMode.Development,
            AllowSelfSignedCertificates = true,
            AllowNameMismatch = true
        };
        var validator = new CertificateValidator(config, _mockLogger.Object);
        var certificate = CreateTestCertificate();

        // Act
        var result = validator.ValidateRemoteCertificate(this, certificate, null, 
            SslPolicyErrors.RemoteCertificateNameMismatch | SslPolicyErrors.RemoteCertificateChainErrors);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void ValidateRemoteCertificate_DevelopmentMode_WithDisallowedErrors_ShouldReturnFalse()
    {
        // Arrange
        var config = new SslConfig 
        { 
            ValidationMode = CertificateValidationMode.Development,
            AllowSelfSignedCertificates = false,
            AllowNameMismatch = false
        };
        var validator = new CertificateValidator(config, _mockLogger.Object);
        var certificate = CreateTestCertificate();

        // Act
        var result = validator.ValidateRemoteCertificate(this, certificate, null, SslPolicyErrors.RemoteCertificateNameMismatch);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void ValidateRemoteCertificate_ThumbprintMode_WithMatchingThumbprint_ShouldReturnTrue()
    {
        // Arrange
        var certificate = CreateTestCertificate();
        var thumbprint = GetCertificateThumbprint(certificate);
        
        var config = new SslConfig 
        { 
            ValidationMode = CertificateValidationMode.Thumbprint,
            TrustedCertificateThumbprints = { thumbprint }
        };
        var validator = new CertificateValidator(config, _mockLogger.Object);

        // Act
        var result = validator.ValidateRemoteCertificate(this, certificate, null, SslPolicyErrors.RemoteCertificateNameMismatch);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void ValidateRemoteCertificate_ThumbprintMode_WithNonMatchingThumbprint_ShouldReturnFalse()
    {
        // Arrange
        var certificate = CreateTestCertificate();
        var config = new SslConfig 
        { 
            ValidationMode = CertificateValidationMode.Thumbprint,
            TrustedCertificateThumbprints = { "INVALID_THUMBPRINT_1234567890ABCDEF1234567890ABCDEF12345678" }
        };
        var validator = new CertificateValidator(config, _mockLogger.Object);

        // Act
        var result = validator.ValidateRemoteCertificate(this, certificate, null, SslPolicyErrors.None);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void ValidateRemoteCertificate_ThumbprintMode_WithEmptyThumbprints_ShouldFallbackToStrict()
    {
        // Arrange
        var config = new SslConfig { ValidationMode = CertificateValidationMode.Thumbprint };
        var validator = new CertificateValidator(config, _mockLogger.Object);
        var certificate = CreateTestCertificate();

        // Act
        var result = validator.ValidateRemoteCertificate(this, certificate, null, SslPolicyErrors.None);

        // Assert
        result.Should().BeTrue(); // 回退到严格模式，无错误应该通过
    }

    [Fact]
    public void ValidateRemoteCertificate_CustomMode_WithValidCallback_ShouldUseCallback()
    {
        // Arrange
        var callbackInvoked = false;
        var config = new SslConfig 
        { 
            ValidationMode = CertificateValidationMode.Custom,
            RemoteCertificateValidationCallback = (sender, cert, chain, errors) =>
            {
                callbackInvoked = true;
                return true;
            }
        };
        var validator = new CertificateValidator(config, _mockLogger.Object);
        var certificate = CreateTestCertificate();

        // Act
        var result = validator.ValidateRemoteCertificate(this, certificate, null, SslPolicyErrors.RemoteCertificateNameMismatch);

        // Assert
        result.Should().BeTrue();
        callbackInvoked.Should().BeTrue();
    }

    [Fact]
    public void ValidateRemoteCertificate_CustomMode_WithNullCallback_ShouldFallbackToStrict()
    {
        // Arrange
        var config = new SslConfig { ValidationMode = CertificateValidationMode.Custom };
        var validator = new CertificateValidator(config, _mockLogger.Object);
        var certificate = CreateTestCertificate();

        // Act
        var result = validator.ValidateRemoteCertificate(this, certificate, null, SslPolicyErrors.None);

        // Assert
        result.Should().BeTrue(); // 回退到严格模式，无错误应该通过
    }

    [Fact]
    public void ValidateRemoteCertificate_WithException_ShouldReturnFalse()
    {
        // Arrange
        var config = new SslConfig 
        { 
            ValidationMode = CertificateValidationMode.Custom,
            RemoteCertificateValidationCallback = (sender, cert, chain, errors) => throw new InvalidOperationException("Test exception")
        };
        var validator = new CertificateValidator(config, _mockLogger.Object);
        var certificate = CreateTestCertificate();

        // Act
        var result = validator.ValidateRemoteCertificate(this, certificate, null, SslPolicyErrors.None);

        // Assert
        result.Should().BeFalse();
    }

    /// <summary>
    /// 创建测试证书
    /// </summary>
    /// <returns>测试证书</returns>
    private static X509Certificate2 CreateTestCertificate()
    {
        using var rsa = RSA.Create(2048);
        var request = new CertificateRequest("CN=Test Certificate", rsa, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
        
        request.CertificateExtensions.Add(new X509BasicConstraintsExtension(false, false, 0, false));
        request.CertificateExtensions.Add(new X509KeyUsageExtension(X509KeyUsageFlags.DigitalSignature | X509KeyUsageFlags.KeyEncipherment, false));
        
        var certificate = request.CreateSelfSigned(DateTimeOffset.UtcNow.AddDays(-1), DateTimeOffset.UtcNow.AddDays(365));
        return certificate;
    }

    /// <summary>
    /// 获取证书SHA-256指纹
    /// </summary>
    /// <param name="certificate">证书</param>
    /// <returns>指纹</returns>
    private static string GetCertificateThumbprint(X509Certificate2 certificate)
    {
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(certificate.RawData);
        return Convert.ToHexString(hash);
    }
}
