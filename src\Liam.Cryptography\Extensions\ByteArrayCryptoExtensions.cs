using Liam.Cryptography.Services;
using System.Security.Cryptography;

namespace Liam.Cryptography.Extensions;

/// <summary>
/// 字节数组加密扩展方法
/// </summary>
public static class ByteArrayCryptoExtensions
{
    /// <summary>
    /// 计算字节数组的SHA256哈希值
    /// </summary>
    /// <param name="input">输入字节数组</param>
    /// <returns>SHA256哈希值（十六进制字符串）</returns>
    public static string ToSha256Hash(this byte[] input)
    {
        var hashProvider = new Sha256HashProvider();
        return hashProvider.ComputeHash(input);
    }

    /// <summary>
    /// 计算字节数组的SHA256哈希值（异步）
    /// </summary>
    /// <param name="input">输入字节数组</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>SHA256哈希值（十六进制字符串）</returns>
    public static async Task<string> ToSha256HashAsync(this byte[] input, CancellationToken cancellationToken = default)
    {
        var hashProvider = new Sha256HashProvider();
        return await hashProvider.ComputeHashAsync(input, cancellationToken);
    }

    /// <summary>
    /// 将字节数组转换为十六进制字符串
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="lowercase">是否使用小写字母（默认为true）</param>
    /// <returns>十六进制字符串</returns>
    public static string ToHexString(this byte[] bytes, bool lowercase = true)
    {
        if (bytes == null)
            throw new ArgumentNullException(nameof(bytes), "字节数组不能为null");

        if (bytes.Length == 0)
            return string.Empty;

        var hex = Convert.ToHexString(bytes);
        return lowercase ? hex.ToLowerInvariant() : hex;
    }

    /// <summary>
    /// 将字节数组转换为Base64字符串
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <returns>Base64字符串</returns>
    public static string ToBase64String(this byte[] bytes)
    {
        if (bytes == null)
            throw new ArgumentNullException(nameof(bytes), "字节数组不能为null");

        if (bytes.Length == 0)
            return string.Empty;

        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// 安全地清除字节数组内容
    /// </summary>
    /// <param name="bytes">要清除的字节数组</param>
    public static void SecureClear(this byte[] bytes)
    {
        if (bytes == null)
            throw new ArgumentNullException(nameof(bytes), "字节数组不能为null");

        if (bytes.Length > 0)
        {
            Array.Clear(bytes, 0, bytes.Length);
        }
    }

    /// <summary>
    /// 安全地比较两个字节数组是否相等（防止时序攻击）
    /// </summary>
    /// <param name="a">第一个字节数组</param>
    /// <param name="b">第二个字节数组</param>
    /// <returns>比较结果</returns>
    public static bool SecureEquals(this byte[] a, byte[] b)
    {
        if (a == null && b == null)
            return true;

        if (a == null || b == null)
            return false;

        if (a.Length != b.Length)
            return false;

        var result = 0;
        for (int i = 0; i < a.Length; i++)
        {
            result |= a[i] ^ b[i];
        }

        return result == 0;
    }

    /// <summary>
    /// 生成随机字节数组
    /// </summary>
    /// <param name="length">字节数组长度</param>
    /// <returns>随机字节数组</returns>
    public static byte[] GenerateRandomBytes(int length)
    {
        if (length <= 0)
            throw new ArgumentException("长度必须大于0", nameof(length));

        var bytes = new byte[length];
        using var rng = System.Security.Cryptography.RandomNumberGenerator.Create();
        rng.GetBytes(bytes);
        return bytes;
    }

    /// <summary>
    /// 验证字节数组的SHA256哈希值
    /// </summary>
    /// <param name="input">输入字节数组</param>
    /// <param name="expectedHash">期望的哈希值</param>
    /// <returns>验证结果</returns>
    public static bool VerifySha256Hash(this byte[] input, string expectedHash)
    {
        var hashProvider = new Sha256HashProvider();
        return hashProvider.VerifyHash(input, expectedHash);
    }

    /// <summary>
    /// 使用AES加密字节数组
    /// </summary>
    /// <param name="plainData">明文数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <returns>加密后的字节数组</returns>
    public static byte[] EncryptAes(this byte[] plainData, byte[] key, byte[]? iv = null)
    {
        if (plainData == null)
            throw new ArgumentNullException(nameof(plainData), "明文数据不能为null");
        if (key == null)
            throw new ArgumentNullException(nameof(key), "密钥不能为null");

        // 直接使用AES加密字节数组，不进行UTF-8转换
        using var aes = System.Security.Cryptography.Aes.Create();
        aes.Key = key;

        if (iv != null)
        {
            aes.IV = iv;
        }

        using var encryptor = aes.CreateEncryptor();
        using var msEncrypt = new MemoryStream();
        using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);

        csEncrypt.Write(plainData, 0, plainData.Length);
        csEncrypt.FlushFinalBlock();

        var encrypted = msEncrypt.ToArray();

        // 如果没有提供IV，将IV和加密数据组合返回
        if (iv == null)
        {
            var result = new byte[aes.IV.Length + encrypted.Length];
            Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
            Array.Copy(encrypted, 0, result, aes.IV.Length, encrypted.Length);
            return result;
        }
        else
        {
            // 如果提供了IV，只返回加密数据
            return encrypted;
        }
    }

    /// <summary>
    /// 使用AES加密字节数组（异步）
    /// </summary>
    /// <param name="plainData">明文数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加密后的字节数组</returns>
    public static async Task<byte[]> EncryptAesAsync(this byte[] plainData, byte[] key, byte[]? iv = null, CancellationToken cancellationToken = default)
    {
        var aes = new AesSymmetricCrypto();
        var plainText = System.Text.Encoding.UTF8.GetString(plainData);
        return await aes.EncryptAsync(plainText, key, iv, cancellationToken);
    }

    /// <summary>
    /// 使用AES解密字节数组
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <returns>解密后的字节数组</returns>
    public static byte[] DecryptAes(this byte[] cipherData, byte[] key, byte[]? iv = null)
    {
        if (cipherData == null)
            throw new ArgumentNullException(nameof(cipherData), "密文数据不能为null");
        if (key == null)
            throw new ArgumentNullException(nameof(key), "密钥不能为null");

        // 直接解密字节数组，不进行UTF-8转换
        using var aes = System.Security.Cryptography.Aes.Create();
        aes.Key = key;

        byte[] actualIV;
        byte[] actualCipherData;

        if (iv != null)
        {
            // 如果提供了IV，直接使用提供的IV和密文数据
            if (iv.Length != 16) // AES块大小为128位（16字节）
                throw new ArgumentException("AES初始化向量长度必须为16字节", nameof(iv));
            actualIV = iv;
            actualCipherData = cipherData;
        }
        else
        {
            // 如果没有提供IV，从密文数据中提取IV
            if (cipherData.Length < 16)
                throw new ArgumentException("密文数据长度不足", nameof(cipherData));

            actualIV = new byte[16];
            actualCipherData = new byte[cipherData.Length - 16];

            Array.Copy(cipherData, 0, actualIV, 0, 16);
            Array.Copy(cipherData, 16, actualCipherData, 0, actualCipherData.Length);
        }

        aes.IV = actualIV;

        using var decryptor = aes.CreateDecryptor();
        using var msDecrypt = new MemoryStream(actualCipherData);
        using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
        using var msResult = new MemoryStream();

        csDecrypt.CopyTo(msResult);
        return msResult.ToArray();
    }

    /// <summary>
    /// 使用AES解密字节数组（异步）
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解密后的字节数组</returns>
    public static async Task<byte[]> DecryptAesAsync(this byte[] cipherData, byte[] key, byte[]? iv = null, CancellationToken cancellationToken = default)
    {
        var aes = new AesSymmetricCrypto();
        var decryptedText = await aes.DecryptAsync(cipherData, key, iv, cancellationToken);
        return System.Text.Encoding.UTF8.GetBytes(decryptedText);
    }

    /// <summary>
    /// 使用RSA私钥对字节数组进行数字签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>数字签名</returns>
    public static byte[] SignRsa(this byte[] data, string privateKey)
    {
        var signature = new RsaDigitalSignature();
        return signature.Sign(data, privateKey);
    }

    /// <summary>
    /// 使用RSA私钥对字节数组进行数字签名（异步）
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>数字签名</returns>
    public static async Task<byte[]> SignRsaAsync(this byte[] data, string privateKey, CancellationToken cancellationToken = default)
    {
        var signature = new RsaDigitalSignature();
        return await signature.SignAsync(data, privateKey, cancellationToken);
    }

    /// <summary>
    /// 使用RSA公钥验证字节数组的数字签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>验证结果</returns>
    public static bool VerifyRsaSignature(this byte[] data, byte[] signature, string publicKey)
    {
        var signatureService = new RsaDigitalSignature();
        return signatureService.Verify(data, signature, publicKey);
    }

    /// <summary>
    /// 使用RSA公钥验证字节数组的数字签名（异步）
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">公钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    public static async Task<bool> VerifyRsaSignatureAsync(this byte[] data, byte[] signature, string publicKey, CancellationToken cancellationToken = default)
    {
        var signatureService = new RsaDigitalSignature();
        return await signatureService.VerifyAsync(data, signature, publicKey, cancellationToken);
    }

    /// <summary>
    /// 将十六进制字符串转换为字节数组
    /// </summary>
    /// <param name="hex">十六进制字符串</param>
    /// <returns>字节数组</returns>
    public static byte[] FromHexString(string hex)
    {
        if (string.IsNullOrEmpty(hex))
            return Array.Empty<byte>();

        if (hex.Length % 2 != 0)
            throw new ArgumentException("十六进制字符串长度必须是偶数", nameof(hex));

        return Convert.FromHexString(hex);
    }

    /// <summary>
    /// 将Base64字符串转换为字节数组
    /// </summary>
    /// <param name="base64">Base64字符串</param>
    /// <returns>字节数组</returns>
    public static byte[] FromBase64String(string base64)
    {
        if (string.IsNullOrEmpty(base64))
            return Array.Empty<byte>();

        return Convert.FromBase64String(base64);
    }
}
