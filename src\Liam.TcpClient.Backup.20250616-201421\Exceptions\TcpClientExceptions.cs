using Liam.TcpClient.Models;

namespace Liam.TcpClient.Exceptions;

/// <summary>
/// TCP客户端基础异常（保持向后兼容性）
/// </summary>
public class TcpClientException : TcpClientExceptionBase
{
    /// <summary>
    /// 异常分类
    /// </summary>
    public override TcpClientExceptionCategory Category => TcpClientExceptionCategory.Network;

    /// <summary>
    /// 初始化TCP客户端异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="connectionState">连接状态</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="operationContext">操作上下文</param>
    public TcpClientException(
        string message = "TCP客户端异常",
        Exception? innerException = null,
        string? connectionState = null,
        TcpClientConfig? configuration = null,
        Dictionary<string, object>? operationContext = null)
        : base(message, innerException, connectionState, configuration, operationContext)
    {
    }
}

/// <summary>
/// 连接异常
/// </summary>
public class ConnectionException : TcpClientExceptionBase
{
    /// <summary>
    /// 异常分类
    /// </summary>
    public override TcpClientExceptionCategory Category => TcpClientExceptionCategory.Network;

    /// <summary>
    /// 异常严重程度
    /// </summary>
    public override TcpClientExceptionSeverity Severity => TcpClientExceptionSeverity.Error;

    /// <summary>
    /// 是否可重试
    /// </summary>
    public override bool IsRetryable => true;

    /// <summary>
    /// 建议的重试延迟时间
    /// </summary>
    public override TimeSpan? SuggestedRetryDelay => TimeSpan.FromSeconds(5);

    /// <summary>
    /// 初始化连接异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="connectionState">连接状态</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="operationContext">操作上下文</param>
    public ConnectionException(
        string message = "连接异常",
        Exception? innerException = null,
        string? connectionState = null,
        TcpClientConfig? configuration = null,
        Dictionary<string, object>? operationContext = null)
        : base(message, innerException, connectionState, configuration, operationContext)
    {
    }

    /// <summary>
    /// 创建连接失败异常
    /// </summary>
    /// <param name="host">主机地址</param>
    /// <param name="port">端口号</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="configuration">客户端配置</param>
    /// <returns>连接异常</returns>
    public static ConnectionException CreateConnectionFailed(string host, int port, Exception? innerException = null, TcpClientConfig? configuration = null)
    {
        var context = new Dictionary<string, object>
        {
            ["Host"] = host,
            ["Port"] = port,
            ["Operation"] = "Connect"
        };

        return new ConnectionException(
            $"无法连接到服务器 {host}:{port}",
            innerException,
            "Disconnected",
            configuration,
            context);
    }
}

/// <summary>
/// 连接超时异常
/// </summary>
public class ConnectionTimeoutException : TcpClientExceptionBase
{
    /// <summary>
    /// 超时时间
    /// </summary>
    public TimeSpan Timeout { get; }

    /// <summary>
    /// 异常分类
    /// </summary>
    public override TcpClientExceptionCategory Category => TcpClientExceptionCategory.Timeout;

    /// <summary>
    /// 异常严重程度
    /// </summary>
    public override TcpClientExceptionSeverity Severity => TcpClientExceptionSeverity.Warning;

    /// <summary>
    /// 是否可重试
    /// </summary>
    public override bool IsRetryable => true;

    /// <summary>
    /// 建议的重试延迟时间
    /// </summary>
    public override TimeSpan? SuggestedRetryDelay => TimeSpan.FromSeconds(Math.Min(Timeout.TotalSeconds * 0.5, 30));

    /// <summary>
    /// 初始化连接超时异常
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="connectionState">连接状态</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="operationContext">操作上下文</param>
    public ConnectionTimeoutException(
        TimeSpan timeout,
        string? message = null,
        Exception? innerException = null,
        string? connectionState = null,
        TcpClientConfig? configuration = null,
        Dictionary<string, object>? operationContext = null)
        : base(message ?? $"连接超时，超时时间：{timeout.TotalSeconds}秒", innerException, connectionState, configuration, operationContext)
    {
        Timeout = timeout;
        AddContext("TimeoutSeconds", timeout.TotalSeconds);
        AddContext("Operation", "Connect");
    }
}

/// <summary>
/// 认证异常
/// </summary>
public class AuthenticationException : TcpClientExceptionBase
{
    /// <summary>
    /// 异常分类
    /// </summary>
    public override TcpClientExceptionCategory Category => TcpClientExceptionCategory.Authentication;

    /// <summary>
    /// 异常严重程度
    /// </summary>
    public override TcpClientExceptionSeverity Severity => TcpClientExceptionSeverity.Error;

    /// <summary>
    /// 是否可重试
    /// </summary>
    public override bool IsRetryable => false;

    /// <summary>
    /// 初始化认证异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="connectionState">连接状态</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="operationContext">操作上下文</param>
    public AuthenticationException(
        string message = "认证失败",
        Exception? innerException = null,
        string? connectionState = null,
        TcpClientConfig? configuration = null,
        Dictionary<string, object>? operationContext = null)
        : base(message, innerException, connectionState, configuration, operationContext)
    {
        AddContext("Operation", "Authentication");
    }
}

/// <summary>
/// 心跳超时异常
/// </summary>
public class HeartbeatTimeoutException : TcpClientException
{
    /// <summary>
    /// 超时时间
    /// </summary>
    public TimeSpan Timeout { get; }

    /// <summary>
    /// 初始化心跳超时异常
    /// </summary>
    /// <param name="timeout">超时时间</param>
    public HeartbeatTimeoutException(TimeSpan timeout) 
        : base($"心跳超时，超时时间：{timeout.TotalSeconds}秒")
    {
        Timeout = timeout;
    }

    /// <summary>
    /// 初始化心跳超时异常
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="message">异常消息</param>
    public HeartbeatTimeoutException(TimeSpan timeout, string message) : base(message)
    {
        Timeout = timeout;
    }

    /// <summary>
    /// 初始化心跳超时异常
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public HeartbeatTimeoutException(TimeSpan timeout, string message, Exception innerException) 
        : base(message, innerException)
    {
        Timeout = timeout;
    }
}

/// <summary>
/// 消息异常
/// </summary>
public class MessageException : TcpClientException
{
    /// <summary>
    /// 初始化消息异常
    /// </summary>
    public MessageException() : base()
    {
    }

    /// <summary>
    /// 初始化消息异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public MessageException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化消息异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public MessageException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// SSL异常
/// </summary>
public class SslException : TcpClientException
{
    /// <summary>
    /// 初始化SSL异常
    /// </summary>
    public SslException() : base()
    {
    }

    /// <summary>
    /// 初始化SSL异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public SslException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化SSL异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public SslException(string message, Exception innerException) : base(message, innerException)
    {
    }
}
