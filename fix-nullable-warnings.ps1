# PowerShell脚本用于修复可空引用类型警告
# 批量替换测试文件中的可空引用警告

$testFiles = Get-ChildItem -Path "tests/Liam.Cryptography.Tests" -Recurse -Filter "*.cs"

foreach ($file in $testFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content

    # 修复 keyPair.PrivateKey 的警告（避免重复添加!）
    $content = $content -replace '(\w+\.PrivateKey)(?!\!)', '$1!'

    # 修复 keyPair.PublicKey 的警告（避免重复添加!）
    $content = $content -replace '(\w+\.PublicKey)(?!\!)', '$1!'

    # 修复其他常见的null引用警告
    # 修复 null! 参数传递
    $content = $content -replace '\bnull\!(?!\!)', 'null!'

    # 修复方法调用中的可能为null的参数
    $content = $content -replace '(\w+\.\w+)\s*\(([^,\)]*),\s*(\w+\.(?:PrivateKey|PublicKey))(?!\!)([,\)])', '$1($2, $3!$4'

    # 修复扩展方法调用
    $content = $content -replace '(\w+)\.(\w+)\(([^,\)]*),\s*(\w+\.(?:PrivateKey|PublicKey))(?!\!)([,\)])', '$1.$2($3, $4!$5'

    # 如果内容有变化，写回文件
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "Fixed nullable warnings in: $($file.Name)"
    }
}

Write-Host "Nullable warning fixes completed."
