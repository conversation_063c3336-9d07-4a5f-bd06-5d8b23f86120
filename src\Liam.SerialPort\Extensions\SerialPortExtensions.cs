using System.Text;
using Liam.SerialPort.Constants;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;

namespace Liam.SerialPort.Extensions;

/// <summary>
/// 串口扩展方法
/// </summary>
public static class SerialPortExtensions
{
    /// <summary>
    /// 发送十六进制字符串
    /// </summary>
    /// <param name="service">串口服务</param>
    /// <param name="hexString">十六进制字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    public static async Task SendHexAsync(this ISerialPortService service, string hexString, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(hexString))
            return;

        var bytes = HexStringToBytes(hexString);
        await service.SendAsync(bytes, cancellationToken);
    }

    /// <summary>
    /// 发送带换行符的字符串
    /// </summary>
    /// <param name="service">串口服务</param>
    /// <param name="data">要发送的字符串</param>
    /// <param name="newLine">换行符，默认为CRLF</param>
    /// <param name="cancellationToken">取消令牌</param>
    public static async Task SendLineAsync(this ISerialPortService service, string data, string? newLine = null, CancellationToken cancellationToken = default)
    {
        newLine ??= SerialPortConstants.DataFormats.NewLines.CRLF;
        await service.SendAsync(data + newLine, cancellationToken);
    }

    /// <summary>
    /// 批量发送数据
    /// </summary>
    /// <param name="service">串口服务</param>
    /// <param name="dataList">数据列表</param>
    /// <param name="interval">发送间隔</param>
    /// <param name="cancellationToken">取消令牌</param>
    public static async Task SendBatchAsync(this ISerialPortService service, IEnumerable<byte[]> dataList, TimeSpan interval, CancellationToken cancellationToken = default)
    {
        foreach (var data in dataList)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            await service.SendAsync(data, cancellationToken);
            
            if (interval > TimeSpan.Zero)
            {
                await Task.Delay(interval, cancellationToken);
            }
        }
    }

    /// <summary>
    /// 批量发送字符串
    /// </summary>
    /// <param name="service">串口服务</param>
    /// <param name="dataList">字符串列表</param>
    /// <param name="interval">发送间隔</param>
    /// <param name="cancellationToken">取消令牌</param>
    public static async Task SendBatchAsync(this ISerialPortService service, IEnumerable<string> dataList, TimeSpan interval, CancellationToken cancellationToken = default)
    {
        foreach (var data in dataList)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            await service.SendAsync(data, cancellationToken);
            
            if (interval > TimeSpan.Zero)
            {
                await Task.Delay(interval, cancellationToken);
            }
        }
    }

    /// <summary>
    /// 等待特定数据
    /// </summary>
    /// <param name="service">串口服务</param>
    /// <param name="expectedData">期望的数据</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否接收到期望的数据</returns>
    public static async Task<bool> WaitForDataAsync(this ISerialPortService service, byte[] expectedData, TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        var buffer = new List<byte>();
        var endTime = DateTime.Now.Add(timeout);
        var dataReceived = false;

        void OnDataReceived(object? sender, Events.DataReceivedEventArgs e)
        {
            buffer.AddRange(e.Data);
            
            if (buffer.Count >= expectedData.Length)
            {
                var lastBytes = buffer.Skip(buffer.Count - expectedData.Length).ToArray();
                if (lastBytes.SequenceEqual(expectedData))
                {
                    dataReceived = true;
                }
            }
        }

        try
        {
            service.DataReceived += OnDataReceived;

            while (DateTime.Now < endTime && !dataReceived && !cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(100, cancellationToken);
            }

            return dataReceived;
        }
        finally
        {
            service.DataReceived -= OnDataReceived;
        }
    }

    /// <summary>
    /// 等待特定字符串
    /// </summary>
    /// <param name="service">串口服务</param>
    /// <param name="expectedString">期望的字符串</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="encoding">字符编码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否接收到期望的字符串</returns>
    public static async Task<bool> WaitForStringAsync(this ISerialPortService service, string expectedString, TimeSpan timeout, Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        encoding ??= Encoding.UTF8;
        var expectedData = encoding.GetBytes(expectedString);
        return await WaitForDataAsync(service, expectedData, timeout, cancellationToken);
    }

    /// <summary>
    /// 检查串口是否为USB设备
    /// </summary>
    /// <param name="portInfo">串口信息</param>
    /// <returns>是否为USB设备</returns>
    public static bool IsUsbDevice(this SerialPortInfo portInfo)
    {
        return portInfo.IsUsbDevice;
    }

    /// <summary>
    /// 检查串口是否为虚拟端口
    /// </summary>
    /// <param name="portInfo">串口信息</param>
    /// <returns>是否为虚拟端口</returns>
    public static bool IsVirtualPort(this SerialPortInfo portInfo)
    {
        return portInfo.IsVirtualPort;
    }

    /// <summary>
    /// 获取串口的友好显示名称
    /// </summary>
    /// <param name="portInfo">串口信息</param>
    /// <returns>友好显示名称</returns>
    public static string GetDisplayName(this SerialPortInfo portInfo)
    {
        return portInfo.DisplayName;
    }

    /// <summary>
    /// 创建常用的串口设置
    /// </summary>
    /// <param name="baudRate">波特率</param>
    /// <param name="dataBits">数据位</param>
    /// <param name="stopBits">停止位</param>
    /// <param name="parity">校验位</param>
    /// <returns>串口设置</returns>
    public static SerialPortSettings CreateSettings(int baudRate = 9600, int dataBits = 8, System.IO.Ports.StopBits stopBits = System.IO.Ports.StopBits.One, System.IO.Ports.Parity parity = System.IO.Ports.Parity.None)
    {
        return new SerialPortSettings
        {
            BaudRate = baudRate,
            DataBits = dataBits,
            StopBits = stopBits,
            Parity = parity
        };
    }

    /// <summary>
    /// 创建高速串口设置
    /// </summary>
    /// <returns>高速串口设置</returns>
    public static SerialPortSettings CreateHighSpeedSettings()
    {
        return new SerialPortSettings
        {
            BaudRate = SerialPortConstants.BaudRates.Baud115200,
            DataBits = 8,
            StopBits = System.IO.Ports.StopBits.One,
            Parity = System.IO.Ports.Parity.None,
            ReadTimeout = 1000,
            WriteTimeout = 1000,
            ReceiveBufferSize = 8192,
            SendBufferSize = 4096
        };
    }

    /// <summary>
    /// 创建低速串口设置
    /// </summary>
    /// <returns>低速串口设置</returns>
    public static SerialPortSettings CreateLowSpeedSettings()
    {
        return new SerialPortSettings
        {
            BaudRate = SerialPortConstants.BaudRates.Baud9600,
            DataBits = 8,
            StopBits = System.IO.Ports.StopBits.One,
            Parity = System.IO.Ports.Parity.None,
            ReadTimeout = 5000,
            WriteTimeout = 5000,
            ReceiveBufferSize = 1024,
            SendBufferSize = 512
        };
    }

    /// <summary>
    /// 验证波特率是否有效
    /// </summary>
    /// <param name="baudRate">波特率</param>
    /// <returns>是否有效</returns>
    public static bool IsValidBaudRate(int baudRate)
    {
        return baudRate >= SerialPortConstants.Validation.MinBaudRate && 
               baudRate <= SerialPortConstants.Validation.MaxBaudRate;
    }

    /// <summary>
    /// 获取推荐的波特率列表
    /// </summary>
    /// <returns>推荐的波特率列表</returns>
    public static int[] GetRecommendedBaudRates()
    {
        return SerialPortConstants.BaudRates.Common;
    }

    /// <summary>
    /// 将字节数组转换为十六进制字符串
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="separator">分隔符</param>
    /// <returns>十六进制字符串</returns>
    public static string ToHexString(this byte[] bytes, string separator = " ")
    {
        if (bytes == null || bytes.Length == 0)
            return string.Empty;

        return string.Join(separator, bytes.Select(b => b.ToString("X2")));
    }

    /// <summary>
    /// 将十六进制字符串转换为字节数组
    /// </summary>
    /// <param name="hexString">十六进制字符串</param>
    /// <returns>字节数组</returns>
    public static byte[] HexStringToBytes(string hexString)
    {
        if (string.IsNullOrWhiteSpace(hexString))
            return Array.Empty<byte>();

        // 清理十六进制字符串
        var cleanHex = hexString;
        foreach (var separator in SerialPortConstants.DataFormats.HexSeparators)
        {
            cleanHex = cleanHex.Replace(separator, "");
        }

        if (cleanHex.Length % 2 != 0)
            throw new ArgumentException("十六进制字符串长度必须为偶数", nameof(hexString));

        var bytes = new byte[cleanHex.Length / 2];
        for (int i = 0; i < bytes.Length; i++)
        {
            bytes[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
        }

        return bytes;
    }

    /// <summary>
    /// 检查字节数组是否包含指定的模式
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="pattern">模式</param>
    /// <returns>是否包含模式</returns>
    public static bool ContainsPattern(this byte[] data, byte[] pattern)
    {
        if (data == null || pattern == null || pattern.Length == 0 || data.Length < pattern.Length)
            return false;

        for (int i = 0; i <= data.Length - pattern.Length; i++)
        {
            bool found = true;
            for (int j = 0; j < pattern.Length; j++)
            {
                if (data[i + j] != pattern[j])
                {
                    found = false;
                    break;
                }
            }
            if (found)
                return true;
        }

        return false;
    }

    /// <summary>
    /// 查找字节数组中指定模式的位置
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="pattern">模式</param>
    /// <returns>模式的位置，未找到返回-1</returns>
    public static int FindPattern(this byte[] data, byte[] pattern)
    {
        if (data == null || pattern == null || pattern.Length == 0 || data.Length < pattern.Length)
            return -1;

        for (int i = 0; i <= data.Length - pattern.Length; i++)
        {
            bool found = true;
            for (int j = 0; j < pattern.Length; j++)
            {
                if (data[i + j] != pattern[j])
                {
                    found = false;
                    break;
                }
            }
            if (found)
                return i;
        }

        return -1;
    }

    /// <summary>
    /// 计算校验和
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="checksumType">校验和类型</param>
    /// <returns>校验和</returns>
    public static byte CalculateChecksum(this byte[] data, ChecksumType checksumType = ChecksumType.Sum)
    {
        if (data == null || data.Length == 0)
            return 0;

        return checksumType switch
        {
            ChecksumType.Sum => (byte)(data.Sum(b => b) & 0xFF),
            ChecksumType.Xor => data.Aggregate((byte)0, (acc, b) => (byte)(acc ^ b)),
            ChecksumType.TwosComplement => (byte)(256 - (data.Sum(b => b) & 0xFF)),
            _ => 0
        };
    }

    /// <summary>
    /// 验证校验和
    /// </summary>
    /// <param name="data">数据（包含校验和）</param>
    /// <param name="checksumType">校验和类型</param>
    /// <returns>校验和是否正确</returns>
    public static bool VerifyChecksum(this byte[] data, ChecksumType checksumType = ChecksumType.Sum)
    {
        if (data == null || data.Length < 2)
            return false;

        var dataWithoutChecksum = data.Take(data.Length - 1).ToArray();
        var expectedChecksum = data.Last();
        var calculatedChecksum = dataWithoutChecksum.CalculateChecksum(checksumType);

        return expectedChecksum == calculatedChecksum;
    }
}

/// <summary>
/// 校验和类型
/// </summary>
public enum ChecksumType
{
    /// <summary>
    /// 简单求和
    /// </summary>
    Sum,

    /// <summary>
    /// 异或校验
    /// </summary>
    Xor,

    /// <summary>
    /// 二进制补码
    /// </summary>
    TwosComplement
}
