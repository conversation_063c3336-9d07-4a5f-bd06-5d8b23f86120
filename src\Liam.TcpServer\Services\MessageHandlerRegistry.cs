using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Liam.TcpServer.Interfaces;

namespace Liam.TcpServer.Services;

/// <summary>
/// 消息处理器注册表实现
/// </summary>
public class MessageHandlerRegistry : IMessageHandlerRegistry
{
    private readonly ConcurrentDictionary<byte, object> _handlers = new();
    private readonly ILogger<MessageHandlerRegistry>? _logger;

    /// <summary>
    /// 初始化消息处理器注册表
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public MessageHandlerRegistry(ILogger<MessageHandlerRegistry>? logger = null)
    {
        _logger = logger;
    }

    /// <summary>
    /// 注册消息处理器
    /// </summary>
    /// <typeparam name="T">消息类型</typeparam>
    /// <param name="handler">消息处理器</param>
    public void RegisterHandler<T>(ICustomMessageHandler<T> handler) where T : class
    {
        ArgumentNullException.ThrowIfNull(handler);

        if (_handlers.TryAdd(handler.MessageType, handler))
        {
            _logger?.LogInformation("注册消息处理器，消息类型: {MessageType}, 处理器类型: {HandlerType}", 
                handler.MessageType, handler.GetType().Name);
        }
        else
        {
            _logger?.LogWarning("消息类型 {MessageType} 的处理器已存在，注册失败", handler.MessageType);
            throw new InvalidOperationException($"消息类型 {handler.MessageType} 的处理器已存在");
        }
    }

    /// <summary>
    /// 注销消息处理器
    /// </summary>
    /// <param name="messageType">消息类型</param>
    public void UnregisterHandler(byte messageType)
    {
        if (_handlers.TryRemove(messageType, out var handler))
        {
            _logger?.LogInformation("注销消息处理器，消息类型: {MessageType}, 处理器类型: {HandlerType}", 
                messageType, handler.GetType().Name);
        }
        else
        {
            _logger?.LogWarning("消息类型 {MessageType} 的处理器不存在，注销失败", messageType);
        }
    }

    /// <summary>
    /// 获取消息处理器
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <returns>消息处理器</returns>
    public object? GetHandler(byte messageType)
    {
        _handlers.TryGetValue(messageType, out var handler);
        return handler;
    }

    /// <summary>
    /// 获取强类型消息处理器
    /// </summary>
    /// <typeparam name="T">消息类型</typeparam>
    /// <param name="messageType">消息类型</param>
    /// <returns>强类型消息处理器</returns>
    public ICustomMessageHandler<T>? GetHandler<T>(byte messageType) where T : class
    {
        var handler = GetHandler(messageType);
        return handler as ICustomMessageHandler<T>;
    }

    /// <summary>
    /// 检查是否有处理器
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <returns>是否有处理器</returns>
    public bool HasHandler(byte messageType)
    {
        return _handlers.ContainsKey(messageType);
    }

    /// <summary>
    /// 获取所有已注册的消息类型
    /// </summary>
    /// <returns>已注册的消息类型</returns>
    public IReadOnlyList<byte> GetRegisteredMessageTypes()
    {
        return _handlers.Keys.ToList().AsReadOnly();
    }

    /// <summary>
    /// 获取注册的处理器数量
    /// </summary>
    /// <returns>处理器数量</returns>
    public int GetHandlerCount()
    {
        return _handlers.Count;
    }

    /// <summary>
    /// 清空所有处理器
    /// </summary>
    public void ClearAllHandlers()
    {
        var count = _handlers.Count;
        _handlers.Clear();
        _logger?.LogInformation("清空了所有消息处理器，共 {Count} 个", count);
    }

    /// <summary>
    /// 获取处理器统计信息
    /// </summary>
    /// <returns>处理器统计信息</returns>
    public MessageHandlerStatistics GetStatistics()
    {
        var handlersByType = _handlers
            .GroupBy(kvp => kvp.Value.GetType().Name)
            .ToDictionary(g => g.Key, g => g.Count());

        return new MessageHandlerStatistics
        {
            TotalHandlers = _handlers.Count,
            RegisteredMessageTypes = GetRegisteredMessageTypes(),
            HandlersByType = handlersByType,
            LastUpdatedAt = DateTime.UtcNow
        };
    }
}

/// <summary>
/// 消息处理器统计信息
/// </summary>
public class MessageHandlerStatistics
{
    /// <summary>
    /// 总处理器数量
    /// </summary>
    public int TotalHandlers { get; set; }

    /// <summary>
    /// 已注册的消息类型
    /// </summary>
    public IReadOnlyList<byte> RegisteredMessageTypes { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 按处理器类型分组的统计
    /// </summary>
    public Dictionary<string, int> HandlersByType { get; set; } = new();

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; }
}
