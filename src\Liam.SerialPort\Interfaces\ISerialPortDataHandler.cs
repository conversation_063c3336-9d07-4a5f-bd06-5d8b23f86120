using Liam.SerialPort.Events;
using <PERSON>.SerialPort.Models;

namespace Liam.SerialPort.Interfaces;

/// <summary>
/// 串口数据处理接口，提供数据的发送、接收和缓冲管理功能
/// </summary>
public interface ISerialPortDataHandler : IDisposable
{
    /// <summary>
    /// 数据接收事件
    /// </summary>
    event EventHandler<DataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 数据发送完成事件
    /// </summary>
    event EventHandler<DataSentEventArgs>? DataSent;

    /// <summary>
    /// 数据处理错误事件
    /// </summary>
    event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 获取接收缓冲区中的字节数
    /// </summary>
    int BytesToRead { get; }

    /// <summary>
    /// 获取发送缓冲区中的字节数
    /// </summary>
    int BytesToWrite { get; }

    /// <summary>
    /// 获取接收缓冲区大小
    /// </summary>
    int ReceiveBufferSize { get; }

    /// <summary>
    /// 获取发送缓冲区大小
    /// </summary>
    int SendBufferSize { get; }

    /// <summary>
    /// 发送数据（字节数组）
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task SendAsync(byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据（字符串）
    /// </summary>
    /// <param name="data">要发送的字符串</param>
    /// <param name="encoding">字符编码，默认为UTF-8</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task SendAsync(string data, System.Text.Encoding? encoding = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据（十六进制字符串）
    /// </summary>
    /// <param name="hexData">十六进制字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task SendHexAsync(string hexData, CancellationToken cancellationToken = default);

    /// <summary>
    /// 读取数据（字节数组）
    /// </summary>
    /// <param name="count">要读取的字节数，-1表示读取所有可用数据</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取到的数据</returns>
    Task<byte[]> ReadAsync(int count = -1, TimeSpan? timeout = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 读取数据（字符串）
    /// </summary>
    /// <param name="encoding">字符编码，默认为UTF-8</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取到的字符串</returns>
    Task<string> ReadStringAsync(System.Text.Encoding? encoding = null, TimeSpan? timeout = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 读取一行数据
    /// </summary>
    /// <param name="encoding">字符编码，默认为UTF-8</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取到的行数据</returns>
    Task<string> ReadLineAsync(System.Text.Encoding? encoding = null, TimeSpan? timeout = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据并等待响应
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收到的响应数据</returns>
    Task<byte[]> SendAndReceiveAsync(byte[] data, TimeSpan timeout, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据并等待响应
    /// </summary>
    /// <param name="data">要发送的字符串</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="encoding">字符编码，默认为UTF-8</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收到的响应字符串</returns>
    Task<string> SendAndReceiveAsync(string data, TimeSpan timeout, System.Text.Encoding? encoding = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清空接收缓冲区
    /// </summary>
    void ClearReceiveBuffer();

    /// <summary>
    /// 清空发送缓冲区
    /// </summary>
    void ClearSendBuffer();

    /// <summary>
    /// 清空所有缓冲区
    /// </summary>
    void ClearAllBuffers();

    /// <summary>
    /// 开始数据接收监听
    /// </summary>
    void StartListening();

    /// <summary>
    /// 停止数据接收监听
    /// </summary>
    void StopListening();
}
