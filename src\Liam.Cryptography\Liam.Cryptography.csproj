﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>

    <!-- NuGet包信息 -->
    <PackageId>Liam.Cryptography</PackageId>
    <Version>1.1.0</Version>
    <Authors>Liam</Authors>
    <Description>Liam系列加密功能库，提供完整的对称加密、非对称加密、哈希算法、数字签名和密钥管理功能。支持AES、ChaCha20-Poly1305、RSA、ECDSA、SHA256、Argon2等现代加密算法，包含流式处理和性能优化。</Description>
    <PackageTags>cryptography;encryption;decryption;aes;chacha20;poly1305;rsa;ecdsa;ed25519;sha256;argon2;digital-signature;key-management;streaming;performance;liam</PackageTags>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <RepositoryUrl>https://gitee.com/liam-gitee/liam.git</RepositoryUrl>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <PackageIcon>icon.png</PackageIcon>
  </PropertyGroup>

  <ItemGroup>
    <None Include="README.md" Pack="true" PackagePath="\" />
    <None Include="../../icon.png" Pack="true" PackagePath="\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Konscious.Security.Cryptography.Argon2" Version="1.3.1" />
  </ItemGroup>

</Project>
