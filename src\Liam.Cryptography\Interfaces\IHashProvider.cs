namespace Liam.Cryptography.Interfaces;

/// <summary>
/// 哈希算法提供者接口
/// </summary>
public interface IHashProvider
{
    /// <summary>
    /// 计算字符串的哈希值
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    string ComputeHash(string input);

    /// <summary>
    /// 计算字节数组的哈希值
    /// </summary>
    /// <param name="input">输入字节数组</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    string ComputeHash(byte[] input);

    /// <summary>
    /// 计算字符串的哈希值（异步）
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    Task<string> ComputeHashAsync(string input, CancellationToken cancellationToken = default);

    /// <summary>
    /// 计算字节数组的哈希值（异步）
    /// </summary>
    /// <param name="input">输入字节数组</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    Task<string> ComputeHashAsync(byte[] input, CancellationToken cancellationToken = default);

    /// <summary>
    /// 计算文件的哈希值
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    string ComputeFileHash(string filePath);

    /// <summary>
    /// 计算文件的哈希值（异步）
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    Task<string> ComputeFileHashAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证哈希值
    /// </summary>
    /// <param name="input">输入数据</param>
    /// <param name="expectedHash">期望的哈希值</param>
    /// <returns>验证结果</returns>
    bool VerifyHash(string input, string expectedHash);

    /// <summary>
    /// 验证哈希值
    /// </summary>
    /// <param name="input">输入数据</param>
    /// <param name="expectedHash">期望的哈希值</param>
    /// <returns>验证结果</returns>
    bool VerifyHash(byte[] input, string expectedHash);
}
