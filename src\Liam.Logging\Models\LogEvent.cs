using <PERSON>.Logging.Constants;

namespace Liam.Logging.Models;

/// <summary>
/// 日志事件模型，包含完整的日志信息
/// </summary>
public class LogEvent
{
    /// <summary>
    /// 日志事件的唯一标识符
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString("N");

    /// <summary>
    /// 日志级别
    /// </summary>
    public LogLevel Level { get; set; }

    /// <summary>
    /// 日志消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 日志模板（用于结构化日志）
    /// </summary>
    public string? MessageTemplate { get; set; }

    /// <summary>
    /// 日志参数（用于结构化日志）
    /// </summary>
    public object[]? Parameters { get; set; }

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 日志时间戳
    /// </summary>
    public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.Now;

    /// <summary>
    /// 日志类别（通常是类名）
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 事件ID
    /// </summary>
    public int EventId { get; set; }

    /// <summary>
    /// 事件名称
    /// </summary>
    public string? EventName { get; set; }

    /// <summary>
    /// 线程ID
    /// </summary>
    public int ThreadId { get; set; } = Environment.CurrentManagedThreadId;

    /// <summary>
    /// 进程ID
    /// </summary>
    public int ProcessId { get; set; } = Environment.ProcessId;

    /// <summary>
    /// 机器名称
    /// </summary>
    public string MachineName { get; set; } = Environment.MachineName;

    /// <summary>
    /// 用户名
    /// </summary>
    public string? UserName { get; set; } = Environment.UserName;

    /// <summary>
    /// 应用程序名称
    /// </summary>
    public string? ApplicationName { get; set; }

    /// <summary>
    /// 日志作用域信息
    /// </summary>
    public Dictionary<string, object?> Scopes { get; set; } = new();

    /// <summary>
    /// 自定义属性
    /// </summary>
    public Dictionary<string, object?> Properties { get; set; } = new();

    /// <summary>
    /// 日志来源信息
    /// </summary>
    public LogSource? Source { get; set; }

    /// <summary>
    /// 创建日志事件的副本
    /// </summary>
    /// <returns>日志事件副本</returns>
    public LogEvent Clone()
    {
        return new LogEvent
        {
            Id = Id,
            Level = Level,
            Message = Message,
            MessageTemplate = MessageTemplate,
            Parameters = Parameters?.ToArray(),
            Exception = Exception,
            Timestamp = Timestamp,
            Category = Category,
            EventId = EventId,
            EventName = EventName,
            ThreadId = ThreadId,
            ProcessId = ProcessId,
            MachineName = MachineName,
            UserName = UserName,
            ApplicationName = ApplicationName,
            Scopes = new Dictionary<string, object?>(Scopes),
            Properties = new Dictionary<string, object?>(Properties),
            Source = Source?.Clone()
        };
    }
}

/// <summary>
/// 日志来源信息
/// </summary>
public class LogSource
{
    /// <summary>
    /// 源文件路径
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// 行号
    /// </summary>
    public int? LineNumber { get; set; }

    /// <summary>
    /// 方法名
    /// </summary>
    public string? MethodName { get; set; }

    /// <summary>
    /// 类名
    /// </summary>
    public string? ClassName { get; set; }

    /// <summary>
    /// 命名空间
    /// </summary>
    public string? Namespace { get; set; }

    /// <summary>
    /// 创建日志来源信息的副本
    /// </summary>
    /// <returns>日志来源信息副本</returns>
    public LogSource Clone()
    {
        return new LogSource
        {
            FilePath = FilePath,
            LineNumber = LineNumber,
            MethodName = MethodName,
            ClassName = ClassName,
            Namespace = Namespace
        };
    }
}
