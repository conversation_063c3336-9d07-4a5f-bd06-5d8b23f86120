namespace Liam.SerialPort.Exceptions;

/// <summary>
/// 串口异常基类
/// </summary>
public class SerialPortException : Exception
{
    /// <summary>
    /// 串口名称
    /// </summary>
    public string? PortName { get; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortException()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    public SerialPortException(string message) : base(message)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="message">错误消息</param>
    public SerialPortException(string portName, string message) : base(message)
    {
        PortName = portName;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortException(string portName, string message, Exception innerException) : base(message, innerException)
    {
        PortName = portName;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="message">错误消息</param>
    public SerialPortException(string portName, string errorCode, string message) : base(message)
    {
        PortName = portName;
        ErrorCode = errorCode;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortException(string portName, string errorCode, string message, Exception innerException) : base(message, innerException)
    {
        PortName = portName;
        ErrorCode = errorCode;
    }
}

/// <summary>
/// 串口连接异常
/// </summary>
public class SerialPortConnectionException : SerialPortException
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortConnectionException()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    public SerialPortConnectionException(string message) : base(message)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortConnectionException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="message">错误消息</param>
    public SerialPortConnectionException(string portName, string message) : base(portName, message)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortConnectionException(string portName, string message, Exception innerException) : base(portName, message, innerException)
    {
    }
}

/// <summary>
/// 串口超时异常
/// </summary>
public class SerialPortTimeoutException : SerialPortException
{
    /// <summary>
    /// 超时时间
    /// </summary>
    public TimeSpan Timeout { get; }

    /// <summary>
    /// 操作类型
    /// </summary>
    public string OperationType { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="operationType">操作类型</param>
    public SerialPortTimeoutException(TimeSpan timeout, string operationType) 
        : base($"{operationType}操作超时，超时时间：{timeout}")
    {
        Timeout = timeout;
        OperationType = operationType;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="operationType">操作类型</param>
    public SerialPortTimeoutException(string portName, TimeSpan timeout, string operationType) 
        : base(portName, $"串口{portName}的{operationType}操作超时，超时时间：{timeout}")
    {
        Timeout = timeout;
        OperationType = operationType;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="operationType">操作类型</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortTimeoutException(string portName, TimeSpan timeout, string operationType, Exception innerException) 
        : base(portName, $"串口{portName}的{operationType}操作超时，超时时间：{timeout}", innerException)
    {
        Timeout = timeout;
        OperationType = operationType;
    }
}

/// <summary>
/// 串口配置异常
/// </summary>
public class SerialPortConfigurationException : SerialPortException
{
    /// <summary>
    /// 配置参数名称
    /// </summary>
    public string? ParameterName { get; }

    /// <summary>
    /// 配置参数值
    /// </summary>
    public object? ParameterValue { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    public SerialPortConfigurationException(string message) : base(message)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="parameterName">配置参数名称</param>
    /// <param name="parameterValue">配置参数值</param>
    /// <param name="message">错误消息</param>
    public SerialPortConfigurationException(string parameterName, object? parameterValue, string message) : base(message)
    {
        ParameterName = parameterName;
        ParameterValue = parameterValue;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="parameterName">配置参数名称</param>
    /// <param name="parameterValue">配置参数值</param>
    /// <param name="message">错误消息</param>
    public SerialPortConfigurationException(string portName, string parameterName, object? parameterValue, string message) : base(portName, message)
    {
        ParameterName = parameterName;
        ParameterValue = parameterValue;
    }
}

/// <summary>
/// 串口数据异常
/// </summary>
public class SerialPortDataException : SerialPortException
{
    /// <summary>
    /// 数据长度
    /// </summary>
    public int DataLength { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="dataLength">数据长度</param>
    public SerialPortDataException(string message, int dataLength) : base(message)
    {
        DataLength = dataLength;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="dataLength">数据长度</param>
    public SerialPortDataException(string portName, string message, int dataLength) : base(portName, message)
    {
        DataLength = dataLength;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="dataLength">数据长度</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortDataException(string portName, string message, int dataLength, Exception innerException) : base(portName, message, innerException)
    {
        DataLength = dataLength;
    }
}
