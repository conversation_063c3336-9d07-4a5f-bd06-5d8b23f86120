using System.Collections.Concurrent;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Xunit.Abstractions;
using Liam.TcpClient.Extensions;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Liam.TcpClient.Performance;

namespace Liam.TcpClient.Tests.Performance;

/// <summary>
/// 并发压力测试
/// </summary>
public class ConcurrencyStressTests
{
    private readonly ITestOutputHelper _output;
    private readonly Mock<ITcpClient> _mockClient;

    public ConcurrencyStressTests(ITestOutputHelper output)
    {
        _output = output;
        _mockClient = new Mock<ITcpClient>();
    }

    [Fact]
    public async Task ConnectionPool_ShouldHandleHighConcurrency()
    {
        // Arrange
        const int concurrentConnections = 100;
        const int operationsPerConnection = 10;
        var successfulOperations = new ConcurrentBag<bool>();
        var exceptions = new ConcurrentBag<Exception>();

        _mockClient.Setup(c => c.SendTextAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .Returns(async (string text, CancellationToken ct) =>
                   {
                       await Task.Delay(Random.Shared.Next(1, 10), ct); // 模拟网络延迟
                       return true;
                   });

        // Act
        var tasks = Enumerable.Range(0, concurrentConnections)
            .Select(async connectionId =>
            {
                try
                {
                    for (int i = 0; i < operationsPerConnection; i++)
                    {
                        var result = await _mockClient.Object.SendTextAsync($"Connection-{connectionId}-Message-{i}");
                        successfulOperations.Add(result);
                    }
                }
                catch (Exception ex)
                {
                    exceptions.Add(ex);
                }
            });

        await Task.WhenAll(tasks);

        // Assert
        successfulOperations.Count.Should().Be(concurrentConnections * operationsPerConnection);
        exceptions.Should().BeEmpty("不应该有异常发生");
        
        _output.WriteLine($"成功处理 {successfulOperations.Count} 个并发操作");
    }

    [Fact]
    public async Task BatchOperations_ShouldHandleLargeVolume()
    {
        // Arrange
        const int messageCount = 1000;
        const int maxConcurrency = 20;
        var processedMessages = new ConcurrentBag<int>();

        _mockClient.Setup(c => c.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                   .Returns(async (TcpMessage msg, CancellationToken ct) =>
                   {
                       await Task.Delay(1, ct); // 最小延迟
                       processedMessages.Add(1);
                       return true;
                   });

        var messages = Enumerable.Range(1, messageCount)
            .Select(i => TcpMessage.CreateTextMessage($"Batch message {i}"))
            .ToArray();

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await _mockClient.Object.SendBatchAsync(messages, maxConcurrency);
        stopwatch.Stop();

        // Assert
        result.Should().Be(messageCount);
        processedMessages.Count.Should().Be(messageCount);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000, "批量操作应该在5秒内完成");
        
        _output.WriteLine($"批量发送 {messageCount} 条消息耗时: {stopwatch.ElapsedMilliseconds}ms");
        _output.WriteLine($"平均每条消息: {(double)stopwatch.ElapsedMilliseconds / messageCount:F2}ms");
    }

    [Fact]
    public async Task MemoryOptimizer_ShouldHandleHighVolumeBufferOperations()
    {
        // Arrange
        const int operationCount = 10000;
        const int bufferSize = 4096;
        var testData = new byte[bufferSize];
        Random.Shared.NextBytes(testData);

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        var tasks = Enumerable.Range(0, operationCount)
            .Select(async i =>
            {
                using var buffer = MemoryOptimizer.RentByteBuffer(bufferSize);
                MemoryOptimizer.CopyMemory(testData, buffer.Span);
                
                // 模拟一些处理
                await Task.Yield();
                
                return buffer.Length;
            });

        var results = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        results.Should().AllSatisfy(length => length.Should().BeGreaterThanOrEqualTo(bufferSize));
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(2000, "高频缓冲区操作应该在2秒内完成");
        
        _output.WriteLine($"完成 {operationCount} 次缓冲区操作耗时: {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task StringOptimizer_ShouldHandleHighVolumeConcurrentAccess()
    {
        // Arrange
        const int concurrentTasks = 50;
        const int operationsPerTask = 100;
        var results = new ConcurrentBag<string>();

        // Act
        var tasks = Enumerable.Range(0, concurrentTasks)
            .Select(async taskId =>
            {
                for (int i = 0; i < operationsPerTask; i++)
                {
                    var result = StringOptimizer.BuildString(sb =>
                    {
                        sb.Append("Task-").Append(taskId)
                          .Append("-Operation-").Append(i)
                          .Append("-Timestamp-").Append(DateTime.UtcNow.Ticks);
                    });
                    
                    results.Add(result);
                    await Task.Yield(); // 让出控制权
                }
            });

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        results.Count.Should().Be(concurrentTasks * operationsPerTask);
        results.Should().OnlyHaveUniqueItems("每个字符串都应该是唯一的");
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(3000, "并发字符串操作应该在3秒内完成");
        
        _output.WriteLine($"并发字符串操作 {results.Count} 次耗时: {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task ConnectionManager_ShouldHandleRapidConnectDisconnect()
    {
        // Arrange
        const int cycles = 100;
        var connectionStates = new ConcurrentBag<string>();

        _mockClient.Setup(c => c.ConnectAsync(It.IsAny<CancellationToken>()))
                   .Returns(async (CancellationToken ct) =>
                   {
                       await Task.Delay(Random.Shared.Next(1, 5), ct);
                       connectionStates.Add("Connected");
                       return true;
                   });

        _mockClient.Setup(c => c.DisconnectAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .Returns(async (string reason, CancellationToken ct) =>
                   {
                       await Task.Delay(Random.Shared.Next(1, 3), ct);
                       connectionStates.Add("Disconnected");
                   });

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        for (int i = 0; i < cycles; i++)
        {
            await _mockClient.Object.ConnectAsync();
            await _mockClient.Object.DisconnectAsync($"Cycle {i}");
        }
        
        stopwatch.Stop();

        // Assert
        connectionStates.Count.Should().Be(cycles * 2); // 每个周期一次连接和断开
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(2000, "快速连接/断开应该在2秒内完成");
        
        _output.WriteLine($"完成 {cycles} 个连接/断开周期耗时: {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task ErrorHandling_ShouldBeThreadSafe()
    {
        // Arrange
        const int concurrentOperations = 100;
        var errors = new ConcurrentBag<Exception>();
        var successes = new ConcurrentBag<bool>();

        _mockClient.Setup(c => c.SendTextAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .Returns(async (string text, CancellationToken ct) =>
                   {
                       await Task.Delay(1, ct);
                       
                       // 随机产生一些错误
                       if (Random.Shared.Next(0, 10) < 2) // 20% 错误率
                       {
                           throw new InvalidOperationException($"模拟错误: {text}");
                       }
                       
                       return true;
                   });

        // Act
        var tasks = Enumerable.Range(0, concurrentOperations)
            .Select(async i =>
            {
                try
                {
                    var result = await _mockClient.Object.SendTextAsync($"Message {i}");
                    successes.Add(result);
                }
                catch (Exception ex)
                {
                    errors.Add(ex);
                }
            });

        await Task.WhenAll(tasks);

        // Assert
        (successes.Count + errors.Count).Should().Be(concurrentOperations);
        errors.Should().NotBeEmpty("应该有一些模拟的错误");
        successes.Should().NotBeEmpty("应该有一些成功的操作");
        
        _output.WriteLine($"成功操作: {successes.Count}, 错误: {errors.Count}");
    }

    [Fact]
    public async Task ResourceCleanup_ShouldBeProperlyHandled()
    {
        // Arrange
        const int resourceCount = 1000;
        var disposedResources = new ConcurrentBag<int>();

        // Act
        var tasks = Enumerable.Range(0, resourceCount)
            .Select(async i =>
            {
                using var buffer = MemoryOptimizer.RentByteBuffer(1024);
                await Task.Delay(1); // 模拟一些工作
                
                // 资源会在using块结束时自动释放
                disposedResources.Add(i);
            });

        await Task.WhenAll(tasks);

        // 强制垃圾回收以确保资源被清理
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        // Assert
        disposedResources.Count.Should().Be(resourceCount);
        
        _output.WriteLine($"成功创建和清理 {resourceCount} 个资源");
    }

    [Theory]
    [InlineData(10, 100)]   // 低并发，高频次
    [InlineData(100, 10)]   // 高并发，低频次
    [InlineData(50, 50)]    // 中等并发和频次
    public async Task ScalabilityTest_ShouldHandleDifferentLoadPatterns(int concurrency, int operationsPerTask)
    {
        // Arrange
        var completedOperations = new ConcurrentBag<int>();

        _mockClient.Setup(c => c.SendTextAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .Returns(async (string text, CancellationToken ct) =>
                   {
                       await Task.Delay(Random.Shared.Next(1, 3), ct);
                       return true;
                   });

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        var tasks = Enumerable.Range(0, concurrency)
            .Select(async taskId =>
            {
                for (int i = 0; i < operationsPerTask; i++)
                {
                    await _mockClient.Object.SendTextAsync($"Task-{taskId}-Op-{i}");
                    completedOperations.Add(1);
                }
            });

        await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        var totalOperations = concurrency * operationsPerTask;
        completedOperations.Count.Should().Be(totalOperations);
        
        var throughput = totalOperations / Math.Max(stopwatch.Elapsed.TotalSeconds, 0.001);
        throughput.Should().BeGreaterThan(100, "吞吐量应该超过100操作/秒");
        
        _output.WriteLine($"并发度: {concurrency}, 每任务操作数: {operationsPerTask}");
        _output.WriteLine($"总操作数: {totalOperations}, 耗时: {stopwatch.ElapsedMilliseconds}ms");
        _output.WriteLine($"吞吐量: {throughput:F2} 操作/秒");
    }
}
