# Liam.TcpServer 示例程序

本目录包含了 Liam.TcpServer 库的示例程序，演示如何使用该库创建TCP服务器和客户端应用程序。

## 示例项目

### 1. TcpServerExample - TCP服务器示例

这是一个完整的TCP服务器示例，展示了Liam.TcpServer的主要功能：

**功能特性：**
- TCP服务器启动和停止
- 客户端连接管理
- 消息接收和发送
- 心跳检测
- 事件处理
- 控制台命令交互
- 统计信息显示

**运行方法：**
```bash
# 构建项目
dotnet build examples/TcpServerExample/TcpServerExample.csproj

# 运行服务器
dotnet run --project examples/TcpServerExample/TcpServerExample.csproj
```

**服务器控制台命令：**
- `/help` - 显示帮助信息
- `/stats` - 显示服务器统计信息
- `/clients` - 显示连接的客户端
- `/broadcast <message>` - 广播消息到所有客户端
- `/quit` - 停止服务器

**客户端命令（通过TCP连接发送）：**
- `/help` - 显示客户端帮助信息
- `/time` - 获取服务器时间
- `/stats` - 获取连接统计信息
- `/echo <message>` - 回显消息
- `/quit` - 断开连接

### 2. TcpClientTest - TCP客户端测试工具

这是一个简单的TCP客户端测试工具，用于连接到TCP服务器进行测试：

**功能特性：**
- 连接到指定的TCP服务器
- 发送和接收消息
- 支持命令行参数指定服务器地址和端口
- 实时显示服务器响应

**运行方法：**
```bash
# 构建项目
dotnet build examples/TcpClientTest/TcpClientTest.csproj

# 连接到本地服务器（默认localhost:8888）
dotnet run --project examples/TcpClientTest/TcpClientTest.csproj

# 连接到指定服务器
dotnet run --project examples/TcpClientTest/TcpClientTest.csproj -- <host> <port>
```

**使用示例：**
```bash
# 连接到本地服务器
dotnet run --project examples/TcpClientTest/TcpClientTest.csproj

# 连接到远程服务器
dotnet run --project examples/TcpClientTest/TcpClientTest.csproj -- ************* 8080
```

## 完整测试流程

### 步骤1：启动服务器
```bash
# 在第一个终端窗口中启动服务器
dotnet run --project examples/TcpServerExample/TcpServerExample.csproj
```

服务器启动后会显示：
```
=== Liam.TcpServer 示例程序 ===

正在启动TCP服务器...
TCP服务器已启动，监听端口: 8888
可用命令:
  /help     - 显示帮助信息
  /stats    - 显示服务器统计信息
  /clients  - 显示连接的客户端
  /broadcast <message> - 广播消息到所有客户端
  /quit     - 停止服务器

等待客户端连接...
提示: 可以使用telnet命令连接测试: telnet localhost 8888
```

### 步骤2：连接客户端
```bash
# 在第二个终端窗口中启动客户端
dotnet run --project examples/TcpClientTest/TcpClientTest.csproj
```

客户端连接后会显示：
```
=== Liam.TcpServer 客户端测试工具 ===

连接到服务器: localhost:8888
输入消息发送到服务器，输入 'quit' 退出

已连接到服务器！
可用命令:
  /help     - 显示服务器帮助信息
  /time     - 获取服务器时间
  /stats    - 获取连接统计信息
  /echo <message> - 回显消息
  /quit     - 断开连接
  quit      - 退出客户端

[服务器] 欢迎连接到Liam.TcpServer示例服务器！
> 
```

### 步骤3：测试交互

**在客户端中输入：**
```
> Hello Server!
[服务器] Echo: Hello Server!

> /time
[服务器] 服务器时间: 2025-06-15 14:30:25

> /stats
[服务器] 连接统计信息:
连接ID: a1b2c3d4
客户端地址: 127.0.0.1:54321
连接时间: 2025-06-15 14:30:20
连接持续时间: 00:00:05
发送字节数: 45
接收字节数: 23
发送消息数: 3
接收消息数: 2
```

**在服务器控制台中输入：**
```
> /stats
=== 服务器统计信息 ===
当前连接数: 1
总连接数: 1
最大并发连接数: 1
总发送字节数: 156
总接收字节数: 68
总发送消息数: 4
总接收消息数: 3
总错误数: 0
运行时间: 00.00:02:15
错误率: 0.00%

> /broadcast Hello Everyone!
消息已广播到 1 个客户端
```

### 步骤4：使用telnet测试

你也可以使用系统自带的telnet命令进行测试：

```bash
# Windows
telnet localhost 8080

# Linux/macOS
telnet localhost 8080
```

连接后可以直接输入文本消息，服务器会回显。

## 高级功能演示

### SSL/TLS 安全连接

要测试SSL功能，需要先生成SSL证书：

```bash
# 生成自签名证书（仅用于测试）
openssl req -x509 -newkey rsa:4096 -keyout server.key -out server.crt -days 365 -nodes
openssl pkcs12 -export -out server.pfx -inkey server.key -in server.crt
```

然后修改服务器代码以启用SSL：

```csharp
// 在TcpServerExample/Program.cs中修改配置
services.AddSslTcpServer(certificate, 8443);
```

### 心跳检测

示例程序已启用心跳检测，你可以观察到：
- 每30秒发送一次心跳
- 10秒内未响应则断开连接
- 控制台会显示心跳事件

### 性能测试

可以同时运行多个客户端实例来测试服务器的并发处理能力：

```bash
# 启动多个客户端（在不同终端中）
dotnet run --project examples/TcpClientTest/TcpClientTest.csproj &
dotnet run --project examples/TcpClientTest/TcpClientTest.csproj &
dotnet run --project examples/TcpClientTest/TcpClientTest.csproj &
```

然后在服务器控制台中使用 `/clients` 和 `/stats` 命令查看连接状态。

## 故障排除

### 常见问题

1. **端口被占用**
   ```
   错误: 启动服务器时发生错误: Only one usage of each socket address is normally permitted
   ```
   解决方案：更改端口号或关闭占用端口的程序

2. **连接被拒绝**
   ```
   错误: 连接失败: No connection could be made because the target machine actively refused it
   ```
   解决方案：确保服务器已启动且端口正确

3. **防火墙阻止**
   - Windows：在Windows防火墙中允许应用程序
   - Linux：使用 `sudo ufw allow 8080` 开放端口

### 调试技巧

1. **启用详细日志**：在服务器配置中设置日志级别为Debug
2. **网络抓包**：使用Wireshark等工具分析网络流量
3. **性能监控**：观察CPU和内存使用情况

## 扩展示例

基于这些示例，你可以进一步开发：

1. **聊天服务器**：实现多用户聊天功能
2. **文件传输服务器**：支持文件上传下载
3. **游戏服务器**：实现实时游戏通信
4. **IoT数据收集器**：收集设备数据
5. **API网关**：实现TCP到HTTP的协议转换

## 相关文档

- [Liam.TcpServer API文档](../src/Liam.TcpServer/README.md)
- [Liam.Logging 集成指南](../src/Liam.Logging/README.md)
- [.NET Socket编程指南](https://docs.microsoft.com/en-us/dotnet/api/system.net.sockets)
