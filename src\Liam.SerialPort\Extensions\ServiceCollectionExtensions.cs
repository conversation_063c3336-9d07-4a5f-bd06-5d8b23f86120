using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Services;

namespace Liam.SerialPort.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加串口服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSerialPort(this IServiceCollection services)
    {
        // 注册核心服务
        services.TryAddSingleton<ISerialPortDiscovery, SerialPortDiscovery>();
        services.TryAddTransient<ISerialPortConnection, SerialPortConnection>();
        services.TryAddTransient<ISerialPortDataHandler, SerialPortDataHandler>();
        services.TryAddTransient<ISerialPortService, SerialPortService>();

        return services;
    }

    /// <summary>
    /// 添加串口服务（单例模式）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSerialPortSingleton(this IServiceCollection services)
    {
        // 注册核心服务为单例
        services.TryAddSingleton<ISerialPortDiscovery, SerialPortDiscovery>();
        services.TryAddSingleton<ISerialPortConnection, SerialPortConnection>();
        services.TryAddSingleton<ISerialPortDataHandler, SerialPortDataHandler>();
        services.TryAddSingleton<ISerialPortService, SerialPortService>();

        return services;
    }

    /// <summary>
    /// 添加串口服务（作用域模式）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSerialPortScoped(this IServiceCollection services)
    {
        // 注册核心服务为作用域
        services.TryAddSingleton<ISerialPortDiscovery, SerialPortDiscovery>();
        services.TryAddScoped<ISerialPortConnection, SerialPortConnection>();
        services.TryAddScoped<ISerialPortDataHandler, SerialPortDataHandler>();
        services.TryAddScoped<ISerialPortService, SerialPortService>();

        return services;
    }

    /// <summary>
    /// 添加串口服务工厂
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSerialPortFactory(this IServiceCollection services)
    {
        // 注册发现服务为单例（全局共享）
        services.TryAddSingleton<ISerialPortDiscovery, SerialPortDiscovery>();

        // 注册工厂方法
        services.TryAddTransient<Func<ISerialPortService>>(provider => () =>
        {
            var logger = provider.GetRequiredService<ILogger<SerialPortService>>();
            var discovery = provider.GetRequiredService<ISerialPortDiscovery>();
            
            // 为每个实例创建独立的连接和数据处理器
            var connectionLogger = provider.GetRequiredService<ILogger<SerialPortConnection>>();
            var dataHandlerLogger = provider.GetRequiredService<ILogger<SerialPortDataHandler>>();
            
            var connection = new SerialPortConnection(connectionLogger);
            var dataHandler = new SerialPortDataHandler(dataHandlerLogger);
            
            return new SerialPortService(logger, discovery, connection, dataHandler);
        });

        return services;
    }

    /// <summary>
    /// 添加串口服务池
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="poolSize">池大小</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSerialPortPool(this IServiceCollection services, int poolSize = 10)
    {
        if (poolSize <= 0)
            throw new ArgumentException("池大小必须大于0", nameof(poolSize));

        // 注册发现服务为单例
        services.TryAddSingleton<ISerialPortDiscovery, SerialPortDiscovery>();

        // 注册串口服务池
        services.TryAddSingleton<ISerialPortServicePool>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<SerialPortServicePool>>();
            var discovery = provider.GetRequiredService<ISerialPortDiscovery>();
            
            return new SerialPortServicePool(logger, discovery, poolSize, provider);
        });

        return services;
    }

    /// <summary>
    /// 配置串口服务日志
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureLogging">日志配置委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ConfigureSerialPortLogging(this IServiceCollection services, Action<ILoggingBuilder> configureLogging)
    {
        // 注意：需要引用Microsoft.Extensions.Logging包才能使用AddLogging扩展方法
        // 这里提供基础的日志服务注册
        services.TryAddSingleton<ILoggerFactory, LoggerFactory>();
        services.TryAddTransient(typeof(ILogger<>), typeof(Logger<>));
        return services;
    }
}

/// <summary>
/// 串口服务池接口
/// </summary>
public interface ISerialPortServicePool : IDisposable
{
    /// <summary>
    /// 获取可用的串口服务
    /// </summary>
    /// <returns>串口服务</returns>
    Task<ISerialPortService> GetServiceAsync();

    /// <summary>
    /// 归还串口服务
    /// </summary>
    /// <param name="service">串口服务</param>
    Task ReturnServiceAsync(ISerialPortService service);

    /// <summary>
    /// 获取池状态
    /// </summary>
    /// <returns>池状态</returns>
    SerialPortPoolStatus GetStatus();
}

/// <summary>
/// 串口服务池实现
/// </summary>
public class SerialPortServicePool : ISerialPortServicePool
{
    private readonly ILogger<SerialPortServicePool> _logger;
    private readonly ISerialPortDiscovery _discovery;
    private readonly IServiceProvider _serviceProvider;
    private readonly Queue<ISerialPortService> _availableServices = new();
    private readonly HashSet<ISerialPortService> _usedServices = new();
    private readonly object _lock = new();
    private readonly int _maxSize;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="discovery">设备发现服务</param>
    /// <param name="maxSize">最大池大小</param>
    /// <param name="serviceProvider">服务提供者</param>
    public SerialPortServicePool(ILogger<SerialPortServicePool> logger, ISerialPortDiscovery discovery, int maxSize, IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _discovery = discovery ?? throw new ArgumentNullException(nameof(discovery));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _maxSize = maxSize;

        _logger.LogInformation("串口服务池已初始化，最大大小：{MaxSize}", _maxSize);
    }

    /// <summary>
    /// 获取可用的串口服务
    /// </summary>
    /// <returns>串口服务</returns>
    public async Task<ISerialPortService> GetServiceAsync()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SerialPortServicePool));

        lock (_lock)
        {
            // 尝试从池中获取可用服务
            if (_availableServices.Count > 0)
            {
                var service = _availableServices.Dequeue();
                _usedServices.Add(service);
                _logger.LogDebug("从池中获取串口服务，剩余可用：{Available}，正在使用：{Used}", _availableServices.Count, _usedServices.Count);
                return service;
            }

            // 如果池未满，创建新服务
            if (_usedServices.Count < _maxSize)
            {
                var service = CreateNewService();
                _usedServices.Add(service);
                _logger.LogDebug("创建新的串口服务，正在使用：{Used}/{MaxSize}", _usedServices.Count, _maxSize);
                return service;
            }
        }

        // 池已满，等待可用服务
        _logger.LogWarning("串口服务池已满，等待可用服务");
        
        while (!_disposed)
        {
            await Task.Delay(100);
            
            lock (_lock)
            {
                if (_availableServices.Count > 0)
                {
                    var service = _availableServices.Dequeue();
                    _usedServices.Add(service);
                    _logger.LogDebug("等待后获取到串口服务");
                    return service;
                }
            }
        }

        throw new ObjectDisposedException(nameof(SerialPortServicePool));
    }

    /// <summary>
    /// 归还串口服务
    /// </summary>
    /// <param name="service">串口服务</param>
    public async Task ReturnServiceAsync(ISerialPortService service)
    {
        if (service == null)
            return;

        if (_disposed)
        {
            service.Dispose();
            return;
        }

        lock (_lock)
        {
            if (_usedServices.Remove(service))
            {
                // 断开连接并重置服务状态
                _ = Task.Run(async () =>
                {
                    try
                    {
                        if (service.IsConnected)
                        {
                            await service.DisconnectAsync();
                        }
                        service.ClearReceiveBuffer();
                        service.ClearSendBuffer();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "重置串口服务状态时发生错误");
                    }
                });

                _availableServices.Enqueue(service);
                _logger.LogDebug("归还串口服务到池，可用：{Available}，正在使用：{Used}", _availableServices.Count, _usedServices.Count);
            }
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取池状态
    /// </summary>
    /// <returns>池状态</returns>
    public SerialPortPoolStatus GetStatus()
    {
        lock (_lock)
        {
            return new SerialPortPoolStatus
            {
                MaxSize = _maxSize,
                AvailableCount = _availableServices.Count,
                UsedCount = _usedServices.Count,
                TotalCount = _availableServices.Count + _usedServices.Count
            };
        }
    }

    /// <summary>
    /// 创建新的串口服务
    /// </summary>
    /// <returns>串口服务</returns>
    private ISerialPortService CreateNewService()
    {
        var logger = _serviceProvider.GetRequiredService<ILogger<SerialPortService>>();
        var connectionLogger = _serviceProvider.GetRequiredService<ILogger<SerialPortConnection>>();
        var dataHandlerLogger = _serviceProvider.GetRequiredService<ILogger<SerialPortDataHandler>>();

        var connection = new SerialPortConnection(connectionLogger);
        var dataHandler = new SerialPortDataHandler(dataHandlerLogger);

        return new SerialPortService(logger, _discovery, connection, dataHandler);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        lock (_lock)
        {
            // 释放所有服务
            while (_availableServices.Count > 0)
            {
                var service = _availableServices.Dequeue();
                try
                {
                    service.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放池中串口服务时发生错误");
                }
            }

            foreach (var service in _usedServices)
            {
                try
                {
                    service.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放使用中串口服务时发生错误");
                }
            }

            _usedServices.Clear();
        }

        _logger.LogInformation("串口服务池已释放");
    }
}

/// <summary>
/// 串口服务池状态
/// </summary>
public class SerialPortPoolStatus
{
    /// <summary>
    /// 最大大小
    /// </summary>
    public int MaxSize { get; set; }

    /// <summary>
    /// 可用数量
    /// </summary>
    public int AvailableCount { get; set; }

    /// <summary>
    /// 使用中数量
    /// </summary>
    public int UsedCount { get; set; }

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 使用率
    /// </summary>
    public double UsageRate => MaxSize > 0 ? (double)UsedCount / MaxSize : 0;

    /// <summary>
    /// 是否已满
    /// </summary>
    public bool IsFull => UsedCount >= MaxSize;
}
