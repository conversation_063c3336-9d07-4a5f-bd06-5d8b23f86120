using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Liam.SerialPort.Extensions;
using Liam.SerialPort.Interfaces;
using Liam.SerialPort.Models;

namespace Liam.SerialPort.Examples;

/// <summary>
/// 串口与Liam.Logging集成使用示例
/// </summary>
public class SerialPortWithLoggingExample
{
    public static async Task Main(string[] args)
    {
        // 创建主机构建器
        var builder = Host.CreateApplicationBuilder(args);

        // 方案一：使用Liam.Logging增强日志功能
        builder.Services.AddSerialPortWithLiamLogging(options =>
        {
            options.MinLogLevel = LogLevel.Debug;
            options.EnableConsoleLogging = true;
            options.EnableFileLogging = true;
            options.LogFilePath = "logs/serialport-{Date}.log";
            options.EnableStructuredLogging = true;
            options.EnableAsyncLogging = true;
            options.EnableLogRotation = true;
            options.MaxFileSize = 5 * 1024 * 1024; // 5MB
            options.MaxFiles = 10;
        });

        // 方案二：使用标准日志（如果不需要Liam.Logging）
        // builder.Services.AddSerialPort();
        // builder.Services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));

        var host = builder.Build();

        // 获取串口服务
        var serialPortService = host.Services.GetRequiredService<ISerialPortService>();
        var logger = host.Services.GetRequiredService<ILogger<SerialPortWithLoggingExample>>();

        try
        {
            // 检查Liam.Logging是否可用
            if (LiamLoggingExtensions.IsLiamLoggingAvailable())
            {
                logger.LogInformation("使用Liam.Logging增强日志功能");
            }
            else
            {
                logger.LogInformation("使用标准Microsoft.Extensions.Logging");
            }

            // 获取可用串口
            logger.LogInformation("正在扫描可用串口...");
            var ports = await serialPortService.GetAvailablePortsAsync();
            
            if (!ports.Any())
            {
                logger.LogWarning("未找到可用的串口设备");
                return;
            }

            // 显示可用串口
            foreach (var port in ports)
            {
                logger.LogInformation("发现串口: {PortName} - {Description}", 
                    port.PortName, port.Description);
            }

            // 选择第一个串口进行连接
            var targetPort = ports.First();
            logger.LogInformation("准备连接到串口: {PortName}", targetPort.PortName);

            // 配置串口参数
            var settings = new SerialPortSettings
            {
                BaudRate = 9600,
                DataBits = 8,
                StopBits = System.IO.Ports.StopBits.One,
                Parity = System.IO.Ports.Parity.None,
                AutoReconnect = true,
                HeartbeatInterval = 5000 // 5秒心跳
            };

            // 订阅事件
            serialPortService.StatusChanged += (sender, e) =>
            {
                logger.LogInformation("串口状态变化: {PortName} {OldStatus} -> {NewStatus} ({Reason})",
                    e.PortName, e.OldStatus, e.NewStatus, e.Reason);
            };

            serialPortService.DataReceived += (sender, e) =>
            {
                logger.LogDebug("接收到数据: {PortName} 长度={Length} 数据={Data}",
                    e.PortName, e.Data.Length, Convert.ToHexString(e.Data));
            };

            serialPortService.ErrorOccurred += (sender, e) =>
            {
                logger.LogError("串口错误: {PortName} {ErrorType} - {Message}",
                    e.PortName, e.ErrorType, e.Message);
            };

            // 连接串口
            var connected = await serialPortService.ConnectAsync(targetPort, settings);
            
            if (connected)
            {
                logger.LogInformation("串口连接成功，开始数据交互...");

                // 发送测试数据
                var testData = "Hello SerialPort!"u8.ToArray();
                await serialPortService.SendAsync(testData);
                logger.LogInformation("发送测试数据: {Data}", System.Text.Encoding.UTF8.GetString(testData));

                // 等待一段时间接收数据
                await Task.Delay(5000);

                // 发送十六进制数据
                await serialPortService.SendHexAsync("48656C6C6F20486578");
                logger.LogInformation("发送十六进制数据: 48656C6C6F20486578");

                // 再等待一段时间
                await Task.Delay(5000);

                // 断开连接
                await serialPortService.DisconnectAsync();
                logger.LogInformation("串口连接已断开");
            }
            else
            {
                logger.LogError("串口连接失败");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "示例执行过程中发生错误");
        }
        finally
        {
            // 清理资源
            serialPortService.Dispose();
            logger.LogInformation("示例执行完成");
        }
    }
}

/// <summary>
/// 高级日志配置示例
/// </summary>
public class AdvancedLoggingExample
{
    public static void ConfigureAdvancedLogging(IServiceCollection services)
    {
        // 高级日志配置
        services.AddSerialPortWithLiamLogging(options =>
        {
            // 基础配置
            options.MinLogLevel = LogLevel.Trace;
            options.EnableStructuredLogging = true;
            options.EnableAsyncLogging = true;

            // 控制台日志
            options.EnableConsoleLogging = true;

            // 文件日志配置
            options.EnableFileLogging = true;
            options.LogFilePath = "logs/serialport-{Date}.log";
            options.EnableLogRotation = true;
            options.MaxFileSize = 10 * 1024 * 1024; // 10MB
            options.MaxFiles = 30; // 保留30个文件
        });

        // 也可以直接配置Liam.Logging（如果需要更细粒度的控制）
        #if LIAM_LOGGING_AVAILABLE
        services.AddLiamLogging(config =>
        {
            config.MinLevel = LogLevel.Debug;
            config.EnableStructuredLogging = true;
            config.EnableAsyncLogging = true;
            config.EnablePerformanceMonitoring = true;

            // 添加多个输出目标
            config.AddConsoleProvider();
            config.AddFileProvider(fileConfig =>
            {
                fileConfig.FilePath = "logs/serialport-detailed-{Date}.log";
                fileConfig.MaxFileSize = 50 * 1024 * 1024; // 50MB
                fileConfig.MaxFiles = 10;
                fileConfig.EnableLogRotation = true;
            });

            // 可以添加数据库日志等其他提供程序
            // config.AddDatabaseProvider(...);
        });
        #endif
    }
}
