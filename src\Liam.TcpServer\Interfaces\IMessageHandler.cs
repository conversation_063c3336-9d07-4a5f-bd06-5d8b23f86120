using Liam.TcpServer.Models;

namespace Liam.TcpServer.Interfaces;

/// <summary>
/// 消息处理器接口
/// </summary>
public interface IMessageHandler
{
    /// <summary>
    /// 处理接收到的消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">接收到的消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandleMessageAsync(ClientConnection connection, TcpMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理连接建立
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandleConnectionAsync(ClientConnection connection, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理连接断开
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandleDisconnectionAsync(ClientConnection connection, string? reason = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理心跳消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="isRequest">是否为心跳请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandleHeartbeatAsync(ClientConnection connection, bool isRequest, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理错误
    /// </summary>
    /// <param name="connection">客户端连接（可选）</param>
    /// <param name="exception">异常信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandleErrorAsync(ClientConnection? connection, Exception exception, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">消息</param>
    /// <returns>验证结果</returns>
    MessageValidationResult ValidateMessage(ClientConnection connection, TcpMessage message);

    /// <summary>
    /// 处理认证消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">认证消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证结果</returns>
    Task<AuthenticationResult> HandleAuthenticationAsync(ClientConnection connection, TcpMessage message, CancellationToken cancellationToken = default);
}

/// <summary>
/// 消息验证结果
/// </summary>
public class MessageValidationResult
{
    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; }

    /// <summary>
    /// 验证错误消息
    /// </summary>
    public string? ErrorMessage { get; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; }

    /// <summary>
    /// 初始化消息验证结果
    /// </summary>
    /// <param name="isValid">是否验证通过</param>
    /// <param name="errorMessage">验证错误消息</param>
    /// <param name="errorCode">错误代码</param>
    public MessageValidationResult(bool isValid, string? errorMessage = null, string? errorCode = null)
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
        ErrorCode = errorCode;
    }

    /// <summary>
    /// 创建成功的验证结果
    /// </summary>
    /// <returns>成功的验证结果</returns>
    public static MessageValidationResult Success()
    {
        return new MessageValidationResult(true);
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <returns>失败的验证结果</returns>
    public static MessageValidationResult Failure(string errorMessage, string? errorCode = null)
    {
        return new MessageValidationResult(false, errorMessage, errorCode);
    }
}

/// <summary>
/// 认证结果
/// </summary>
public class AuthenticationResult
{
    /// <summary>
    /// 是否认证成功
    /// </summary>
    public bool IsSuccess { get; }

    /// <summary>
    /// 认证用户
    /// </summary>
    public string? User { get; }

    /// <summary>
    /// 认证错误消息
    /// </summary>
    public string? ErrorMessage { get; }

    /// <summary>
    /// 认证令牌
    /// </summary>
    public string? Token { get; }

    /// <summary>
    /// 认证过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; }

    /// <summary>
    /// 初始化认证结果
    /// </summary>
    /// <param name="isSuccess">是否认证成功</param>
    /// <param name="user">认证用户</param>
    /// <param name="errorMessage">认证错误消息</param>
    /// <param name="token">认证令牌</param>
    /// <param name="expiresAt">认证过期时间</param>
    public AuthenticationResult(bool isSuccess, string? user = null, string? errorMessage = null, string? token = null, DateTime? expiresAt = null)
    {
        IsSuccess = isSuccess;
        User = user;
        ErrorMessage = errorMessage;
        Token = token;
        ExpiresAt = expiresAt;
    }

    /// <summary>
    /// 创建成功的认证结果
    /// </summary>
    /// <param name="user">认证用户</param>
    /// <param name="token">认证令牌</param>
    /// <param name="expiresAt">认证过期时间</param>
    /// <returns>成功的认证结果</returns>
    public static AuthenticationResult Success(string user, string? token = null, DateTime? expiresAt = null)
    {
        return new AuthenticationResult(true, user, null, token, expiresAt);
    }

    /// <summary>
    /// 创建失败的认证结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>失败的认证结果</returns>
    public static AuthenticationResult Failure(string errorMessage)
    {
        return new AuthenticationResult(false, null, errorMessage);
    }
}

/// <summary>
/// 自定义消息处理器接口
/// </summary>
/// <typeparam name="T">消息类型</typeparam>
public interface ICustomMessageHandler<T> where T : class
{
    /// <summary>
    /// 支持的消息类型
    /// </summary>
    byte MessageType { get; }

    /// <summary>
    /// 处理自定义消息
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">自定义消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandleAsync(ClientConnection connection, T message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 反序列化消息
    /// </summary>
    /// <param name="data">消息数据</param>
    /// <returns>反序列化后的消息</returns>
    T? Deserialize(byte[] data);

    /// <summary>
    /// 序列化消息
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <returns>序列化后的数据</returns>
    byte[] Serialize(T message);
}

/// <summary>
/// 消息处理器注册表接口
/// </summary>
public interface IMessageHandlerRegistry
{
    /// <summary>
    /// 注册消息处理器
    /// </summary>
    /// <typeparam name="T">消息类型</typeparam>
    /// <param name="handler">消息处理器</param>
    void RegisterHandler<T>(ICustomMessageHandler<T> handler) where T : class;

    /// <summary>
    /// 注销消息处理器
    /// </summary>
    /// <param name="messageType">消息类型</param>
    void UnregisterHandler(byte messageType);

    /// <summary>
    /// 获取消息处理器
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <returns>消息处理器</returns>
    object? GetHandler(byte messageType);

    /// <summary>
    /// 检查是否有处理器
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <returns>是否有处理器</returns>
    bool HasHandler(byte messageType);

    /// <summary>
    /// 获取所有已注册的消息类型
    /// </summary>
    /// <returns>已注册的消息类型</returns>
    IReadOnlyList<byte> GetRegisteredMessageTypes();
}
