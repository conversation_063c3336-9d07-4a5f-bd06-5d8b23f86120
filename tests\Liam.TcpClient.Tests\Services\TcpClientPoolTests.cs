using FluentAssertions;
using Liam.TcpClient.Extensions;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Liam.TcpClient.Tests.Services;

/// <summary>
/// TCP客户端池测试
/// </summary>
public class TcpClientPoolTests : IDisposable
{
    private readonly Mock<ITcpClientFactory> _mockFactory;
    private readonly Mock<ILogger<TcpClientPool>> _mockLogger;
    private readonly TcpClientConfig _config;
    private readonly TcpClientPool _pool;

    public TcpClientPoolTests()
    {
        _mockFactory = new Mock<ITcpClientFactory>();
        _mockLogger = new Mock<ILogger<TcpClientPool>>();
        
        _config = new TcpClientConfig
        {
            Host = "localhost",
            Port = 8080,
            ConnectionPoolConfig = new ConnectionPoolConfig
            {
                Enabled = true,
                PoolSize = 5,
                IdleTimeoutSeconds = 300,
                MaxLifetimeSeconds = 3600
            }
        };

        _pool = new TcpClientPool(_mockFactory.Object, _config, _mockLogger.Object);
    }

    [Fact]
    public async Task GetClientAsync_ShouldCreateNewClient_WhenPoolIsEmpty()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(c => c.Configuration).Returns(_config);
        mockClient.Setup(c => c.IsConnected).Returns(true);
        
        _mockFactory.Setup(f => f.CreateClient(_config))
                   .Returns(mockClient.Object);

        // Act
        var client = await _pool.GetClientAsync();

        // Assert
        client.Should().NotBeNull();
        client.Should().Be(mockClient.Object);
        _mockFactory.Verify(f => f.CreateClient(_config), Times.Once);
    }

    [Fact]
    public async Task ReturnClientAsync_ShouldReturnClientToPool_WhenClientIsValid()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(c => c.Configuration).Returns(_config);
        mockClient.Setup(c => c.IsConnected).Returns(true);
        
        _mockFactory.Setup(f => f.CreateClient(_config))
                   .Returns(mockClient.Object);

        var client = await _pool.GetClientAsync();

        // Act
        await _pool.ReturnClientAsync(client);

        // Assert
        var statistics = _pool.GetStatistics();
        statistics.AvailableClients.Should().Be(1);
        statistics.TotalClients.Should().Be(1);
    }

    [Fact]
    public async Task GetClientAsync_ShouldReuseClient_WhenClientIsAvailableInPool()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(c => c.Configuration).Returns(_config);
        mockClient.Setup(c => c.IsConnected).Returns(true);
        
        _mockFactory.Setup(f => f.CreateClient(_config))
                   .Returns(mockClient.Object);

        var client1 = await _pool.GetClientAsync();
        await _pool.ReturnClientAsync(client1);

        // Act
        var client2 = await _pool.GetClientAsync();

        // Assert
        client2.Should().Be(client1);
        _mockFactory.Verify(f => f.CreateClient(_config), Times.Once); // 只创建一次
    }

    [Fact]
    public async Task GetClientAsync_ShouldRespectMaxPoolSize()
    {
        // Arrange
        var clients = new List<ITcpClient>();
        
        for (int i = 0; i < _config.ConnectionPoolConfig!.PoolSize; i++)
        {
            var mockClient = new Mock<ITcpClient>();
            mockClient.Setup(c => c.Configuration).Returns(_config);
            mockClient.Setup(c => c.IsConnected).Returns(true);
            
            _mockFactory.Setup(f => f.CreateClient(_config))
                       .Returns(mockClient.Object);

            var client = await _pool.GetClientAsync();
            clients.Add(client);
        }

        // Act & Assert
        var timeoutTask = _pool.GetClientAsync();
        var delayTask = Task.Delay(100);
        
        var completedTask = await Task.WhenAny(timeoutTask, delayTask);
        completedTask.Should().Be(delayTask); // 应该超时，因为池已满
    }

    [Fact]
    public void GetStatistics_ShouldReturnCorrectStatistics()
    {
        // Act
        var statistics = _pool.GetStatistics();

        // Assert
        statistics.Should().NotBeNull();
        statistics.TotalClients.Should().Be(0);
        statistics.AvailableClients.Should().Be(0);
        statistics.BusyClients.Should().Be(0);
        statistics.MaxPoolSize.Should().Be(_config.ConnectionPoolConfig!.PoolSize);
        statistics.UtilizationRate.Should().Be(0);
    }

    [Fact]
    public async Task ReturnClientAsync_ShouldDisposeDisconnectedClient()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(c => c.Configuration).Returns(_config);
        mockClient.Setup(c => c.IsConnected).Returns(false); // 模拟断开连接
        
        _mockFactory.Setup(f => f.CreateClient(_config))
                   .Returns(mockClient.Object);

        var client = await _pool.GetClientAsync();

        // Act
        await _pool.ReturnClientAsync(client);

        // Assert
        mockClient.Verify(c => c.Dispose(), Times.Once);
        var statistics = _pool.GetStatistics();
        statistics.AvailableClients.Should().Be(0); // 断开的客户端不应该回到池中
    }

    [Fact]
    public void Constructor_ShouldThrowArgumentNullException_WhenFactoryIsNull()
    {
        // Act & Assert
        var act = () => new TcpClientPool(null!, _config, _mockLogger.Object);
        act.Should().Throw<ArgumentNullException>().WithParameterName("clientFactory");
    }

    [Fact]
    public void Constructor_ShouldThrowArgumentNullException_WhenConfigurationIsNull()
    {
        // Act & Assert
        var act = () => new TcpClientPool(_mockFactory.Object, null!, _mockLogger.Object);
        act.Should().Throw<ArgumentNullException>().WithParameterName("configuration");
    }

    [Fact]
    public void Constructor_ShouldThrowArgumentNullException_WhenLoggerIsNull()
    {
        // Act & Assert
        var act = () => new TcpClientPool(_mockFactory.Object, _config, null!);
        act.Should().Throw<ArgumentNullException>().WithParameterName("logger");
    }

    [Fact]
    public async Task GetClientAsync_ShouldThrowObjectDisposedException_WhenPoolIsDisposed()
    {
        // Arrange
        _pool.Dispose();

        // Act & Assert
        var act = async () => await _pool.GetClientAsync();
        await act.Should().ThrowAsync<ObjectDisposedException>();
    }

    [Fact]
    public async Task ReturnClientAsync_ShouldDisposeClient_WhenPoolIsDisposed()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(c => c.Configuration).Returns(_config);
        
        _pool.Dispose();

        // Act
        await _pool.ReturnClientAsync(mockClient.Object);

        // Assert
        mockClient.Verify(c => c.Dispose(), Times.Once);
    }

    public void Dispose()
    {
        _pool?.Dispose();
    }
}
