using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using FluentAssertions;
using Liam.SerialPort.Extensions;
using Liam.SerialPort.Interfaces;

namespace Liam.SerialPort.Tests.Extensions;

/// <summary>
/// Liam.Logging集成扩展方法测试
/// </summary>
public class LiamLoggingExtensionsTests
{
    [Fact]
    public void AddSerialPortWithLiamLogging_ShouldRegisterServices()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddSerialPortWithLiamLogging();

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // 验证串口服务已注册
        var serialPortService = serviceProvider.GetService<ISerialPortService>();
        serialPortService.Should().NotBeNull();
        
        var discovery = serviceProvider.GetService<ISerialPortDiscovery>();
        discovery.Should().NotBeNull();
        
        var connection = serviceProvider.GetService<ISerialPortConnection>();
        connection.Should().NotBeNull();
        
        var dataHandler = serviceProvider.GetService<ISerialPortDataHandler>();
        dataHandler.Should().NotBeNull();
        
        // 验证日志服务已注册
        var loggerFactory = serviceProvider.GetService<ILoggerFactory>();
        loggerFactory.Should().NotBeNull();
    }

    [Fact]
    public void AddSerialPortWithLiamLogging_WithCustomOptions_ShouldApplyConfiguration()
    {
        // Arrange
        var services = new ServiceCollection();
        var customLogLevel = LogLevel.Trace;
        var customFilePath = "custom/path/test.log";

        // Act
        services.AddSerialPortWithLiamLogging(options =>
        {
            options.MinLogLevel = customLogLevel;
            options.EnableFileLogging = true;
            options.LogFilePath = customFilePath;
            options.EnableStructuredLogging = true;
            options.EnableAsyncLogging = true;
            options.MaxFileSize = 5 * 1024 * 1024; // 5MB
            options.MaxFiles = 10;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var serialPortService = serviceProvider.GetService<ISerialPortService>();
        serialPortService.Should().NotBeNull();
        
        // 验证日志配置
        var loggerFactory = serviceProvider.GetService<ILoggerFactory>();
        loggerFactory.Should().NotBeNull();
        
        var logger = loggerFactory!.CreateLogger("Test");
        logger.Should().NotBeNull();
        
        // 验证日志级别
        logger.IsEnabled(customLogLevel).Should().BeTrue();
    }

    [Fact]
    public void IsLiamLoggingAvailable_ShouldReturnCorrectValue()
    {
        // Act
        var isAvailable = LiamLoggingExtensions.IsLiamLoggingAvailable();

        // Assert
        // 根据编译时条件返回相应结果
#if LIAM_LOGGING_AVAILABLE
        isAvailable.Should().BeTrue();
#else
        isAvailable.Should().BeFalse();
#endif
    }

    [Fact]
    public void SerialPortLoggingOptions_ShouldHaveDefaultValues()
    {
        // Act
        var options = new SerialPortLoggingOptions();

        // Assert
        options.MinLogLevel.Should().Be(LogLevel.Information);
        options.EnableConsoleLogging.Should().BeTrue();
        options.EnableFileLogging.Should().BeFalse();
        options.LogFilePath.Should().Be("logs/serialport.log");
        options.EnableStructuredLogging.Should().BeTrue();
        options.EnableAsyncLogging.Should().BeTrue();
        options.EnableLogRotation.Should().BeTrue();
        options.MaxFileSize.Should().Be(10 * 1024 * 1024); // 10MB
        options.MaxFiles.Should().Be(5);
    }

    [Fact]
    public void SerialPortLoggingOptions_ShouldAllowCustomization()
    {
        // Arrange
        var options = new SerialPortLoggingOptions();

        // Act
        options.MinLogLevel = LogLevel.Debug;
        options.EnableConsoleLogging = false;
        options.EnableFileLogging = true;
        options.LogFilePath = "custom/test.log";
        options.EnableStructuredLogging = false;
        options.EnableAsyncLogging = false;
        options.EnableLogRotation = false;
        options.MaxFileSize = 1024 * 1024; // 1MB
        options.MaxFiles = 3;

        // Assert
        options.MinLogLevel.Should().Be(LogLevel.Debug);
        options.EnableConsoleLogging.Should().BeFalse();
        options.EnableFileLogging.Should().BeTrue();
        options.LogFilePath.Should().Be("custom/test.log");
        options.EnableStructuredLogging.Should().BeFalse();
        options.EnableAsyncLogging.Should().BeFalse();
        options.EnableLogRotation.Should().BeFalse();
        options.MaxFileSize.Should().Be(1024 * 1024);
        options.MaxFiles.Should().Be(3);
    }

    [Fact]
    public void AddSerialPortWithLiamLogging_WithNullOptions_ShouldUseDefaults()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddSerialPortWithLiamLogging(null);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var serialPortService = serviceProvider.GetService<ISerialPortService>();
        serialPortService.Should().NotBeNull();
        
        var loggerFactory = serviceProvider.GetService<ILoggerFactory>();
        loggerFactory.Should().NotBeNull();
    }

    [Fact]
    public void AddSerialPortWithLiamLogging_ShouldBeIdempotent()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddSerialPortWithLiamLogging();
        services.AddSerialPortWithLiamLogging(); // 重复调用

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var serialPortServices = serviceProvider.GetServices<ISerialPortService>();
        
        // 应该只有一个实例（或者能正确处理多次注册）
        serialPortServices.Should().NotBeEmpty();
    }

    [Theory]
    [InlineData(LogLevel.Trace)]
    [InlineData(LogLevel.Debug)]
    [InlineData(LogLevel.Information)]
    [InlineData(LogLevel.Warning)]
    [InlineData(LogLevel.Error)]
    [InlineData(LogLevel.Critical)]
    public void AddSerialPortWithLiamLogging_WithDifferentLogLevels_ShouldWork(LogLevel logLevel)
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddSerialPortWithLiamLogging(options =>
        {
            options.MinLogLevel = logLevel;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var loggerFactory = serviceProvider.GetService<ILoggerFactory>();
        loggerFactory.Should().NotBeNull();
        
        var logger = loggerFactory!.CreateLogger("Test");
        logger.IsEnabled(logLevel).Should().BeTrue();
    }

    [Fact]
    public void AddSerialPortWithLiamLogging_WithFileLogging_ShouldConfigureCorrectly()
    {
        // Arrange
        var services = new ServiceCollection();
        var testFilePath = "test-logs/serialport-test.log";

        // Act
        services.AddSerialPortWithLiamLogging(options =>
        {
            options.EnableFileLogging = true;
            options.LogFilePath = testFilePath;
            options.EnableLogRotation = true;
            options.MaxFileSize = 2 * 1024 * 1024; // 2MB
            options.MaxFiles = 7;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var serialPortService = serviceProvider.GetService<ISerialPortService>();
        serialPortService.Should().NotBeNull();
        
        // 验证可以创建日志记录器
        var loggerFactory = serviceProvider.GetService<ILoggerFactory>();
        var logger = loggerFactory!.CreateLogger<LiamLoggingExtensionsTests>();
        logger.Should().NotBeNull();
        
        // 测试日志记录
        logger.LogInformation("测试日志记录功能");
    }
}
