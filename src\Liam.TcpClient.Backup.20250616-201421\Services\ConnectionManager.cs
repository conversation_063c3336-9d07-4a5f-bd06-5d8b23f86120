using System.Buffers;
using System.Net;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Constants;
using Liam.TcpClient.Exceptions;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Liam.TcpClient.Security;

namespace Liam.TcpClient.Services;

/// <summary>
/// 连接管理器实现
/// </summary>
public class ConnectionManager : IConnectionManager
{
    private readonly ILogger<ConnectionManager> _logger;
    private readonly SemaphoreSlim _connectionSemaphore = new(1, 1);
    private System.Net.Sockets.TcpClient? _tcpClient;
    private Stream? _stream;
    private ConnectionInfo? _connectionInfo;
    private TcpClientConfig? _config;
    private CancellationTokenSource? _reconnectCts;
    private Timer? _reconnectTimer;
    private bool _autoReconnectEnabled = true;
    private bool _disposed;

    /// <summary>
    /// 连接信息
    /// </summary>
    public ConnectionInfo? ConnectionInfo => _connectionInfo;

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected => _connectionInfo?.IsConnected == true && _tcpClient?.Connected == true;

    /// <summary>
    /// 是否正在连接
    /// </summary>
    public bool IsConnecting => _connectionInfo?.IsConnecting == true;

    /// <summary>
    /// 是否正在重连
    /// </summary>
    public bool IsReconnecting => _connectionInfo?.IsReconnecting == true;

    /// <summary>
    /// TCP客户端
    /// </summary>
    public System.Net.Sockets.TcpClient? TcpClient => _tcpClient;

    /// <summary>
    /// 网络流
    /// </summary>
    public Stream? Stream => _stream;

    /// <summary>
    /// 连接状态变更事件
    /// </summary>
    public event EventHandler<string>? StateChanged;

    /// <summary>
    /// 连接建立事件
    /// </summary>
    public event EventHandler<ConnectionInfo>? Connected;

    /// <summary>
    /// 连接断开事件
    /// </summary>
    public event EventHandler<(ConnectionInfo ConnectionInfo, string? Reason, Exception? Exception)>? Disconnected;

    /// <summary>
    /// 初始化连接管理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ConnectionManager(ILogger<ConnectionManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 连接到服务器
    /// </summary>
    /// <param name="config">客户端配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接任务</returns>
    public async Task<bool> ConnectAsync(TcpClientConfig config, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(config);
        return await ConnectAsync(config.Host, config.Port, config, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 连接到指定服务器
    /// </summary>
    /// <param name="host">服务器主机</param>
    /// <param name="port">服务器端口</param>
    /// <param name="config">客户端配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接任务</returns>
    public async Task<bool> ConnectAsync(string host, int port, TcpClientConfig config, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(host);
        ArgumentNullException.ThrowIfNull(config);

        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ConnectionManager));
        }

        await _connectionSemaphore.WaitAsync(cancellationToken).ConfigureAwait(false);
        try
        {
            if (IsConnected)
            {
                _logger.LogWarning("客户端已连接到 {Host}:{Port}", host, port);
                return true;
            }

            _config = config;
            _connectionInfo = new ConnectionInfo(config.ClientId, config.ClientName);
            UpdateConnectionState(TcpClientConstants.ConnectionStates.Connecting);

            _logger.LogInformation("开始连接到服务器 {Host}:{Port}", host, port);

            try
            {
                // 创建TCP客户端
                _tcpClient = new System.Net.Sockets.TcpClient();
                ConfigureTcpClient(_tcpClient, config);

                // 连接到服务器
                using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(config.ConnectionTimeoutSeconds));
                using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

                await _tcpClient.ConnectAsync(host, port, combinedCts.Token).ConfigureAwait(false);

                // 获取网络流
                _stream = _tcpClient.GetStream();

                // 配置SSL（如果启用）
                if (config.EnableSsl)
                {
                    _stream = await ConfigureSslAsync(_stream, config, host, combinedCts.Token).ConfigureAwait(false);
                }

                // 更新连接信息
                _connectionInfo.RemoteEndPoint = _tcpClient.Client.RemoteEndPoint;
                _connectionInfo.LocalEndPoint = _tcpClient.Client.LocalEndPoint;
                _connectionInfo.IsSslEnabled = config.EnableSsl;
                UpdateConnectionState(TcpClientConstants.ConnectionStates.Connected);

                _logger.LogInformation("成功连接到服务器 {Host}:{Port}", host, port);
                Connected?.Invoke(this, _connectionInfo);
                return true;
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                _logger.LogWarning("连接操作被取消");
                throw;
            }
            catch (OperationCanceledException)
            {
                var error = $"连接超时，超时时间：{config.ConnectionTimeoutSeconds}秒";
                _logger.LogError(error);
                _connectionInfo?.SetError(error);

                var context = new Dictionary<string, object>
                {
                    ["Host"] = host,
                    ["Port"] = port,
                    ["ConfiguredTimeoutSeconds"] = config.ConnectionTimeoutSeconds
                };
                throw new ConnectionTimeoutException(
                    TimeSpan.FromSeconds(config.ConnectionTimeoutSeconds),
                    error,
                    null,
                    "Connecting",
                    config,
                    context);
            }
            catch (Exception ex)
            {
                var error = $"连接失败：{ex.Message}";
                _logger.LogError(ex, error);
                _connectionInfo?.SetError(error);
                await CleanupConnectionAsync().ConfigureAwait(false);

                var context = new Dictionary<string, object>
                {
                    ["Host"] = host,
                    ["Port"] = port,
                    ["ExceptionType"] = ex.GetType().Name,
                    ["OriginalMessage"] = ex.Message
                };
                throw new ConnectionException(error, ex, "Failed", config, context);
            }
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// 断开连接
    /// </summary>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>断开任务</returns>
    public async Task DisconnectAsync(string? reason = null, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            return;
        }

        await _connectionSemaphore.WaitAsync(cancellationToken).ConfigureAwait(false);
        try
        {
            if (!IsConnected && !IsConnecting)
            {
                return;
            }

            _logger.LogInformation("断开连接，原因：{Reason}", reason ?? "用户请求");

            // 停止自动重连
            await StopAutoReconnectAsync().ConfigureAwait(false);

            // 更新状态
            UpdateConnectionState(TcpClientConstants.ConnectionStates.Disconnecting);

            // 清理连接
            await CleanupConnectionAsync().ConfigureAwait(false);

            // 更新状态
            UpdateConnectionState(TcpClientConstants.ConnectionStates.Disconnected);

            // 触发断开事件
            if (_connectionInfo != null)
            {
                Disconnected?.Invoke(this, (_connectionInfo, reason, null));
            }

            _logger.LogInformation("连接已断开");
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// 重新连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重连任务</returns>
    public async Task<bool> ReconnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed || _config == null)
        {
            return false;
        }

        _logger.LogInformation("开始重新连接");

        // 先断开现有连接
        await DisconnectAsync("重新连接", cancellationToken).ConfigureAwait(false);

        // 等待一段时间后重连
        await Task.Delay(TimeSpan.FromSeconds(_config.ReconnectIntervalSeconds), cancellationToken).ConfigureAwait(false);

        // 重新连接
        return await ConnectAsync(_config, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 检查连接状态
    /// </summary>
    /// <returns>是否连接正常</returns>
    public bool CheckConnection()
    {
        if (_tcpClient?.Client == null || _connectionInfo == null)
        {
            return false;
        }

        try
        {
            // 使用Socket.Poll检查连接状态
            var socket = _tcpClient.Client;
            return !(socket.Poll(1000, SelectMode.SelectRead) && socket.Available == 0);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取连接延迟
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>延迟时间（毫秒）</returns>
    public async Task<double?> GetLatencyAsync(CancellationToken cancellationToken = default)
    {
        if (!IsConnected || _stream == null)
        {
            return null;
        }

        try
        {
            var startTime = DateTime.UtcNow;
            
            // 发送一个小的测试数据包
            var testData = new byte[] { 0x00 };
            await _stream.WriteAsync(testData, cancellationToken).ConfigureAwait(false);
            await _stream.FlushAsync(cancellationToken).ConfigureAwait(false);

            var endTime = DateTime.UtcNow;
            return (endTime - startTime).TotalMilliseconds;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 刷新连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>刷新任务</returns>
    public Task RefreshAsync(CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
        {
            return Task.CompletedTask;
        }

        _connectionInfo?.UpdateLastActivity();

        // 检查连接健康状态
        if (!CheckConnection())
        {
            _logger.LogWarning("检测到连接异常，尝试重新连接");
            if (_autoReconnectEnabled)
            {
                // 移除Task.Run反模式，直接启动重连任务
                _ = StartAutoReconnectAsync();
            }
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 启用自动重连
    /// </summary>
    public void EnableAutoReconnect()
    {
        _autoReconnectEnabled = true;
        _logger.LogDebug("已启用自动重连");
    }

    /// <summary>
    /// 禁用自动重连
    /// </summary>
    public void DisableAutoReconnect()
    {
        _autoReconnectEnabled = false;
        _ = Task.Run(async () => await StopAutoReconnectAsync().ConfigureAwait(false));
        _logger.LogDebug("已禁用自动重连");
    }

    /// <summary>
    /// 获取连接质量评分
    /// </summary>
    /// <returns>质量评分（0-100）</returns>
    public double GetConnectionQuality()
    {
        if (!IsConnected || _connectionInfo == null)
        {
            return 0;
        }

        var quality = 100.0;

        // 基于连接稳定性评分
        if (_connectionInfo.ReconnectCount > 0)
        {
            quality -= Math.Min(_connectionInfo.ReconnectCount * 10, 50);
        }

        // 基于错误率评分
        var errorRate = _connectionInfo.Statistics.Errors > 0 ? 
            (double)_connectionInfo.Statistics.Errors / Math.Max(_connectionInfo.Statistics.MessagesSent + _connectionInfo.Statistics.MessagesReceived, 1) : 0;
        quality -= errorRate * 30;

        // 基于延迟评分
        if (_connectionInfo.Statistics.AverageLatency > 100)
        {
            quality -= Math.Min((_connectionInfo.Statistics.AverageLatency - 100) / 10, 20);
        }

        return Math.Max(quality, 0);
    }

    /// <summary>
    /// 配置TCP客户端
    /// </summary>
    /// <param name="tcpClient">TCP客户端</param>
    /// <param name="config">配置</param>
    private static void ConfigureTcpClient(System.Net.Sockets.TcpClient tcpClient, TcpClientConfig config)
    {
        tcpClient.ReceiveBufferSize = config.ReceiveBufferSize;
        tcpClient.SendBufferSize = config.SendBufferSize;
        tcpClient.NoDelay = true;
    }

    /// <summary>
    /// 配置SSL
    /// </summary>
    /// <param name="stream">网络流</param>
    /// <param name="config">配置</param>
    /// <param name="host">主机名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>SSL流</returns>
    private async Task<Stream> ConfigureSslAsync(Stream stream, TcpClientConfig config, string host, CancellationToken cancellationToken)
    {
        if (config.SslConfig == null)
        {
            var context = new Dictionary<string, object>
            {
                ["Host"] = host,
                ["Operation"] = "SSL Configuration"
            };
            throw new ConfigurationException("SSL配置不能为空", null, "Connecting", config, context);
        }

        try
        {
            // 创建安全的证书验证器
            var certificateValidator = new CertificateValidator(config.SslConfig,
                _logger as ILogger<CertificateValidator>);

            // 使用安全的证书验证回调
            RemoteCertificateValidationCallback validationCallback = config.SslConfig.ValidationMode == CertificateValidationMode.Custom &&
                                                                    config.SslConfig.RemoteCertificateValidationCallback != null
                ? config.SslConfig.RemoteCertificateValidationCallback
                : certificateValidator.ValidateRemoteCertificate;

            var sslStream = new SslStream(stream, false,
                validationCallback,
                config.SslConfig.LocalCertificateSelectionCallback);

            var serverName = config.SslConfig.ServerName ?? host;
            var clientCertificates = config.SslConfig.ClientCertificate != null ?
                new X509CertificateCollection { config.SslConfig.ClientCertificate } : null;

            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(config.SslConfig.HandshakeTimeoutSeconds));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            // 使用配置的SSL协议版本，默认为None让系统选择最安全的协议
            var sslProtocols = config.SslConfig.SslProtocols;

            _logger.LogDebug("开始SSL握手，服务器名称: {ServerName}, 协议: {Protocols}, 验证模式: {ValidationMode}",
                serverName, sslProtocols, config.SslConfig.ValidationMode);

            await sslStream.AuthenticateAsClientAsync(serverName, clientCertificates,
                sslProtocols,
                config.SslConfig.CheckCertificateRevocation).ConfigureAwait(false);

            // 更新SSL信息
            if (_connectionInfo != null)
            {
                _connectionInfo.SslInfo = new SslInfo
                {
                    Protocol = sslStream.SslProtocol.ToString(),
                    CipherAlgorithm = sslStream.CipherAlgorithm.ToString(),
                    HashAlgorithm = sslStream.HashAlgorithm.ToString(),
                    KeyExchangeAlgorithm = sslStream.KeyExchangeAlgorithm.ToString(),
                    IsMutuallyAuthenticated = sslStream.IsMutuallyAuthenticated,
                    IsAuthenticated = sslStream.IsAuthenticated,
                    IsEncrypted = sslStream.IsEncrypted,
                    IsSigned = sslStream.IsSigned,
                    HandshakeCompletedAt = DateTime.UtcNow
                };
            }

            _logger.LogInformation("SSL握手完成，协议：{Protocol}", sslStream.SslProtocol);
            return sslStream;
        }
        catch (AuthenticationException ex)
        {
            var context = new Dictionary<string, object>
            {
                ["Host"] = host,
                ["ServerName"] = config.SslConfig.ServerName ?? host,
                ["ValidationMode"] = config.SslConfig.ValidationMode.ToString(),
                ["Operation"] = "SSL Authentication"
            };
            _logger.LogError(ex, "SSL认证失败");
            throw new SecurityException($"SSL认证失败: {ex.Message}", ex, "SSL Failed", config, context);
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogWarning("SSL握手被取消");
            throw;
        }
        catch (OperationCanceledException)
        {
            var context = new Dictionary<string, object>
            {
                ["Host"] = host,
                ["ServerName"] = config.SslConfig.ServerName ?? host,
                ["TimeoutSeconds"] = config.SslConfig.HandshakeTimeoutSeconds,
                ["Operation"] = "SSL Handshake Timeout"
            };
            _logger.LogError("SSL握手超时");
            throw new ConnectionTimeoutException(
                TimeSpan.FromSeconds(config.SslConfig.HandshakeTimeoutSeconds),
                "SSL握手超时",
                null,
                "SSL Timeout",
                config,
                context);
        }
        catch (Exception ex)
        {
            var context = new Dictionary<string, object>
            {
                ["Host"] = host,
                ["ServerName"] = config.SslConfig.ServerName ?? host,
                ["ExceptionType"] = ex.GetType().Name,
                ["Operation"] = "SSL Configuration"
            };
            _logger.LogError(ex, "SSL配置失败");
            throw new SecurityException($"SSL配置失败: {ex.Message}", ex, "SSL Failed", config, context);
        }
    }

    /// <summary>
    /// 更新连接状态
    /// </summary>
    /// <param name="newState">新状态</param>
    private void UpdateConnectionState(string newState)
    {
        var oldState = _connectionInfo?.State;
        _connectionInfo?.UpdateState(newState);
        
        if (oldState != newState)
        {
            _logger.LogDebug("连接状态变更：{OldState} -> {NewState}", oldState, newState);
            StateChanged?.Invoke(this, newState);
        }
    }

    /// <summary>
    /// 清理连接资源
    /// </summary>
    private async Task CleanupConnectionAsync()
    {
        try
        {
            if (_stream != null)
            {
                await _stream.DisposeAsync().ConfigureAwait(false);
                _stream = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "清理网络流时发生错误");
        }

        try
        {
            _tcpClient?.Close();
            _tcpClient?.Dispose();
            _tcpClient = null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "清理TCP客户端时发生错误");
        }
    }

    /// <summary>
    /// 开始自动重连
    /// </summary>
    private async Task StartAutoReconnectAsync()
    {
        if (!_autoReconnectEnabled || _config == null || _connectionInfo == null)
        {
            return;
        }

        await StopAutoReconnectAsync().ConfigureAwait(false);

        _reconnectCts = new CancellationTokenSource();
        var cancellationToken = _reconnectCts.Token;

        try
        {
            UpdateConnectionState(TcpClientConstants.ConnectionStates.Reconnecting);

            var maxAttempts = _config.MaxReconnectAttempts;
            var attempt = 0;

            while (!cancellationToken.IsCancellationRequested && 
                   (maxAttempts == -1 || attempt < maxAttempts))
            {
                attempt++;
                _connectionInfo.ReconnectCount++;

                _logger.LogInformation("尝试重连，第 {Attempt} 次", attempt);

                try
                {
                    if (await ReconnectAsync(cancellationToken).ConfigureAwait(false))
                    {
                        _logger.LogInformation("重连成功");
                        return;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "重连失败，第 {Attempt} 次", attempt);
                }

                if (!cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(TimeSpan.FromSeconds(_config.ReconnectIntervalSeconds), cancellationToken).ConfigureAwait(false);
                }
            }

            if (maxAttempts != -1 && attempt >= maxAttempts)
            {
                _logger.LogError("重连失败，已达到最大重连次数 {MaxAttempts}", maxAttempts);
                UpdateConnectionState(TcpClientConstants.ConnectionStates.Error);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("自动重连被取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自动重连过程中发生错误");
        }
    }

    /// <summary>
    /// 停止自动重连
    /// </summary>
    private async Task StopAutoReconnectAsync()
    {
        if (_reconnectCts != null)
        {
            _reconnectCts.Cancel();
            _reconnectCts.Dispose();
            _reconnectCts = null;
        }

        if (_reconnectTimer != null)
        {
            await _reconnectTimer.DisposeAsync().ConfigureAwait(false);
            _reconnectTimer = null;
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;
        
        try
        {
            DisconnectAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "释放连接管理器时发生错误");
        }

        _connectionSemaphore.Dispose();
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        try
        {
            await DisconnectAsync().ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "异步释放连接管理器时发生错误");
        }

        _connectionSemaphore.Dispose();
        GC.SuppressFinalize(this);
    }
}
