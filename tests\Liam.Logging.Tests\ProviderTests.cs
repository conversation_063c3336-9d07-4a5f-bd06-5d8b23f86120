using FluentAssertions;
using Liam.Logging.Constants;
using Liam.Logging.Formatters;
using Liam.Logging.Models;
using Liam.Logging.Providers;
using Xunit;

namespace Liam.Logging.Tests;

/// <summary>
/// 日志提供程序测试
/// </summary>
public class ProviderTests
{
    [Fact]
    public void ConsoleLogProvider_Constructor_ShouldSetProperties()
    {
        // Arrange
        var formatter = new TextLogFormatter();

        // Act
        var provider = new ConsoleLogProvider(formatter, enableColors: true, useStandardError: false);

        // Assert
        provider.Name.Should().Be("Console");
        provider.IsEnabled.Should().BeTrue();
        provider.SupportsAsync.Should().BeTrue();
    }

    [Fact]
    public async Task ConsoleLogProvider_InitializeAsync_ShouldComplete()
    {
        // Arrange
        var provider = new ConsoleLogProvider();
        var config = new LogProviderConfiguration();

        // Act & Assert
        await provider.InitializeAsync(config);
        // Should not throw
    }

    [Fact]
    public void ConsoleLogProvider_WriteLog_ShouldNotThrow()
    {
        // Arrange
        var provider = new ConsoleLogProvider();
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test message",
            Timestamp = DateTimeOffset.Now
        };

        // Act & Assert
        var act = () => provider.WriteLog(logEvent);
        act.Should().NotThrow();
    }

    [Fact]
    public async Task ConsoleLogProvider_WriteLogAsync_ShouldNotThrow()
    {
        // Arrange
        var provider = new ConsoleLogProvider();
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test async message",
            Timestamp = DateTimeOffset.Now
        };

        // Act & Assert
        var act = async () => await provider.WriteLogAsync(logEvent);
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public void ConsoleLogProvider_WriteLogs_ShouldNotThrow()
    {
        // Arrange
        var provider = new ConsoleLogProvider();
        var logEvents = new[]
        {
            new LogEvent { Level = LogLevel.Information, Message = "Message 1", Timestamp = DateTimeOffset.Now },
            new LogEvent { Level = LogLevel.Warning, Message = "Message 2", Timestamp = DateTimeOffset.Now },
            new LogEvent { Level = LogLevel.Error, Message = "Message 3", Timestamp = DateTimeOffset.Now }
        };

        // Act & Assert
        var act = () => provider.WriteLogs(logEvents);
        act.Should().NotThrow();
    }

    [Fact]
    public async Task ConsoleLogProvider_WriteLogsAsync_ShouldNotThrow()
    {
        // Arrange
        var provider = new ConsoleLogProvider();
        var logEvents = new[]
        {
            new LogEvent { Level = LogLevel.Information, Message = "Async Message 1", Timestamp = DateTimeOffset.Now },
            new LogEvent { Level = LogLevel.Warning, Message = "Async Message 2", Timestamp = DateTimeOffset.Now }
        };

        // Act & Assert
        var act = async () => await provider.WriteLogsAsync(logEvents);
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public void ConsoleLogProvider_Flush_ShouldNotThrow()
    {
        // Arrange
        var provider = new ConsoleLogProvider();

        // Act & Assert
        var act = () => provider.Flush();
        act.Should().NotThrow();
    }

    [Fact]
    public async Task ConsoleLogProvider_FlushAsync_ShouldNotThrow()
    {
        // Arrange
        var provider = new ConsoleLogProvider();

        // Act & Assert
        var act = async () => await provider.FlushAsync();
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public void ConsoleLogProvider_Dispose_ShouldNotThrow()
    {
        // Arrange
        var provider = new ConsoleLogProvider();

        // Act & Assert
        var act = () => provider.Dispose();
        act.Should().NotThrow();
    }

    [Fact]
    public void FileLogProvider_Constructor_ShouldSetProperties()
    {
        // Arrange
        var filePath = "test.log";
        var formatter = new TextLogFormatter();

        // Act
        var provider = new FileLogProvider(filePath, formatter);

        // Assert
        provider.Name.Should().Be("File");
        provider.IsEnabled.Should().BeTrue();
        provider.SupportsAsync.Should().BeTrue();
    }

    [Fact]
    public void FileLogProvider_Constructor_WithNullFilePath_ShouldThrowArgumentNullException()
    {
        // Arrange & Act & Assert
        var act = () => new FileLogProvider(null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public async Task FileLogProvider_InitializeAsync_ShouldCreateFile()
    {
        // Arrange
        var tempFile = Path.GetTempFileName();
        var provider = new FileLogProvider(tempFile);
        var config = new LogProviderConfiguration();

        try
        {
            // Act
            await provider.InitializeAsync(config);

            // Assert
            // Should not throw and file should be accessible
            provider.IsEnabled.Should().BeTrue();
        }
        finally
        {
            provider.Dispose();
            if (File.Exists(tempFile))
            {
                File.Delete(tempFile);
            }
        }
    }

    [Fact]
    public async Task FileLogProvider_WriteLog_ShouldCreateFileAndWriteContent()
    {
        // Arrange
        var tempFile = Path.GetTempFileName();
        var provider = new FileLogProvider(tempFile, enableRotation: false); // 禁用轮转
        var config = new LogProviderConfiguration();
        await provider.InitializeAsync(config);

        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test file message",
            Timestamp = DateTimeOffset.Now
        };

        try
        {
            // Act
            provider.WriteLog(logEvent);
            provider.Flush();
            provider.Dispose(); // 释放资源以便读取文件

            // Assert
            File.Exists(tempFile).Should().BeTrue();
            var content = File.ReadAllText(tempFile);
            content.Should().Contain("Test file message");
            content.Should().Contain("[INFO ]");
        }
        finally
        {
            if (File.Exists(tempFile))
            {
                File.Delete(tempFile);
            }
        }
    }

    [Fact]
    public async Task FileLogProvider_WriteLogAsync_ShouldCreateFileAndWriteContent()
    {
        // Arrange
        var tempFile = Path.GetTempFileName();
        var provider = new FileLogProvider(tempFile, enableRotation: false); // 禁用轮转
        var config = new LogProviderConfiguration();
        await provider.InitializeAsync(config);

        var logEvent = new LogEvent
        {
            Level = LogLevel.Warning,
            Message = "Test async file message",
            Timestamp = DateTimeOffset.Now
        };

        try
        {
            // Act
            await provider.WriteLogAsync(logEvent);
            await provider.FlushAsync();
            provider.Dispose(); // 释放资源以便读取文件

            // Assert
            File.Exists(tempFile).Should().BeTrue();
            var content = File.ReadAllText(tempFile);
            content.Should().Contain("Test async file message");
            content.Should().Contain("[WARN ]");
        }
        finally
        {
            if (File.Exists(tempFile))
            {
                File.Delete(tempFile);
            }
        }
    }

    [Fact]
    public async Task FileLogProvider_WriteLogs_ShouldWriteMultipleMessages()
    {
        // Arrange
        var tempFile = Path.GetTempFileName();
        var provider = new FileLogProvider(tempFile, enableRotation: false); // 禁用轮转
        var config = new LogProviderConfiguration();
        await provider.InitializeAsync(config);

        var logEvents = new[]
        {
            new LogEvent { Level = LogLevel.Information, Message = "Batch Message 1", Timestamp = DateTimeOffset.Now },
            new LogEvent { Level = LogLevel.Error, Message = "Batch Message 2", Timestamp = DateTimeOffset.Now }
        };

        try
        {
            // Act
            provider.WriteLogs(logEvents);
            provider.Flush();
            provider.Dispose(); // 释放资源以便读取文件

            // Assert
            File.Exists(tempFile).Should().BeTrue();
            var content = File.ReadAllText(tempFile);
            content.Should().Contain("Batch Message 1");
            content.Should().Contain("Batch Message 2");
            content.Should().Contain("[INFO ]");
            content.Should().Contain("[ERROR]");
        }
        finally
        {
            if (File.Exists(tempFile))
            {
                File.Delete(tempFile);
            }
        }
    }

    [Fact]
    public void FileLogProvider_WithDisabledProvider_ShouldNotWrite()
    {
        // Arrange
        var tempFile = Path.GetTempFileName();
        var provider = new FileLogProvider(tempFile);
        provider.IsEnabled = false;
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Should not be written",
            Timestamp = DateTimeOffset.Now
        };

        try
        {
            // Act
            provider.WriteLog(logEvent);
            provider.Flush();

            // Assert
            if (File.Exists(tempFile))
            {
                var content = File.ReadAllText(tempFile);
                content.Should().BeEmpty();
            }
        }
        finally
        {
            provider.Dispose();
            if (File.Exists(tempFile))
            {
                File.Delete(tempFile);
            }
        }
    }

    [Fact]
    public void FileLogProvider_Dispose_ShouldCleanupResources()
    {
        // Arrange
        var tempFile = Path.GetTempFileName();
        var provider = new FileLogProvider(tempFile);

        // Act
        provider.Dispose();

        // Assert
        // Should not throw when writing after dispose
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "After dispose",
            Timestamp = DateTimeOffset.Now
        };

        var act = () => provider.WriteLog(logEvent);
        act.Should().NotThrow(); // Should be ignored silently

        // Cleanup
        if (File.Exists(tempFile))
        {
            File.Delete(tempFile);
        }
    }

    [Fact]
    public async Task FileLogProvider_DisposeAsync_ShouldCleanupResourcesAsync()
    {
        // Arrange
        var tempFile = Path.GetTempFileName();
        var provider = new FileLogProvider(tempFile);

        try
        {
            // Act
            await provider.DisposeAsync();

            // Assert
            // Should not throw when writing after dispose
            var logEvent = new LogEvent
            {
                Level = LogLevel.Information,
                Message = "After async dispose",
                Timestamp = DateTimeOffset.Now
            };

            var act = () => provider.WriteLog(logEvent);
            act.Should().NotThrow(); // Should be ignored silently
        }
        finally
        {
            // Cleanup
            if (File.Exists(tempFile))
            {
                File.Delete(tempFile);
            }
        }
    }

    [Fact]
    public async Task ConsoleLogProvider_WriteLogAsync_WithCancellation_ShouldRespectCancellationToken()
    {
        // Arrange
        var provider = new ConsoleLogProvider();
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test cancellation",
            Timestamp = DateTimeOffset.Now
        };
        var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            provider.WriteLogAsync(logEvent, cts.Token));
    }

    [Fact]
    public async Task FileLogProvider_WriteLogAsync_WithCancellation_ShouldRespectCancellationToken()
    {
        // Arrange
        var tempFile = Path.GetTempFileName();
        var provider = new FileLogProvider(tempFile);
        var config = new LogProviderConfiguration();
        await provider.InitializeAsync(config);

        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test cancellation",
            Timestamp = DateTimeOffset.Now
        };
        var cts = new CancellationTokenSource();
        cts.Cancel();

        try
        {
            // Act & Assert
            await Assert.ThrowsAsync<OperationCanceledException>(() =>
                provider.WriteLogAsync(logEvent, cts.Token));
        }
        finally
        {
            provider.Dispose();
            if (File.Exists(tempFile))
            {
                File.Delete(tempFile);
            }
        }
    }
}
