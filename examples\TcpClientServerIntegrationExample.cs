using Microsoft.Extensions.Logging;
using Liam.TcpClient.Models;
using Liam.TcpClient.Services;
using Liam.TcpServer.Models;
using Liam.TcpServer.Services;
using TcpClient = Liam.TcpClient.Services.TcpClient;
using TcpServer = Liam.TcpServer.Services.TcpServer;

namespace Liam.Examples;

/// <summary>
/// TCP客户端和服务器集成示例
/// </summary>
public class TcpClientServerIntegrationExample
{
    /// <summary>
    /// 基本客户端-服务器通信示例
    /// </summary>
    public static async Task BasicClientServerExample()
    {
        Console.WriteLine("=== TCP客户端-服务器集成示例 ===");

        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        const int port = 8080;

        // 创建服务器
        var serverConfig = new TcpServerConfig
        {
            Port = port,
            EnableHeartbeat = true,
            MaxConnections = 10
        };

        var serverLogger = loggerFactory.CreateLogger<TcpServer>();
        var serverConnectionManagerLogger = loggerFactory.CreateLogger<Liam.TcpServer.Services.ConnectionManager>();
        var serverMessageHandlerLogger = loggerFactory.CreateLogger<Liam.TcpServer.Services.MessageHandler>();

        var serverConnectionManager = new Liam.TcpServer.Services.ConnectionManager(serverConnectionManagerLogger);
        var serverMessageHandler = new Liam.TcpServer.Services.MessageHandler(serverMessageHandlerLogger);

        using var server = new TcpServer(serverConfig, serverLogger, serverConnectionManager, serverMessageHandler);

        // 订阅服务器事件
        server.ClientConnected += (sender, e) =>
        {
            Console.WriteLine($"🔗 服务器: 客户端已连接 {e.ConnectionInfo.Id[..8]}");
        };

        server.DataReceived += (sender, e) =>
        {
            Console.WriteLine($"📨 服务器: 收到数据 '{e.Data}' 来自 {e.ConnectionId[..8]}");
            
            // 回显消息
            _ = Task.Run(async () =>
            {
                var response = $"Echo: {e.Data}";
                await server.SendTextAsync(e.ConnectionId, response);
                Console.WriteLine($"📤 服务器: 已回复 '{response}' 给 {e.ConnectionId[..8]}");
            });
        };

        try
        {
            // 启动服务器
            Console.WriteLine("正在启动服务器...");
            await server.StartAsync();
            Console.WriteLine($"✅ 服务器已启动，监听端口 {port}");

            // 等待服务器完全启动
            await Task.Delay(1000);

            // 创建客户端配置
            var clientConfig = new TcpClientConfig
            {
                Host = "localhost",
                Port = port,
                EnableHeartbeat = true,
                ClientId = "test-client-001",
                ClientName = "IntegrationTestClient"
            };

            // 创建客户端
            var clientLogger = loggerFactory.CreateLogger<TcpClient>();
            var clientConnectionManagerLogger = loggerFactory.CreateLogger<Liam.TcpClient.Services.ConnectionManager>();
            var clientMessageHandlerLogger = loggerFactory.CreateLogger<Liam.TcpClient.Services.MessageHandler>();
            var clientHeartbeatManagerLogger = loggerFactory.CreateLogger<Liam.TcpClient.Services.HeartbeatManager>();

            var clientConnectionManager = new Liam.TcpClient.Services.ConnectionManager(clientConnectionManagerLogger);
            var clientMessageHandler = new Liam.TcpClient.Services.MessageHandler(clientMessageHandlerLogger);
            var clientHeartbeatManager = new Liam.TcpClient.Services.HeartbeatManager(clientHeartbeatManagerLogger, clientMessageHandler);

            using var client = new TcpClient(clientConfig, clientLogger, clientConnectionManager, clientMessageHandler, clientHeartbeatManager);

            // 订阅客户端事件
            client.Connected += (sender, e) =>
            {
                Console.WriteLine($"🔗 客户端: 已连接到服务器 {e.ConnectionInfo.RemoteEndPoint}");
            };

            client.MessageReceived += (sender, e) =>
            {
                Console.WriteLine($"📨 客户端: 收到消息 '{e.Message.GetText()}'");
            };

            // 连接到服务器
            Console.WriteLine("正在连接到服务器...");
            if (await client.ConnectAsync())
            {
                Console.WriteLine("✅ 客户端连接成功");

                // 发送测试消息
                var testMessages = new[]
                {
                    "Hello Server!",
                    "How are you?",
                    "This is a test message",
                    "Goodbye!"
                };

                foreach (var message in testMessages)
                {
                    Console.WriteLine($"📤 客户端: 发送 '{message}'");
                    await client.SendTextAsync(message);
                    
                    // 等待响应
                    await Task.Delay(500);
                }

                // 等待所有消息处理完成
                await Task.Delay(2000);

                // 显示统计信息
                Console.WriteLine("\n📊 统计信息:");
                
                var clientStats = client.GetStatistics();
                Console.WriteLine($"客户端 - 发送: {clientStats.TotalMessagesSent}, 接收: {clientStats.TotalMessagesReceived}");
                
                var serverStats = server.GetStatistics();
                Console.WriteLine($"服务器 - 发送: {serverStats.TotalMessagesSent}, 接收: {serverStats.TotalMessagesReceived}");

                Console.WriteLine($"客户端连接质量: {client.GetConnectionQuality():F1}%");
            }
            else
            {
                Console.WriteLine("❌ 客户端连接失败");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 集成示例失败: {ex.Message}");
        }
        finally
        {
            // 停止服务器
            Console.WriteLine("正在停止服务器...");
            await server.StopAsync();
            Console.WriteLine("✅ 服务器已停止");
        }
    }

    /// <summary>
    /// 多客户端示例
    /// </summary>
    public static async Task MultipleClientsExample()
    {
        Console.WriteLine("\n=== 多客户端示例 ===");

        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        const int port = 8081;
        const int clientCount = 3;

        // 创建服务器
        var serverConfig = new TcpServerConfig
        {
            Port = port,
            EnableHeartbeat = true,
            MaxConnections = 10
        };

        var serverLogger = loggerFactory.CreateLogger<TcpServer>();
        var serverConnectionManagerLogger = loggerFactory.CreateLogger<Liam.TcpServer.Services.ConnectionManager>();
        var serverMessageHandlerLogger = loggerFactory.CreateLogger<Liam.TcpServer.Services.MessageHandler>();

        var serverConnectionManager = new Liam.TcpServer.Services.ConnectionManager(serverConnectionManagerLogger);
        var serverMessageHandler = new Liam.TcpServer.Services.MessageHandler(serverMessageHandlerLogger);

        using var server = new TcpServer(serverConfig, serverLogger, serverConnectionManager, serverMessageHandler);

        // 订阅服务器事件
        server.ClientConnected += (sender, e) =>
        {
            Console.WriteLine($"🔗 服务器: 客户端 {e.ConnectionInfo.ClientName} 已连接");
        };

        server.DataReceived += (sender, e) =>
        {
            Console.WriteLine($"📨 服务器: 收到来自 {e.ConnectionId[..8]} 的消息: {e.Data}");
        };

        try
        {
            // 启动服务器
            await server.StartAsync();
            Console.WriteLine($"✅ 服务器已启动，监听端口 {port}");
            await Task.Delay(1000);

            // 创建多个客户端
            var clients = new List<TcpClient>();
            var tasks = new List<Task>();

            for (int i = 0; i < clientCount; i++)
            {
                var clientId = i;
                var task = Task.Run(async () =>
                {
                    var clientConfig = new TcpClientConfig
                    {
                        Host = "localhost",
                        Port = port,
                        EnableHeartbeat = true,
                        ClientId = $"client-{clientId:D3}",
                        ClientName = $"TestClient{clientId}"
                    };

                    var clientLogger = loggerFactory.CreateLogger<TcpClient>();
                    var clientConnectionManagerLogger = loggerFactory.CreateLogger<Liam.TcpClient.Services.ConnectionManager>();
                    var clientMessageHandlerLogger = loggerFactory.CreateLogger<Liam.TcpClient.Services.MessageHandler>();
                    var clientHeartbeatManagerLogger = loggerFactory.CreateLogger<Liam.TcpClient.Services.HeartbeatManager>();

                    var clientConnectionManager = new Liam.TcpClient.Services.ConnectionManager(clientConnectionManagerLogger);
                    var clientMessageHandler = new Liam.TcpClient.Services.MessageHandler(clientMessageHandlerLogger);
                    var clientHeartbeatManager = new Liam.TcpClient.Services.HeartbeatManager(clientHeartbeatManagerLogger, clientMessageHandler);

                    using var client = new TcpClient(clientConfig, clientLogger, clientConnectionManager, clientMessageHandler, clientHeartbeatManager);
                    
                    client.Connected += (sender, e) =>
                    {
                        Console.WriteLine($"🔗 客户端{clientId}: 已连接");
                    };

                    if (await client.ConnectAsync())
                    {
                        // 每个客户端发送几条消息
                        for (int j = 0; j < 3; j++)
                        {
                            var message = $"Message {j + 1} from Client {clientId}";
                            await client.SendTextAsync(message);
                            Console.WriteLine($"📤 客户端{clientId}: 发送 '{message}'");
                            await Task.Delay(1000);
                        }

                        // 保持连接一段时间
                        await Task.Delay(5000);
                    }
                });

                tasks.Add(task);
            }

            // 等待所有客户端完成
            await Task.WhenAll(tasks);

            // 显示服务器统计
            var serverStats = server.GetStatistics();
            Console.WriteLine($"\n📊 服务器最终统计:");
            Console.WriteLine($"   总连接数: {serverStats.TotalConnections}");
            Console.WriteLine($"   当前连接数: {serverStats.CurrentConnections}");
            Console.WriteLine($"   总接收消息: {serverStats.TotalMessagesReceived}");
            Console.WriteLine($"   总发送消息: {serverStats.TotalMessagesSent}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 多客户端示例失败: {ex.Message}");
        }
        finally
        {
            await server.StopAsync();
            Console.WriteLine("✅ 服务器已停止");
        }
    }

    /// <summary>
    /// 心跳和重连集成测试
    /// </summary>
    public static async Task HeartbeatReconnectIntegrationExample()
    {
        Console.WriteLine("\n=== 心跳和重连集成测试 ===");

        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        const int port = 8082;

        // 创建服务器
        var serverConfig = new TcpServerConfig
        {
            Port = port,
            EnableHeartbeat = true,
            HeartbeatIntervalSeconds = 5,
            HeartbeatTimeoutSeconds = 3
        };

        var serverLogger = loggerFactory.CreateLogger<TcpServer>();
        var serverConnectionManagerLogger = loggerFactory.CreateLogger<Liam.TcpServer.Services.ConnectionManager>();
        var serverMessageHandlerLogger = loggerFactory.CreateLogger<Liam.TcpServer.Services.MessageHandler>();

        var serverConnectionManager = new Liam.TcpServer.Services.ConnectionManager(serverConnectionManagerLogger);
        var serverMessageHandler = new Liam.TcpServer.Services.MessageHandler(serverMessageHandlerLogger);

        using var server = new TcpServer(serverConfig, serverLogger, serverConnectionManager, serverMessageHandler);

        // 创建客户端
        var clientConfig = new TcpClientConfig
        {
            Host = "localhost",
            Port = port,
            EnableHeartbeat = true,
            HeartbeatIntervalSeconds = 5,
            HeartbeatTimeoutSeconds = 3,
            EnableAutoReconnect = true,
            ReconnectIntervalSeconds = 2,
            MaxReconnectAttempts = 3
        };

        var clientLogger = loggerFactory.CreateLogger<TcpClient>();
        var clientConnectionManagerLogger = loggerFactory.CreateLogger<Liam.TcpClient.Services.ConnectionManager>();
        var clientMessageHandlerLogger = loggerFactory.CreateLogger<Liam.TcpClient.Services.MessageHandler>();
        var clientHeartbeatManagerLogger = loggerFactory.CreateLogger<Liam.TcpClient.Services.HeartbeatManager>();

        var clientConnectionManager = new Liam.TcpClient.Services.ConnectionManager(clientConnectionManagerLogger);
        var clientMessageHandler = new Liam.TcpClient.Services.MessageHandler(clientMessageHandlerLogger);
        var clientHeartbeatManager = new Liam.TcpClient.Services.HeartbeatManager(clientHeartbeatManagerLogger, clientMessageHandler);

        using var client = new TcpClient(clientConfig, clientLogger, clientConnectionManager, clientMessageHandler, clientHeartbeatManager);

        // 订阅心跳事件
        client.Heartbeat += (sender, e) =>
        {
            switch (e.Type)
            {
                case Liam.TcpClient.Events.HeartbeatType.Request:
                    Console.WriteLine("💓 客户端: 发送心跳");
                    break;
                case Liam.TcpClient.Events.HeartbeatType.Response:
                    Console.WriteLine($"💓 客户端: 心跳响应 {e.ResponseTime:F2}ms");
                    break;
                case Liam.TcpClient.Events.HeartbeatType.Timeout:
                    Console.WriteLine("💔 客户端: 心跳超时");
                    break;
            }
        };

        server.Heartbeat += (sender, e) =>
        {
            Console.WriteLine($"💓 服务器: 心跳事件 {e.ConnectionId[..8]}");
        };

        try
        {
            // 启动服务器
            await server.StartAsync();
            Console.WriteLine("✅ 服务器已启动");
            await Task.Delay(1000);

            // 连接客户端
            if (await client.ConnectAsync())
            {
                Console.WriteLine("✅ 客户端已连接");

                // 运行一段时间观察心跳
                Console.WriteLine("观察心跳机制 (15秒)...");
                await Task.Delay(15000);

                // 模拟服务器重启
                Console.WriteLine("🔄 模拟服务器重启...");
                await server.StopAsync();
                await Task.Delay(3000);
                await server.StartAsync();
                Console.WriteLine("✅ 服务器已重启");

                // 观察客户端重连
                Console.WriteLine("观察客户端重连 (10秒)...");
                await Task.Delay(10000);

                // 检查连接状态
                var isHealthy = await client.CheckHealthAsync();
                Console.WriteLine($"最终连接健康状态: {(isHealthy ? "健康" : "异常")}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 心跳重连测试失败: {ex.Message}");
        }
        finally
        {
            await server.StopAsync();
            Console.WriteLine("✅ 测试完成");
        }
    }

    /// <summary>
    /// 运行所有集成示例
    /// </summary>
    public static async Task RunAllIntegrationExamples()
    {
        Console.WriteLine("🚀 TCP客户端-服务器集成示例");
        Console.WriteLine("=====================================");

        try
        {
            await BasicClientServerExample();
            await Task.Delay(2000);

            await MultipleClientsExample();
            await Task.Delay(2000);

            await HeartbeatReconnectIntegrationExample();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 集成示例执行失败: {ex.Message}");
        }

        Console.WriteLine("\n✅ 所有集成示例执行完成");
    }
}
