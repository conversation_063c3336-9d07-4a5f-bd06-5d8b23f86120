using System.IO.Ports;
using System.Text;

namespace Liam.SerialPort.Tests.TestHelpers;

/// <summary>
/// 模拟串口类，用于单元测试
/// </summary>
public class MockSerialPort : IDisposable
{
    private readonly Queue<byte[]> _receiveQueue = new();
    private readonly List<byte[]> _sentData = new();
    private bool _isOpen;
    private bool _disposed;

    /// <summary>
    /// 串口名称
    /// </summary>
    public string PortName { get; set; } = "COM1";

    /// <summary>
    /// 波特率
    /// </summary>
    public int BaudRate { get; set; } = 9600;

    /// <summary>
    /// 数据位
    /// </summary>
    public int DataBits { get; set; } = 8;

    /// <summary>
    /// 停止位
    /// </summary>
    public StopBits StopBits { get; set; } = StopBits.One;

    /// <summary>
    /// 校验位
    /// </summary>
    public Parity Parity { get; set; } = Parity.None;

    /// <summary>
    /// 流控制
    /// </summary>
    public Handshake Handshake { get; set; } = Handshake.None;

    /// <summary>
    /// 读取超时
    /// </summary>
    public int ReadTimeout { get; set; } = 5000;

    /// <summary>
    /// 写入超时
    /// </summary>
    public int WriteTimeout { get; set; } = 5000;

    /// <summary>
    /// 是否打开
    /// </summary>
    public bool IsOpen => _isOpen;

    /// <summary>
    /// 接收缓冲区中的字节数
    /// </summary>
    public int BytesToRead => _receiveQueue.Sum(data => data.Length);

    /// <summary>
    /// 发送缓冲区中的字节数
    /// </summary>
    public int BytesToWrite => 0;

    /// <summary>
    /// 读取缓冲区大小
    /// </summary>
    public int ReadBufferSize { get; set; } = 4096;

    /// <summary>
    /// 写入缓冲区大小
    /// </summary>
    public int WriteBufferSize { get; set; } = 2048;

    /// <summary>
    /// 换行符
    /// </summary>
    public string NewLine { get; set; } = "\r\n";

    /// <summary>
    /// 字符编码
    /// </summary>
    public Encoding Encoding { get; set; } = Encoding.UTF8;

    /// <summary>
    /// 是否丢弃空字节
    /// </summary>
    public bool DiscardNull { get; set; } = false;

    /// <summary>
    /// DTR使能
    /// </summary>
    public bool DtrEnable { get; set; } = false;

    /// <summary>
    /// RTS使能
    /// </summary>
    public bool RtsEnable { get; set; } = false;

    /// <summary>
    /// 接收字节阈值
    /// </summary>
    public int ReceivedBytesThreshold { get; set; } = 1;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    public event EventHandler<TestSerialDataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 错误接收事件
    /// </summary>
    public event EventHandler<TestSerialErrorReceivedEventArgs>? ErrorReceived;

    /// <summary>
    /// 获取已发送的数据
    /// </summary>
    public IReadOnlyList<byte[]> SentData => _sentData.AsReadOnly();

    /// <summary>
    /// 打开串口
    /// </summary>
    public void Open()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(MockSerialPort));

        if (_isOpen)
            throw new InvalidOperationException("串口已经打开");

        _isOpen = true;
    }

    /// <summary>
    /// 关闭串口
    /// </summary>
    public void Close()
    {
        _isOpen = false;
    }

    /// <summary>
    /// 写入数据
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">偏移量</param>
    /// <param name="count">字节数</param>
    public void Write(byte[] buffer, int offset, int count)
    {
        if (!_isOpen)
            throw new InvalidOperationException("串口未打开");

        if (buffer == null)
            throw new ArgumentNullException(nameof(buffer));

        if (offset < 0 || offset >= buffer.Length)
            throw new ArgumentOutOfRangeException(nameof(offset));

        if (count < 0 || offset + count > buffer.Length)
            throw new ArgumentOutOfRangeException(nameof(count));

        var data = new byte[count];
        Array.Copy(buffer, offset, data, 0, count);
        _sentData.Add(data);
    }

    /// <summary>
    /// 写入字符串
    /// </summary>
    /// <param name="text">文本</param>
    public void Write(string text)
    {
        if (text == null)
            throw new ArgumentNullException(nameof(text));

        var bytes = Encoding.GetBytes(text);
        Write(bytes, 0, bytes.Length);
    }

    /// <summary>
    /// 读取数据
    /// </summary>
    /// <param name="buffer">缓冲区</param>
    /// <param name="offset">偏移量</param>
    /// <param name="count">要读取的字节数</param>
    /// <returns>实际读取的字节数</returns>
    public int Read(byte[] buffer, int offset, int count)
    {
        if (!_isOpen)
            throw new InvalidOperationException("串口未打开");

        if (buffer == null)
            throw new ArgumentNullException(nameof(buffer));

        if (offset < 0 || offset >= buffer.Length)
            throw new ArgumentOutOfRangeException(nameof(offset));

        if (count < 0 || offset + count > buffer.Length)
            throw new ArgumentOutOfRangeException(nameof(count));

        int totalRead = 0;
        while (totalRead < count && _receiveQueue.Count > 0)
        {
            var data = _receiveQueue.Dequeue();
            int bytesToCopy = Math.Min(count - totalRead, data.Length);
            Array.Copy(data, 0, buffer, offset + totalRead, bytesToCopy);
            totalRead += bytesToCopy;

            // 如果数据没有完全读取，将剩余部分重新入队
            if (bytesToCopy < data.Length)
            {
                var remaining = new byte[data.Length - bytesToCopy];
                Array.Copy(data, bytesToCopy, remaining, 0, remaining.Length);
                var newQueue = new Queue<byte[]>();
                newQueue.Enqueue(remaining);
                while (_receiveQueue.Count > 0)
                {
                    newQueue.Enqueue(_receiveQueue.Dequeue());
                }
                _receiveQueue.Clear();
                while (newQueue.Count > 0)
                {
                    _receiveQueue.Enqueue(newQueue.Dequeue());
                }
                break;
            }
        }

        return totalRead;
    }

    /// <summary>
    /// 读取一个字节
    /// </summary>
    /// <returns>读取的字节，如果没有数据则返回-1</returns>
    public int ReadByte()
    {
        if (!_isOpen)
            throw new InvalidOperationException("串口未打开");

        if (_receiveQueue.Count == 0)
            return -1;

        var data = _receiveQueue.Dequeue();
        if (data.Length == 1)
        {
            return data[0];
        }
        else
        {
            // 将剩余数据重新入队
            var remaining = new byte[data.Length - 1];
            Array.Copy(data, 1, remaining, 0, remaining.Length);
            var newQueue = new Queue<byte[]>();
            newQueue.Enqueue(remaining);
            while (_receiveQueue.Count > 0)
            {
                newQueue.Enqueue(_receiveQueue.Dequeue());
            }
            _receiveQueue.Clear();
            while (newQueue.Count > 0)
            {
                _receiveQueue.Enqueue(newQueue.Dequeue());
            }
            return data[0];
        }
    }

    /// <summary>
    /// 清空输入缓冲区
    /// </summary>
    public void DiscardInBuffer()
    {
        _receiveQueue.Clear();
    }

    /// <summary>
    /// 清空输出缓冲区
    /// </summary>
    public void DiscardOutBuffer()
    {
        // 模拟清空输出缓冲区
    }

    /// <summary>
    /// 模拟接收数据
    /// </summary>
    /// <param name="data">接收的数据</param>
    public void SimulateDataReceived(byte[] data)
    {
        if (data == null || data.Length == 0)
            return;

        _receiveQueue.Enqueue(data);
        
        // 触发数据接收事件
        DataReceived?.Invoke(this, new TestSerialDataReceivedEventArgs(SerialData.Chars));
    }

    /// <summary>
    /// 模拟接收字符串
    /// </summary>
    /// <param name="text">接收的文本</param>
    public void SimulateDataReceived(string text)
    {
        if (string.IsNullOrEmpty(text))
            return;

        var bytes = Encoding.GetBytes(text);
        SimulateDataReceived(bytes);
    }

    /// <summary>
    /// 模拟错误
    /// </summary>
    /// <param name="errorType">错误类型</param>
    public void SimulateError(SerialError errorType)
    {
        ErrorReceived?.Invoke(this, new TestSerialErrorReceivedEventArgs(errorType));
    }

    /// <summary>
    /// 清空已发送数据记录
    /// </summary>
    public void ClearSentData()
    {
        _sentData.Clear();
    }

    /// <summary>
    /// 获取最后发送的数据
    /// </summary>
    /// <returns>最后发送的数据</returns>
    public byte[]? GetLastSentData()
    {
        return _sentData.LastOrDefault();
    }

    /// <summary>
    /// 获取最后发送的字符串
    /// </summary>
    /// <returns>最后发送的字符串</returns>
    public string? GetLastSentString()
    {
        var lastData = GetLastSentData();
        return lastData != null ? Encoding.GetString(lastData) : null;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        Close();
        _receiveQueue.Clear();
        _sentData.Clear();
        _disposed = true;
    }
}

/// <summary>
/// 测试用的串口数据接收事件参数
/// </summary>
public class TestSerialDataReceivedEventArgs : EventArgs
{
    public SerialData EventType { get; }

    public TestSerialDataReceivedEventArgs(SerialData eventType)
    {
        EventType = eventType;
    }
}

/// <summary>
/// 测试用的串口错误接收事件参数
/// </summary>
public class TestSerialErrorReceivedEventArgs : EventArgs
{
    public SerialError EventType { get; }

    public TestSerialErrorReceivedEventArgs(SerialError eventType)
    {
        EventType = eventType;
    }
}
