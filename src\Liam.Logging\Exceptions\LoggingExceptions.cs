namespace Liam.Logging.Exceptions;

/// <summary>
/// 日志记录基础异常类
/// </summary>
public class LoggingException : Exception
{
    /// <summary>
    /// 初始化日志记录异常
    /// </summary>
    public LoggingException() : base("日志记录过程中发生错误")
    {
    }

    /// <summary>
    /// 初始化日志记录异常
    /// </summary>
    /// <param name="message">错误消息</param>
    public LoggingException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化日志记录异常
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public LoggingException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// 日志提供程序异常类
/// </summary>
public class LogProviderException : LoggingException
{
    /// <summary>
    /// 提供程序名称
    /// </summary>
    public string ProviderName { get; }

    /// <summary>
    /// 初始化日志提供程序异常
    /// </summary>
    /// <param name="providerName">提供程序名称</param>
    public LogProviderException(string providerName) 
        : base($"日志提供程序 '{providerName}' 发生错误")
    {
        ProviderName = providerName;
    }

    /// <summary>
    /// 初始化日志提供程序异常
    /// </summary>
    /// <param name="providerName">提供程序名称</param>
    /// <param name="message">错误消息</param>
    public LogProviderException(string providerName, string message) 
        : base($"日志提供程序 '{providerName}': {message}")
    {
        ProviderName = providerName;
    }

    /// <summary>
    /// 初始化日志提供程序异常
    /// </summary>
    /// <param name="providerName">提供程序名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public LogProviderException(string providerName, string message, Exception innerException) 
        : base($"日志提供程序 '{providerName}': {message}", innerException)
    {
        ProviderName = providerName;
    }
}

/// <summary>
/// 日志配置异常类
/// </summary>
public class LogConfigurationException : LoggingException
{
    /// <summary>
    /// 配置节点名称
    /// </summary>
    public string? ConfigurationSection { get; }

    /// <summary>
    /// 初始化日志配置异常
    /// </summary>
    public LogConfigurationException() : base("日志配置错误")
    {
    }

    /// <summary>
    /// 初始化日志配置异常
    /// </summary>
    /// <param name="message">错误消息</param>
    public LogConfigurationException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化日志配置异常
    /// </summary>
    /// <param name="configurationSection">配置节点名称</param>
    /// <param name="message">错误消息</param>
    public LogConfigurationException(string configurationSection, string message) 
        : base($"配置节点 '{configurationSection}': {message}")
    {
        ConfigurationSection = configurationSection;
    }

    /// <summary>
    /// 初始化日志配置异常
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public LogConfigurationException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// 日志格式化异常类
/// </summary>
public class LogFormattingException : LoggingException
{
    /// <summary>
    /// 格式化器名称
    /// </summary>
    public string FormatterName { get; }

    /// <summary>
    /// 初始化日志格式化异常
    /// </summary>
    /// <param name="formatterName">格式化器名称</param>
    public LogFormattingException(string formatterName) 
        : base($"日志格式化器 '{formatterName}' 发生错误")
    {
        FormatterName = formatterName;
    }

    /// <summary>
    /// 初始化日志格式化异常
    /// </summary>
    /// <param name="formatterName">格式化器名称</param>
    /// <param name="message">错误消息</param>
    public LogFormattingException(string formatterName, string message) 
        : base($"日志格式化器 '{formatterName}': {message}")
    {
        FormatterName = formatterName;
    }

    /// <summary>
    /// 初始化日志格式化异常
    /// </summary>
    /// <param name="formatterName">格式化器名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public LogFormattingException(string formatterName, string message, Exception innerException) 
        : base($"日志格式化器 '{formatterName}': {message}", innerException)
    {
        FormatterName = formatterName;
    }
}

/// <summary>
/// 日志文件异常类
/// </summary>
public class LogFileException : LogProviderException
{
    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; }

    /// <summary>
    /// 初始化日志文件异常
    /// </summary>
    /// <param name="filePath">文件路径</param>
    public LogFileException(string filePath) 
        : base("FileProvider", $"文件操作失败: {filePath}")
    {
        FilePath = filePath;
    }

    /// <summary>
    /// 初始化日志文件异常
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="message">错误消息</param>
    public LogFileException(string filePath, string message) 
        : base("FileProvider", $"文件操作失败: {filePath} - {message}")
    {
        FilePath = filePath;
    }

    /// <summary>
    /// 初始化日志文件异常
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public LogFileException(string filePath, string message, Exception innerException) 
        : base("FileProvider", $"文件操作失败: {filePath} - {message}", innerException)
    {
        FilePath = filePath;
    }
}

/// <summary>
/// 日志队列异常类
/// </summary>
public class LogQueueException : LoggingException
{
    /// <summary>
    /// 队列名称
    /// </summary>
    public string QueueName { get; }

    /// <summary>
    /// 初始化日志队列异常
    /// </summary>
    /// <param name="queueName">队列名称</param>
    public LogQueueException(string queueName) 
        : base($"日志队列 '{queueName}' 发生错误")
    {
        QueueName = queueName;
    }

    /// <summary>
    /// 初始化日志队列异常
    /// </summary>
    /// <param name="queueName">队列名称</param>
    /// <param name="message">错误消息</param>
    public LogQueueException(string queueName, string message) 
        : base($"日志队列 '{queueName}': {message}")
    {
        QueueName = queueName;
    }

    /// <summary>
    /// 初始化日志队列异常
    /// </summary>
    /// <param name="queueName">队列名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public LogQueueException(string queueName, string message, Exception innerException) 
        : base($"日志队列 '{queueName}': {message}", innerException)
    {
        QueueName = queueName;
    }
}
