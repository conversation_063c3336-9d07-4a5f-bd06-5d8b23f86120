﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>

    <!-- NuGet包信息 -->
    <PackageId>Liam.SerialPort</PackageId>
    <Version>1.1.0</Version>
    <Authors>Liam</Authors>
    <Description>现代化串口通讯功能库，支持跨平台串口设备发现、连接管理、数据收发、热插拔检测和自动重连等功能。v1.1.0新增：Liam.Logging集成支持，提供增强的日志功能</Description>
    <PackageTags>serialport;communication;cross-platform;dotnet8;async;hotplug;reconnect</PackageTags>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <RepositoryUrl>https://gitee.com/liam-gitee/liam</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageProjectUrl>https://gitee.com/liam-gitee/liam</PackageProjectUrl>
    <PackageIcon>icon.png</PackageIcon>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.IO.Ports" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
  </ItemGroup>

  <!-- 可选的Liam.Logging集成 -->
  <ItemGroup>
    <ProjectReference Include="..\Liam.Logging\Liam.Logging.csproj" Condition="Exists('..\Liam.Logging\Liam.Logging.csproj')" />
    <PackageReference Include="Liam.Logging" Version="1.1.0" Condition="!Exists('..\Liam.Logging\Liam.Logging.csproj')" />
  </ItemGroup>

  <!-- 条件编译符号 -->
  <PropertyGroup Condition="Exists('..\Liam.Logging\Liam.Logging.csproj') OR '$(LiamLoggingAvailable)' == 'true'">
    <DefineConstants>$(DefineConstants);LIAM_LOGGING_AVAILABLE</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <None Include="..\..\icon.png" Pack="true" PackagePath="\" />
    <None Include="README.md" Pack="true" PackagePath="\" />
  </ItemGroup>

</Project>
