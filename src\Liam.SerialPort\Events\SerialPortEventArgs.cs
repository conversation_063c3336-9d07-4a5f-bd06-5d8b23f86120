using Liam.SerialPort.Models;

namespace Liam.SerialPort.Events;

/// <summary>
/// 串口事件参数基类
/// </summary>
public abstract class SerialPortEventArgs : EventArgs
{
    /// <summary>
    /// 事件发生时间
    /// </summary>
    public DateTime Timestamp { get; } = DateTime.Now;

    /// <summary>
    /// 串口名称
    /// </summary>
    public string PortName { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    protected SerialPortEventArgs(string portName)
    {
        PortName = portName ?? throw new ArgumentNullException(nameof(portName));
    }
}

/// <summary>
/// 连接状态变化事件参数
/// </summary>
public class ConnectionStatusChangedEventArgs : SerialPortEventArgs
{
    /// <summary>
    /// 旧状态
    /// </summary>
    public ConnectionStatus OldStatus { get; }

    /// <summary>
    /// 新状态
    /// </summary>
    public ConnectionStatus NewStatus { get; }

    /// <summary>
    /// 状态变化原因
    /// </summary>
    public string? Reason { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="oldStatus">旧状态</param>
    /// <param name="newStatus">新状态</param>
    /// <param name="reason">状态变化原因</param>
    public ConnectionStatusChangedEventArgs(string portName, ConnectionStatus oldStatus, ConnectionStatus newStatus, string? reason = null)
        : base(portName)
    {
        OldStatus = oldStatus;
        NewStatus = newStatus;
        Reason = reason;
    }
}

/// <summary>
/// 数据接收事件参数
/// </summary>
public class DataReceivedEventArgs : SerialPortEventArgs
{
    /// <summary>
    /// 接收到的数据
    /// </summary>
    public byte[] Data { get; }

    /// <summary>
    /// 数据长度
    /// </summary>
    public int Length => Data.Length;

    /// <summary>
    /// 数据的字符串表示（UTF-8编码）
    /// </summary>
    public string DataAsString => System.Text.Encoding.UTF8.GetString(Data);

    /// <summary>
    /// 数据的十六进制表示
    /// </summary>
    public string DataAsHex => Convert.ToHexString(Data);

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="data">接收到的数据</param>
    public DataReceivedEventArgs(string portName, byte[] data)
        : base(portName)
    {
        Data = data ?? throw new ArgumentNullException(nameof(data));
    }

    /// <summary>
    /// 获取指定编码的字符串表示
    /// </summary>
    /// <param name="encoding">字符编码</param>
    /// <returns>字符串表示</returns>
    public string GetDataAsString(System.Text.Encoding encoding)
    {
        return encoding.GetString(Data);
    }
}

/// <summary>
/// 数据发送完成事件参数
/// </summary>
public class DataSentEventArgs : SerialPortEventArgs
{
    /// <summary>
    /// 发送的数据
    /// </summary>
    public byte[] Data { get; }

    /// <summary>
    /// 数据长度
    /// </summary>
    public int Length => Data.Length;

    /// <summary>
    /// 发送是否成功
    /// </summary>
    public bool Success { get; }

    /// <summary>
    /// 发送耗时
    /// </summary>
    public TimeSpan Duration { get; }

    /// <summary>
    /// 错误信息（如果发送失败）
    /// </summary>
    public string? Error { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="data">发送的数据</param>
    /// <param name="success">发送是否成功</param>
    /// <param name="duration">发送耗时</param>
    /// <param name="error">错误信息</param>
    public DataSentEventArgs(string portName, byte[] data, bool success, TimeSpan duration, string? error = null)
        : base(portName)
    {
        Data = data ?? throw new ArgumentNullException(nameof(data));
        Success = success;
        Duration = duration;
        Error = error;
    }
}

/// <summary>
/// 串口错误事件参数
/// </summary>
public class SerialPortErrorEventArgs : SerialPortEventArgs
{
    /// <summary>
    /// 错误类型
    /// </summary>
    public SerialPortErrorType ErrorType { get; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; }

    /// <summary>
    /// 异常对象
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// 是否为致命错误
    /// </summary>
    public bool IsFatal { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="errorType">错误类型</param>
    /// <param name="message">错误消息</param>
    /// <param name="exception">异常对象</param>
    /// <param name="isFatal">是否为致命错误</param>
    public SerialPortErrorEventArgs(string portName, SerialPortErrorType errorType, string message, Exception? exception = null, bool isFatal = false)
        : base(portName)
    {
        ErrorType = errorType;
        Message = message ?? throw new ArgumentNullException(nameof(message));
        Exception = exception;
        IsFatal = isFatal;
    }
}

/// <summary>
/// 设备变化事件参数
/// </summary>
public class DeviceChangedEventArgs : EventArgs
{
    /// <summary>
    /// 变化类型
    /// </summary>
    public DeviceChangeType ChangeType { get; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public SerialPortInfo DeviceInfo { get; }

    /// <summary>
    /// 事件发生时间
    /// </summary>
    public DateTime Timestamp { get; } = DateTime.Now;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="changeType">变化类型</param>
    /// <param name="deviceInfo">设备信息</param>
    public DeviceChangedEventArgs(DeviceChangeType changeType, SerialPortInfo deviceInfo)
    {
        ChangeType = changeType;
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));
    }
}

/// <summary>
/// 串口错误类型
/// </summary>
public enum SerialPortErrorType
{
    /// <summary>
    /// 连接错误
    /// </summary>
    Connection,

    /// <summary>
    /// 读取错误
    /// </summary>
    Read,

    /// <summary>
    /// 写入错误
    /// </summary>
    Write,

    /// <summary>
    /// 超时错误
    /// </summary>
    Timeout,

    /// <summary>
    /// 设备不可用
    /// </summary>
    DeviceUnavailable,

    /// <summary>
    /// 配置错误
    /// </summary>
    Configuration,

    /// <summary>
    /// 缓冲区溢出
    /// </summary>
    BufferOverflow,

    /// <summary>
    /// 校验错误
    /// </summary>
    Parity,

    /// <summary>
    /// 帧错误
    /// </summary>
    Frame,

    /// <summary>
    /// 溢出错误
    /// </summary>
    Overrun,

    /// <summary>
    /// 未知错误
    /// </summary>
    Unknown
}

/// <summary>
/// 设备变化类型
/// </summary>
public enum DeviceChangeType
{
    /// <summary>
    /// 设备插入
    /// </summary>
    Inserted,

    /// <summary>
    /// 设备移除
    /// </summary>
    Removed,

    /// <summary>
    /// 设备状态变化
    /// </summary>
    StatusChanged
}
