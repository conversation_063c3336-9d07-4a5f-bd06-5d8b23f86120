using System.IO.Ports;

namespace Liam.SerialPort.Models;

/// <summary>
/// 串口配置设置
/// </summary>
public class SerialPortSettings
{
    /// <summary>
    /// 波特率，默认9600
    /// </summary>
    public int BaudRate { get; set; } = 9600;

    /// <summary>
    /// 数据位，默认8位
    /// </summary>
    public int DataBits { get; set; } = 8;

    /// <summary>
    /// 停止位，默认1位
    /// </summary>
    public StopBits StopBits { get; set; } = StopBits.One;

    /// <summary>
    /// 校验位，默认无校验
    /// </summary>
    public Parity Parity { get; set; } = Parity.None;

    /// <summary>
    /// 流控制，默认无流控制
    /// </summary>
    public Handshake Handshake { get; set; } = Handshake.None;

    /// <summary>
    /// 读取超时时间（毫秒），默认5000ms
    /// </summary>
    public int ReadTimeout { get; set; } = 5000;

    /// <summary>
    /// 写入超时时间（毫秒），默认5000ms
    /// </summary>
    public int WriteTimeout { get; set; } = 5000;

    /// <summary>
    /// 接收缓冲区大小，默认4096字节
    /// </summary>
    public int ReceiveBufferSize { get; set; } = 4096;

    /// <summary>
    /// 发送缓冲区大小，默认2048字节
    /// </summary>
    public int SendBufferSize { get; set; } = 2048;

    /// <summary>
    /// 是否启用DTR（数据终端就绪）信号
    /// </summary>
    public bool DtrEnable { get; set; } = false;

    /// <summary>
    /// 是否启用RTS（请求发送）信号
    /// </summary>
    public bool RtsEnable { get; set; } = false;

    /// <summary>
    /// 换行符，默认为\r\n
    /// </summary>
    public string NewLine { get; set; } = "\r\n";

    /// <summary>
    /// 字符编码，默认为UTF-8
    /// </summary>
    public System.Text.Encoding Encoding { get; set; } = System.Text.Encoding.UTF8;

    /// <summary>
    /// 是否丢弃空字节
    /// </summary>
    public bool DiscardNull { get; set; } = false;

    /// <summary>
    /// 连接重试次数，默认3次
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 连接重试间隔（毫秒），默认1000ms
    /// </summary>
    public int RetryInterval { get; set; } = 1000;

    /// <summary>
    /// 是否启用自动重连
    /// </summary>
    public bool AutoReconnect { get; set; } = true;

    /// <summary>
    /// 自动重连间隔（毫秒），默认5000ms
    /// </summary>
    public int AutoReconnectInterval { get; set; } = 5000;

    /// <summary>
    /// 最大自动重连次数，-1表示无限制
    /// </summary>
    public int MaxAutoReconnectAttempts { get; set; } = -1;

    /// <summary>
    /// 连接超时时间（毫秒），默认10000ms
    /// </summary>
    public int ConnectionTimeout { get; set; } = 10000;

    /// <summary>
    /// 心跳检测间隔（毫秒），0表示禁用心跳检测
    /// </summary>
    public int HeartbeatInterval { get; set; } = 0;

    /// <summary>
    /// 心跳数据
    /// </summary>
    public byte[] HeartbeatData { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 是否启用数据日志记录
    /// </summary>
    public bool EnableDataLogging { get; set; } = false;

    /// <summary>
    /// 数据日志最大大小（字节），默认1MB
    /// </summary>
    public long MaxLogSize { get; set; } = 1024 * 1024;

    /// <summary>
    /// 创建默认设置
    /// </summary>
    /// <returns>默认串口设置</returns>
    public static SerialPortSettings Default => new();

    /// <summary>
    /// 创建常用的9600波特率设置
    /// </summary>
    /// <returns>9600波特率设置</returns>
    public static SerialPortSettings Baud9600 => new() { BaudRate = 9600 };

    /// <summary>
    /// 创建常用的115200波特率设置
    /// </summary>
    /// <returns>115200波特率设置</returns>
    public static SerialPortSettings Baud115200 => new() { BaudRate = 115200 };

    /// <summary>
    /// 验证设置的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        if (BaudRate <= 0)
            errors.Add("波特率必须大于0");

        if (DataBits < 5 || DataBits > 8)
            errors.Add("数据位必须在5-8之间");

        if (ReadTimeout < 0)
            errors.Add("读取超时时间不能为负数");

        if (WriteTimeout < 0)
            errors.Add("写入超时时间不能为负数");

        if (ReceiveBufferSize <= 0)
            errors.Add("接收缓冲区大小必须大于0");

        if (SendBufferSize <= 0)
            errors.Add("发送缓冲区大小必须大于0");

        if (RetryCount < 0)
            errors.Add("重试次数不能为负数");

        if (RetryInterval < 0)
            errors.Add("重试间隔不能为负数");

        if (AutoReconnectInterval < 0)
            errors.Add("自动重连间隔不能为负数");

        if (ConnectionTimeout <= 0)
            errors.Add("连接超时时间必须大于0");

        if (HeartbeatInterval < 0)
            errors.Add("心跳检测间隔不能为负数");

        if (MaxLogSize <= 0)
            errors.Add("日志最大大小必须大于0");

        return new ValidationResult(errors.Count == 0, errors);
    }

    /// <summary>
    /// 克隆设置对象
    /// </summary>
    /// <returns>克隆的设置对象</returns>
    public SerialPortSettings Clone()
    {
        return new SerialPortSettings
        {
            BaudRate = BaudRate,
            DataBits = DataBits,
            StopBits = StopBits,
            Parity = Parity,
            Handshake = Handshake,
            ReadTimeout = ReadTimeout,
            WriteTimeout = WriteTimeout,
            ReceiveBufferSize = ReceiveBufferSize,
            SendBufferSize = SendBufferSize,
            DtrEnable = DtrEnable,
            RtsEnable = RtsEnable,
            NewLine = NewLine,
            Encoding = Encoding,
            DiscardNull = DiscardNull,
            RetryCount = RetryCount,
            RetryInterval = RetryInterval,
            AutoReconnect = AutoReconnect,
            AutoReconnectInterval = AutoReconnectInterval,
            MaxAutoReconnectAttempts = MaxAutoReconnectAttempts,
            ConnectionTimeout = ConnectionTimeout,
            HeartbeatInterval = HeartbeatInterval,
            HeartbeatData = (byte[])HeartbeatData.Clone(),
            EnableDataLogging = EnableDataLogging,
            MaxLogSize = MaxLogSize
        };
    }

    /// <summary>
    /// 重写ToString方法
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $"BaudRate={BaudRate}, DataBits={DataBits}, StopBits={StopBits}, Parity={Parity}, Handshake={Handshake}";
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public IReadOnlyList<string> Errors { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="isValid">是否有效</param>
    /// <param name="errors">错误信息列表</param>
    public ValidationResult(bool isValid, IEnumerable<string> errors)
    {
        IsValid = isValid;
        Errors = errors.ToList().AsReadOnly();
    }
}
