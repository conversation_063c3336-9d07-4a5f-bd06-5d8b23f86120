using Liam.Cryptography.Models;
using Liam.Cryptography.Services;
using Liam.Cryptography.Tests.Fixtures;

namespace Liam.Cryptography.Tests.Models;

/// <summary>
/// 密钥对模型测�?/// </summary>
[Collection("Crypto Tests")]
public class KeyPairTests
{
    private readonly CryptoTestFixture _fixture;

    public KeyPairTests(CryptoTestFixture fixture)
    {
        _fixture = fixture;
    }

    #region 构造函数测�?
    [Fact]
    public void Constructor_ValidParameters_ShouldCreateKeyPair()
    {
        // Arrange
        var publicKey = "test-public-key";
        var privateKey = "test-private-key";
        var keySize = 2048;

        // Act
        var keyPair = new KeyPair
        {
            PublicKey = publicKey,
            PrivateKey = privateKey,
            KeySize = keySize
        };

        // Assert
        keyPair.PublicKey.Should().Be(publicKey);
        keyPair.PrivateKey.Should().Be(privateKey);
        keyPair.KeySize.Should().Be(keySize);
    }

    [Fact]
    public void Constructor_DefaultValues_ShouldHaveExpectedDefaults()
    {
        // Act
        var keyPair = new KeyPair();

        // Assert
        keyPair.PublicKey.Should().BeNull();
        keyPair.PrivateKey.Should().BeNull();
        keyPair.KeySize.Should().Be(0);
    }

    #endregion

    #region IsValid方法测试

    [Fact]
    public void IsValid_ValidKeyPair_ShouldReturnTrue()
    {
        // Arrange
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var isValid = keyPair.IsValid();

        // Assert
        isValid.Should().BeTrue();
    }

    [Theory]
    [InlineData(null, "private-key", 2048)]
    [InlineData("public-key", null, 2048)]
    [InlineData("", "private-key", 2048)]
    [InlineData("public-key", "", 2048)]
    [InlineData("public-key", "private-key", 0)]
    [InlineData("public-key", "private-key", -1)]
    public void IsValid_InvalidKeyPair_ShouldReturnFalse(string? publicKey, string? privateKey, int keySize)
    {
        // Arrange
        var keyPair = new KeyPair
        {
            PublicKey = publicKey!,
            PrivateKey = privateKey!,
            KeySize = keySize
        };

        // Act
        var isValid = keyPair.IsValid();

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void IsValid_EmptyStrings_ShouldReturnFalse()
    {
        // Arrange
        var keyPair = new KeyPair
        {
            PublicKey = string.Empty,
            PrivateKey = string.Empty,
            KeySize = 2048
        };

        // Act
        var isValid = keyPair.IsValid();

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void IsValid_WhitespaceStrings_ShouldReturnFalse()
    {
        // Arrange
        var keyPair = new KeyPair
        {
            PublicKey = "   ",
            PrivateKey = "   ",
            KeySize = 2048
        };

        // Act
        var isValid = keyPair.IsValid();

        // Assert
        isValid.Should().BeFalse();
    }

    #endregion

    #region ClearPrivateKey方法测试

    [Fact]
    public void ClearPrivateKey_ValidKeyPair_ShouldClearPrivateKeyOnly()
    {
        // Arrange
        var rsaService = new RsaAsymmetricCrypto();
        var keyPair = rsaService.GenerateKeyPair(2048);
        var originalPublicKey = keyPair.PublicKey;

        // Act
        keyPair.ClearPrivateKey();

        // Assert
        keyPair.PrivateKey.Should().BeNull();
        keyPair.PublicKey.Should().Be(originalPublicKey); // 公钥应该保持不变
        keyPair.KeySize.Should().Be(2048); // 密钥大小应该保持不变
    }

    [Fact]
    public void ClearPrivateKey_AlreadyNullPrivateKey_ShouldNotThrow()
    {
        // Arrange
        var keyPair = new KeyPair
        {
            PublicKey = "test-public-key",
            PrivateKey = null,
            KeySize = 2048
        };

        // Act & Assert
        var action = () => keyPair.ClearPrivateKey();
        action.Should().NotThrow();
        keyPair.PrivateKey.Should().BeNull();
    }

    [Fact]
    public void ClearPrivateKey_AfterClear_IsValidShouldReturnFalse()
    {
        // Arrange - 创建一个新的密钥对，不使用共享的测试固定装置
        var rsaService = new RsaAsymmetricCrypto();
        var keyPair = rsaService.GenerateKeyPair(2048);
        keyPair.IsValid().Should().BeTrue(); // 确保开始时是有效的

        // Act
        keyPair.ClearPrivateKey();

        // Assert
        keyPair.IsValid().Should().BeFalse(); // 清除私钥后应该无效
    }

    #endregion

    #region GetPublicKeyOnly方法测试

    [Fact]
    public void GetPublicKeyOnly_ValidKeyPair_ShouldReturnPublicKeyOnlyPair()
    {
        // Arrange
        var originalKeyPair = _fixture.TestRsaKeyPair;

        // Act
        var publicKeyOnlyPair = originalKeyPair.GetPublicKeyOnly();

        // Assert
        publicKeyOnlyPair.Should().NotBeNull();
        publicKeyOnlyPair.PublicKey.Should().Be(originalKeyPair.PublicKey!);
        publicKeyOnlyPair.PrivateKey.Should().BeNull();
        publicKeyOnlyPair.KeySize.Should().Be(originalKeyPair.KeySize);
    }

    [Fact]
    public void GetPublicKeyOnly_ShouldNotAffectOriginalKeyPair()
    {
        // Arrange
        var originalKeyPair = _fixture.TestRsaKeyPair;
        var originalPrivateKey = originalKeyPair.PrivateKey;

        // Act
        var publicKeyOnlyPair = originalKeyPair.GetPublicKeyOnly();

        // Assert
        originalKeyPair.PrivateKey.Should().Be(originalPrivateKey); // 原始密钥对不应该被修改
        publicKeyOnlyPair.PrivateKey.Should().BeNull();
    }

    [Fact]
    public void GetPublicKeyOnly_NullPublicKey_ShouldReturnKeyPairWithNullPublicKey()
    {
        // Arrange
        var keyPair = new KeyPair
        {
            PublicKey = null,
            PrivateKey = "test-private-key",
            KeySize = 2048
        };

        // Act
        var publicKeyOnlyPair = keyPair.GetPublicKeyOnly();

        // Assert
        publicKeyOnlyPair.PublicKey.Should().BeNull();
        publicKeyOnlyPair.PrivateKey.Should().BeNull();
        publicKeyOnlyPair.KeySize.Should().Be(2048);
    }

    #endregion

    #region 属性设置测试
    [Fact]
    public void Properties_SetAndGet_ShouldWorkCorrectly()
    {
        // Arrange
        var keyPair = new KeyPair();
        var publicKey = "test-public-key";
        var privateKey = "test-private-key";
        var keySize = 2048;

        // Act
        keyPair.PublicKey = publicKey;
        keyPair.PrivateKey = privateKey;
        keyPair.KeySize = keySize;

        // Assert
        keyPair.PublicKey.Should().Be(publicKey);
        keyPair.PrivateKey.Should().Be(privateKey);
        keyPair.KeySize.Should().Be(keySize);
    }

    [Fact]
    public void Properties_SetToNull_ShouldAcceptNullValues()
    {
        // Arrange
        var keyPair = new KeyPair
        {
            PublicKey = "test-public-key",
            PrivateKey = "test-private-key",
            KeySize = 2048
        };

        // Act
        keyPair.PublicKey = null;
        keyPair.PrivateKey = null;

        // Assert
        keyPair.PublicKey.Should().BeNull();
        keyPair.PrivateKey.Should().BeNull();
    }

    #endregion

    #region 边界条件测试

    [Theory]
    [InlineData(1024)]
    [InlineData(2048)]
    [InlineData(3072)]
    [InlineData(4096)]
    public void KeyPair_DifferentKeySizes_ShouldWorkCorrectly(int keySize)
    {
        // Arrange
        var rsaService = new RsaAsymmetricCrypto();

        // Act
        var keyPair = rsaService.GenerateKeyPair(keySize);

        // Assert
        keyPair.KeySize.Should().Be(keySize);
        keyPair.IsValid().Should().BeTrue();
    }

    [Fact]
    public void KeyPair_VeryLongKeys_ShouldHandleCorrectly()
    {
        // Arrange
        var veryLongKey = new string('A', 10000); // 10KB的密钥字符串
        var keyPair = new KeyPair
        {
            PublicKey = veryLongKey,
            PrivateKey = veryLongKey,
            KeySize = 2048
        };

        // Act & Assert
        keyPair.PublicKey.Should().Be(veryLongKey);
        keyPair.PrivateKey.Should().Be(veryLongKey);
        keyPair.IsValid().Should().BeTrue();
    }

    #endregion

    #region 实际密钥对测试
    [Fact]
    public void KeyPair_RealRsaKeyPair_ShouldPassValidation()
    {
        // Arrange
        var rsaService = new RsaAsymmetricCrypto();
        var keyPair = rsaService.GenerateKeyPair(2048);

        // Act & Assert
        keyPair.IsValid().Should().BeTrue();
        keyPair.PublicKey.Should().StartWith("-----BEGIN PUBLIC KEY-----");
        keyPair.PublicKey.Should().EndWith("-----END PUBLIC KEY-----");
        keyPair.PrivateKey.Should().StartWith("-----BEGIN PRIVATE KEY-----");
        keyPair.PrivateKey.Should().EndWith("-----END PRIVATE KEY-----");
    }

    [Fact]
    public void KeyPair_AfterEncryptionDecryption_ShouldStillBeValid()
    {
        // Arrange
        var rsaService = new RsaAsymmetricCrypto();
        var keyPair = rsaService.GenerateKeyPair(2048);
        var plainText = "Test message for encryption";

        // Act
        var encrypted = rsaService.EncryptWithPublicKey(plainText, keyPair.PublicKey!);
        var decrypted = rsaService.DecryptWithPrivateKey(encrypted, keyPair.PrivateKey!);

        // Assert
        keyPair.IsValid().Should().BeTrue();
        decrypted.Should().Be(plainText);
    }

    #endregion
}
