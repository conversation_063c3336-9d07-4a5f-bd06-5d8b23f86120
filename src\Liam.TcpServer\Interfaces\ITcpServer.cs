using Liam.TcpServer.Events;
using <PERSON>.TcpServer.Models;

namespace Liam.TcpServer.Interfaces;

/// <summary>
/// TCP服务器接口
/// </summary>
public interface ITcpServer : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 服务器配置
    /// </summary>
    TcpServerConfig Configuration { get; }

    /// <summary>
    /// 服务器是否正在运行
    /// </summary>
    bool IsRunning { get; }

    /// <summary>
    /// 当前连接数
    /// </summary>
    int ConnectionCount { get; }

    /// <summary>
    /// 服务器状态
    /// </summary>
    string Status { get; }

    /// <summary>
    /// 服务器启动时间
    /// </summary>
    DateTime? StartedAt { get; }

    /// <summary>
    /// 服务器运行时间
    /// </summary>
    TimeSpan? Uptime { get; }

    /// <summary>
    /// 客户端连接事件
    /// </summary>
    event EventHandler<ClientConnectedEventArgs>? ClientConnected;

    /// <summary>
    /// 客户端断开连接事件
    /// </summary>
    event EventHandler<ClientDisconnectedEventArgs>? ClientDisconnected;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    event EventHandler<DataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 数据发送事件
    /// </summary>
    event EventHandler<DataSentEventArgs>? DataSent;

    /// <summary>
    /// 错误事件
    /// </summary>
    event EventHandler<TcpServerErrorEventArgs>? Error;

    /// <summary>
    /// 服务器状态变更事件
    /// </summary>
    event EventHandler<ServerStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 心跳事件
    /// </summary>
    event EventHandler<HeartbeatEventArgs>? Heartbeat;

    /// <summary>
    /// 启动服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 重启服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重启任务</returns>
    Task RestartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据到指定客户端
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendAsync(string connectionId, byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送文本到指定客户端
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="text">文本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendTextAsync(string connectionId, string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送消息到指定客户端
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendMessageAsync(string connectionId, TcpMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 广播数据到所有客户端
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<int> BroadcastAsync(byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 广播文本到所有客户端
    /// </summary>
    /// <param name="text">文本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<int> BroadcastTextAsync(string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// 广播消息到所有客户端
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<int> BroadcastMessageAsync(TcpMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 断开指定客户端连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="reason">断开原因</param>
    /// <returns>是否成功断开</returns>
    Task<bool> DisconnectClientAsync(string connectionId, string? reason = null);

    /// <summary>
    /// 获取客户端连接信息
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>客户端连接信息</returns>
    ClientConnection? GetConnection(string connectionId);

    /// <summary>
    /// 获取所有客户端连接信息
    /// </summary>
    /// <returns>所有客户端连接信息</returns>
    IReadOnlyList<ClientConnection> GetAllConnections();

    /// <summary>
    /// 获取活跃的客户端连接信息
    /// </summary>
    /// <returns>活跃的客户端连接信息</returns>
    IReadOnlyList<ClientConnection> GetActiveConnections();

    /// <summary>
    /// 获取服务器统计信息
    /// </summary>
    /// <returns>服务器统计信息</returns>
    ServerStatistics GetStatistics();

    /// <summary>
    /// 检查客户端连接是否存在
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>是否存在</returns>
    bool HasConnection(string connectionId);

    /// <summary>
    /// 检查客户端是否已连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>是否已连接</returns>
    bool IsClientConnected(string connectionId);

    /// <summary>
    /// 清理断开的连接
    /// </summary>
    /// <returns>清理的连接数</returns>
    Task<int> CleanupDisconnectedConnectionsAsync();

    /// <summary>
    /// 发送心跳到指定客户端
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<bool> SendHeartbeatAsync(string connectionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送心跳到所有客户端
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task<int> SendHeartbeatToAllAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 服务器统计信息
/// </summary>
public class ServerStatistics
{
    /// <summary>
    /// 总连接数
    /// </summary>
    public long TotalConnections { get; set; }

    /// <summary>
    /// 当前连接数
    /// </summary>
    public int CurrentConnections { get; set; }

    /// <summary>
    /// 最大并发连接数
    /// </summary>
    public int MaxConcurrentConnections { get; set; }

    /// <summary>
    /// 总发送字节数
    /// </summary>
    public long TotalBytesSent { get; set; }

    /// <summary>
    /// 总接收字节数
    /// </summary>
    public long TotalBytesReceived { get; set; }

    /// <summary>
    /// 总发送消息数
    /// </summary>
    public long TotalMessagesSent { get; set; }

    /// <summary>
    /// 总接收消息数
    /// </summary>
    public long TotalMessagesReceived { get; set; }

    /// <summary>
    /// 总错误数
    /// </summary>
    public long TotalErrors { get; set; }

    /// <summary>
    /// 服务器启动时间
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// 服务器运行时间
    /// </summary>
    public TimeSpan? Uptime { get; set; }

    /// <summary>
    /// 平均连接持续时间
    /// </summary>
    public TimeSpan AverageConnectionDuration { get; set; }

    /// <summary>
    /// 平均发送速度（字节/秒）
    /// </summary>
    public double AverageSendRate { get; set; }

    /// <summary>
    /// 平均接收速度（字节/秒）
    /// </summary>
    public double AverageReceiveRate { get; set; }

    /// <summary>
    /// 错误率
    /// </summary>
    public double ErrorRate { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; }
}
