namespace Liam.Cryptography.Interfaces;

/// <summary>
/// 对称加密接口，提供对称加密和解密功能
/// </summary>
public interface ISymmetricCrypto
{
    /// <summary>
    /// 加密数据
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <returns>加密后的数据</returns>
    byte[] Encrypt(string plainText, byte[] key, byte[]? iv = null);

    /// <summary>
    /// 加密数据（异步）
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加密后的数据</returns>
    Task<byte[]> EncryptAsync(string plainText, byte[] key, byte[]? iv = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 解密数据
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <returns>解密后的明文</returns>
    string Decrypt(byte[] cipherData, byte[] key, byte[]? iv = null);

    /// <summary>
    /// 解密数据（异步）
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解密后的明文</returns>
    Task<string> DecryptAsync(byte[] cipherData, byte[] key, byte[]? iv = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 生成随机密钥
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>生成的密钥</returns>
    byte[] GenerateKey(int keySize = 256);

    /// <summary>
    /// 生成随机初始化向量
    /// </summary>
    /// <returns>生成的初始化向量</returns>
    byte[] GenerateIV();
}
