namespace Liam.SerialPort.Models;

/// <summary>
/// 连接状态枚举
/// </summary>
public enum ConnectionStatus
{
    /// <summary>
    /// 未连接
    /// </summary>
    Disconnected = 0,

    /// <summary>
    /// 正在连接
    /// </summary>
    Connecting = 1,

    /// <summary>
    /// 已连接
    /// </summary>
    Connected = 2,

    /// <summary>
    /// 正在断开连接
    /// </summary>
    Disconnecting = 3,

    /// <summary>
    /// 连接错误
    /// </summary>
    Error = 4,

    /// <summary>
    /// 正在重连
    /// </summary>
    Reconnecting = 5,

    /// <summary>
    /// 连接超时
    /// </summary>
    Timeout = 6,

    /// <summary>
    /// 设备不可用
    /// </summary>
    DeviceUnavailable = 7
}

/// <summary>
/// 连接状态扩展方法
/// </summary>
public static class ConnectionStatusExtensions
{
    /// <summary>
    /// 检查是否为连接状态
    /// </summary>
    /// <param name="status">连接状态</param>
    /// <returns>是否为连接状态</returns>
    public static bool IsConnected(this ConnectionStatus status)
    {
        return status == ConnectionStatus.Connected;
    }

    /// <summary>
    /// 检查是否为断开状态
    /// </summary>
    /// <param name="status">连接状态</param>
    /// <returns>是否为断开状态</returns>
    public static bool IsDisconnected(this ConnectionStatus status)
    {
        return status == ConnectionStatus.Disconnected;
    }

    /// <summary>
    /// 检查是否为过渡状态（正在连接或断开）
    /// </summary>
    /// <param name="status">连接状态</param>
    /// <returns>是否为过渡状态</returns>
    public static bool IsTransitioning(this ConnectionStatus status)
    {
        return status == ConnectionStatus.Connecting ||
               status == ConnectionStatus.Disconnecting ||
               status == ConnectionStatus.Reconnecting;
    }

    /// <summary>
    /// 检查是否为错误状态
    /// </summary>
    /// <param name="status">连接状态</param>
    /// <returns>是否为错误状态</returns>
    public static bool IsError(this ConnectionStatus status)
    {
        return status == ConnectionStatus.Error ||
               status == ConnectionStatus.Timeout ||
               status == ConnectionStatus.DeviceUnavailable;
    }

    /// <summary>
    /// 获取状态的中文描述
    /// </summary>
    /// <param name="status">连接状态</param>
    /// <returns>中文描述</returns>
    public static string GetDescription(this ConnectionStatus status)
    {
        return status switch
        {
            ConnectionStatus.Disconnected => "未连接",
            ConnectionStatus.Connecting => "正在连接",
            ConnectionStatus.Connected => "已连接",
            ConnectionStatus.Disconnecting => "正在断开连接",
            ConnectionStatus.Error => "连接错误",
            ConnectionStatus.Reconnecting => "正在重连",
            ConnectionStatus.Timeout => "连接超时",
            ConnectionStatus.DeviceUnavailable => "设备不可用",
            _ => "未知状态"
        };
    }

    /// <summary>
    /// 检查是否可以执行连接操作
    /// </summary>
    /// <param name="status">连接状态</param>
    /// <returns>是否可以连接</returns>
    public static bool CanConnect(this ConnectionStatus status)
    {
        return status == ConnectionStatus.Disconnected ||
               status == ConnectionStatus.Error ||
               status == ConnectionStatus.Timeout ||
               status == ConnectionStatus.DeviceUnavailable;
    }

    /// <summary>
    /// 检查是否可以执行断开操作
    /// </summary>
    /// <param name="status">连接状态</param>
    /// <returns>是否可以断开</returns>
    public static bool CanDisconnect(this ConnectionStatus status)
    {
        return status == ConnectionStatus.Connected ||
               status == ConnectionStatus.Connecting ||
               status == ConnectionStatus.Reconnecting;
    }

    /// <summary>
    /// 检查是否可以发送数据
    /// </summary>
    /// <param name="status">连接状态</param>
    /// <returns>是否可以发送数据</returns>
    public static bool CanSendData(this ConnectionStatus status)
    {
        return status == ConnectionStatus.Connected;
    }
}

/// <summary>
/// 连接统计信息
/// </summary>
public class ConnectionStatistics
{
    /// <summary>
    /// 连接建立时间
    /// </summary>
    public DateTime? ConnectedAt { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime? LastActivity { get; set; }

    /// <summary>
    /// 连接持续时间
    /// </summary>
    public TimeSpan? Duration => ConnectedAt.HasValue ? DateTime.Now - ConnectedAt.Value : null;

    /// <summary>
    /// 发送字节数
    /// </summary>
    public long BytesSent { get; set; }

    /// <summary>
    /// 接收字节数
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 发送消息数
    /// </summary>
    public long MessagesSent { get; set; }

    /// <summary>
    /// 接收消息数
    /// </summary>
    public long MessagesReceived { get; set; }

    /// <summary>
    /// 连接尝试次数
    /// </summary>
    public int ConnectionAttempts { get; set; }

    /// <summary>
    /// 重连次数
    /// </summary>
    public int ReconnectionCount { get; set; }

    /// <summary>
    /// 错误次数
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// 最后错误时间
    /// </summary>
    public DateTime? LastErrorAt { get; set; }

    /// <summary>
    /// 最后错误信息
    /// </summary>
    public string? LastError { get; set; }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void Reset()
    {
        ConnectedAt = null;
        LastActivity = null;
        BytesSent = 0;
        BytesReceived = 0;
        MessagesSent = 0;
        MessagesReceived = 0;
        ConnectionAttempts = 0;
        ReconnectionCount = 0;
        ErrorCount = 0;
        LastErrorAt = null;
        LastError = null;
    }

    /// <summary>
    /// 记录发送数据
    /// </summary>
    /// <param name="byteCount">字节数</param>
    public void RecordSent(int byteCount)
    {
        BytesSent += byteCount;
        MessagesSent++;
        LastActivity = DateTime.Now;
    }

    /// <summary>
    /// 记录接收数据
    /// </summary>
    /// <param name="byteCount">字节数</param>
    public void RecordReceived(int byteCount)
    {
        BytesReceived += byteCount;
        MessagesReceived++;
        LastActivity = DateTime.Now;
    }

    /// <summary>
    /// 记录连接
    /// </summary>
    public void RecordConnection()
    {
        ConnectedAt = DateTime.Now;
        LastActivity = DateTime.Now;
        ConnectionAttempts++;
    }

    /// <summary>
    /// 记录重连
    /// </summary>
    public void RecordReconnection()
    {
        ReconnectionCount++;
        ConnectionAttempts++;
    }

    /// <summary>
    /// 记录错误
    /// </summary>
    /// <param name="error">错误信息</param>
    public void RecordError(string error)
    {
        ErrorCount++;
        LastErrorAt = DateTime.Now;
        LastError = error;
    }
}
