using Liam.Cryptography.Models;

namespace Liam.Cryptography.Interfaces;

/// <summary>
/// 数字签名接口
/// </summary>
public interface IDigitalSignature
{
    /// <summary>
    /// 生成数字签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>数字签名</returns>
    byte[] Sign(string data, string privateKey);

    /// <summary>
    /// 生成数字签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>数字签名</returns>
    byte[] Sign(byte[] data, string privateKey);

    /// <summary>
    /// 生成数字签名（异步）
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>数字签名</returns>
    Task<byte[]> SignAsync(string data, string privateKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// 生成数字签名（异步）
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>数字签名</returns>
    Task<byte[]> SignAsync(byte[] data, string privateKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证数字签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>验证结果</returns>
    bool Verify(string data, byte[] signature, string publicKey);

    /// <summary>
    /// 验证数字签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>验证结果</returns>
    bool Verify(byte[] data, byte[] signature, string publicKey);

    /// <summary>
    /// 验证数字签名（异步）
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">公钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<bool> VerifyAsync(string data, byte[] signature, string publicKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证数字签名（异步）
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signature">数字签名</param>
    /// <param name="publicKey">公钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<bool> VerifyAsync(byte[] data, byte[] signature, string publicKey, CancellationToken cancellationToken = default);
}
