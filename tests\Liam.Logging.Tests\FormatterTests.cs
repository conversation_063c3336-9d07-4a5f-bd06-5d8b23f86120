using FluentAssertions;
using Liam.Logging.Constants;
using Liam.Logging.Formatters;
using Liam.Logging.Models;
using System.Text.Json;
using Xunit;

namespace Liam.Logging.Tests;

/// <summary>
/// 格式化器测试
/// </summary>
public class FormatterTests
{
    [Fact]
    public void TextLogFormatter_Format_ShouldReturnFormattedString()
    {
        // Arrange
        var formatter = new TextLogFormatter();
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test message",
            Category = "TestCategory",
            Timestamp = new DateTimeOffset(2023, 1, 1, 12, 0, 0, TimeSpan.Zero),
            ThreadId = 123,
            EventId = 1001,
            EventName = "TestEvent"
        };

        // Act
        var result = formatter.Format(logEvent);

        // Assert
        result.Should().Contain("2023-01-01 12:00:00.000");
        result.Should().Contain("[INFO ]");
        result.Should().Contain("TestCategory");
        result.Should().Contain("Test message");
        result.Should().Contain("[T:123]");
        result.Should().Contain("(1001:TestEvent)");
    }

    [Fact]
    public void TextLogFormatter_Format_WithException_ShouldIncludeExceptionDetails()
    {
        // Arrange
        var formatter = new TextLogFormatter();
        var exception = new InvalidOperationException("Test exception");
        var logEvent = new LogEvent
        {
            Level = LogLevel.Error,
            Message = "Error occurred",
            Exception = exception,
            Timestamp = DateTimeOffset.Now
        };

        // Act
        var result = formatter.Format(logEvent);

        // Assert
        result.Should().Contain("Error occurred");
        result.Should().Contain("Exception:");
        result.Should().Contain("InvalidOperationException");
        result.Should().Contain("Test exception");
    }

    [Fact]
    public void TextLogFormatter_Format_WithScopes_ShouldIncludeScopeInfo()
    {
        // Arrange
        var formatter = new TextLogFormatter();
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test message",
            Timestamp = DateTimeOffset.Now,
            Scopes = new Dictionary<string, object?>
            {
                ["Scope0"] = "RequestId: 123",
                ["Scope1"] = "UserId: 456"
            }
        };

        // Act
        var result = formatter.Format(logEvent);

        // Assert
        result.Should().Contain("Scopes:");
        result.Should().Contain("Scope0=RequestId: 123");
        result.Should().Contain("Scope1=UserId: 456");
    }

    [Fact]
    public void TextLogFormatter_Format_WithProperties_ShouldIncludeProperties()
    {
        // Arrange
        var formatter = new TextLogFormatter();
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test message",
            Timestamp = DateTimeOffset.Now,
            Properties = new Dictionary<string, object?>
            {
                ["Property1"] = "Value1",
                ["Property2"] = 42
            }
        };

        // Act
        var result = formatter.Format(logEvent);

        // Assert
        result.Should().Contain("Properties:");
        result.Should().Contain("Property1=Value1");
        result.Should().Contain("Property2=42");
    }

    [Fact]
    public void TextLogFormatter_FormatToBytes_ShouldReturnUtf8Bytes()
    {
        // Arrange
        var formatter = new TextLogFormatter();
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test message",
            Timestamp = DateTimeOffset.Now
        };

        // Act
        var result = formatter.FormatToBytes(logEvent);

        // Assert
        result.Should().NotBeEmpty();
        var text = System.Text.Encoding.UTF8.GetString(result);
        text.Should().Contain("Test message");
    }

    [Fact]
    public void JsonLogFormatter_Format_ShouldReturnValidJson()
    {
        // Arrange
        var formatter = new JsonLogFormatter();
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test message",
            Category = "TestCategory",
            Timestamp = new DateTimeOffset(2023, 1, 1, 12, 0, 0, TimeSpan.Zero),
            ThreadId = 123,
            ProcessId = 456,
            MachineName = "TestMachine",
            EventId = 1001,
            EventName = "TestEvent"
        };

        // Act
        var result = formatter.Format(logEvent);

        // Assert
        result.Should().NotBeNullOrEmpty();
        
        // Verify it's valid JSON
        var jsonDocument = JsonDocument.Parse(result);
        jsonDocument.Should().NotBeNull();

        var root = jsonDocument.RootElement;
        root.GetProperty("level").GetString().Should().Be("INFO");
        root.GetProperty("message").GetString().Should().Be("Test message");
        root.GetProperty("category").GetString().Should().Be("TestCategory");
        root.GetProperty("threadId").GetInt32().Should().Be(123);
        root.GetProperty("processId").GetInt32().Should().Be(456);
        root.GetProperty("machineName").GetString().Should().Be("TestMachine");
        root.GetProperty("eventId").GetInt32().Should().Be(1001);
        root.GetProperty("eventName").GetString().Should().Be("TestEvent");
    }

    [Fact]
    public void JsonLogFormatter_Format_WithException_ShouldIncludeExceptionObject()
    {
        // Arrange
        var formatter = new JsonLogFormatter();
        var innerException = new ArgumentException("Inner exception");
        var exception = new InvalidOperationException("Test exception", innerException);
        var logEvent = new LogEvent
        {
            Level = LogLevel.Error,
            Message = "Error occurred",
            Exception = exception,
            Timestamp = DateTimeOffset.Now
        };

        // Act
        var result = formatter.Format(logEvent);

        // Assert
        var jsonDocument = JsonDocument.Parse(result);
        var root = jsonDocument.RootElement;
        
        root.TryGetProperty("exception", out var exceptionElement).Should().BeTrue();
        exceptionElement.GetProperty("type").GetString().Should().Be("System.InvalidOperationException");
        exceptionElement.GetProperty("message").GetString().Should().Be("Test exception");
        
        exceptionElement.TryGetProperty("innerException", out var innerExceptionElement).Should().BeTrue();
        innerExceptionElement.GetProperty("type").GetString().Should().Be("System.ArgumentException");
        innerExceptionElement.GetProperty("message").GetString().Should().Be("Inner exception");
    }

    [Fact]
    public void JsonLogFormatter_Format_WithStructuredData_ShouldIncludeAllFields()
    {
        // Arrange
        var formatter = new JsonLogFormatter();
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "User {UserId} performed action {Action}",
            MessageTemplate = "User {UserId} performed action {Action}",
            Parameters = new object[] { 123, "Login" },
            Timestamp = DateTimeOffset.Now,
            Scopes = new Dictionary<string, object?>
            {
                ["RequestId"] = "req-123"
            },
            Properties = new Dictionary<string, object?>
            {
                ["CustomProperty"] = "CustomValue"
            },
            Source = new LogSource
            {
                ClassName = "TestClass",
                MethodName = "TestMethod",
                FilePath = "TestFile.cs",
                LineNumber = 42
            }
        };

        // Act
        var result = formatter.Format(logEvent);

        // Assert
        var jsonDocument = JsonDocument.Parse(result);
        var root = jsonDocument.RootElement;
        
        root.TryGetProperty("messageTemplate", out _).Should().BeTrue();
        root.TryGetProperty("parameters", out _).Should().BeTrue();
        root.TryGetProperty("scopes", out _).Should().BeTrue();
        root.TryGetProperty("properties", out _).Should().BeTrue();
        root.TryGetProperty("source", out var sourceElement).Should().BeTrue();
        
        sourceElement.GetProperty("className").GetString().Should().Be("TestClass");
        sourceElement.GetProperty("methodName").GetString().Should().Be("TestMethod");
        sourceElement.GetProperty("filePath").GetString().Should().Be("TestFile.cs");
        sourceElement.GetProperty("lineNumber").GetInt32().Should().Be(42);
    }

    [Fact]
    public void JsonLogFormatter_FormatToBytes_ShouldReturnUtf8JsonBytes()
    {
        // Arrange
        var formatter = new JsonLogFormatter();
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test message",
            Timestamp = DateTimeOffset.Now
        };

        // Act
        var result = formatter.FormatToBytes(logEvent);

        // Assert
        result.Should().NotBeEmpty();
        var json = System.Text.Encoding.UTF8.GetString(result);
        
        // Verify it's valid JSON
        var jsonDocument = JsonDocument.Parse(json);
        jsonDocument.Should().NotBeNull();
    }

    [Theory]
    [InlineData(LogLevel.Trace, "TRACE")]
    [InlineData(LogLevel.Debug, "DEBUG")]
    [InlineData(LogLevel.Information, "INFO")]
    [InlineData(LogLevel.Warning, "WARN")]
    [InlineData(LogLevel.Error, "ERROR")]
    [InlineData(LogLevel.Critical, "FATAL")]
    public void TextLogFormatter_Format_ShouldUseCorrectLevelNames(LogLevel level, string expectedLevelName)
    {
        // Arrange
        var formatter = new TextLogFormatter();
        var logEvent = new LogEvent
        {
            Level = level,
            Message = "Test message",
            Timestamp = DateTimeOffset.Now
        };

        // Act
        var result = formatter.Format(logEvent);

        // Assert
        result.Should().Contain($"[{expectedLevelName}");
    }

    [Fact]
    public void TextLogFormatter_WithCustomTimestampFormat_ShouldUseCustomFormat()
    {
        // Arrange
        var customFormat = "yyyy/MM/dd HH:mm:ss";
        var formatter = new TextLogFormatter(customFormat);
        var timestamp = new DateTimeOffset(2023, 1, 1, 12, 30, 45, TimeSpan.Zero);
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test message",
            Timestamp = timestamp
        };

        // Act
        var result = formatter.Format(logEvent);

        // Assert
        result.Should().Contain("2023/01/01 12:30:45");
    }

    [Fact]
    public void JsonLogFormatter_WithCustomOptions_ShouldUseCustomOptions()
    {
        // Arrange
        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        var formatter = new JsonLogFormatter(options);
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test message",
            Timestamp = DateTimeOffset.Now
        };

        // Act
        var result = formatter.Format(logEvent);

        // Assert
        result.Should().Contain("\n"); // Indented JSON should contain newlines
        result.Should().Contain("threadId"); // Camel case property names
    }
}
