namespace Liam.TcpServer.Models;

/// <summary>
/// 连接统计信息
/// </summary>
public class ConnectionStatistics
{
    private readonly object _lock = new();

    /// <summary>
    /// 发送的字节数
    /// </summary>
    public long BytesSent { get; private set; }

    /// <summary>
    /// 接收的字节数
    /// </summary>
    public long BytesReceived { get; private set; }

    /// <summary>
    /// 发送的消息数
    /// </summary>
    public long MessagesSent { get; private set; }

    /// <summary>
    /// 接收的消息数
    /// </summary>
    public long MessagesReceived { get; private set; }

    /// <summary>
    /// 发送错误次数
    /// </summary>
    public long SendErrors { get; private set; }

    /// <summary>
    /// 接收错误次数
    /// </summary>
    public long ReceiveErrors { get; private set; }

    /// <summary>
    /// 心跳发送次数
    /// </summary>
    public long HeartbeatsSent { get; private set; }

    /// <summary>
    /// 心跳接收次数
    /// </summary>
    public long HeartbeatsReceived { get; private set; }

    /// <summary>
    /// 心跳超时次数
    /// </summary>
    public long HeartbeatTimeouts { get; private set; }

    /// <summary>
    /// 最后发送时间
    /// </summary>
    public DateTime? LastSentAt { get; private set; }

    /// <summary>
    /// 最后接收时间
    /// </summary>
    public DateTime? LastReceivedAt { get; private set; }

    /// <summary>
    /// 平均发送速度（字节/秒）
    /// </summary>
    public double AverageSendRate { get; private set; }

    /// <summary>
    /// 平均接收速度（字节/秒）
    /// </summary>
    public double AverageReceiveRate { get; private set; }

    /// <summary>
    /// 统计开始时间
    /// </summary>
    public DateTime StartTime { get; }

    /// <summary>
    /// 初始化连接统计信息
    /// </summary>
    public ConnectionStatistics()
    {
        StartTime = DateTime.UtcNow;
    }

    /// <summary>
    /// 记录发送数据
    /// </summary>
    /// <param name="bytes">发送的字节数</param>
    public void RecordSent(long bytes)
    {
        if (bytes <= 0) return;

        lock (_lock)
        {
            BytesSent += bytes;
            MessagesSent++;
            LastSentAt = DateTime.UtcNow;
            UpdateAverageRates();
        }
    }

    /// <summary>
    /// 记录接收数据
    /// </summary>
    /// <param name="bytes">接收的字节数</param>
    public void RecordReceived(long bytes)
    {
        if (bytes <= 0) return;

        lock (_lock)
        {
            BytesReceived += bytes;
            MessagesReceived++;
            LastReceivedAt = DateTime.UtcNow;
            UpdateAverageRates();
        }
    }

    /// <summary>
    /// 记录发送错误
    /// </summary>
    public void RecordSendError()
    {
        lock (_lock)
        {
            SendErrors++;
        }
    }

    /// <summary>
    /// 记录接收错误
    /// </summary>
    public void RecordReceiveError()
    {
        lock (_lock)
        {
            ReceiveErrors++;
        }
    }

    /// <summary>
    /// 记录心跳发送
    /// </summary>
    public void RecordHeartbeatSent()
    {
        lock (_lock)
        {
            HeartbeatsSent++;
        }
    }

    /// <summary>
    /// 记录心跳接收
    /// </summary>
    public void RecordHeartbeatReceived()
    {
        lock (_lock)
        {
            HeartbeatsReceived++;
        }
    }

    /// <summary>
    /// 记录心跳超时
    /// </summary>
    public void RecordHeartbeatTimeout()
    {
        lock (_lock)
        {
            HeartbeatTimeouts++;
        }
    }

    /// <summary>
    /// 更新平均速率
    /// </summary>
    private void UpdateAverageRates()
    {
        var duration = DateTime.UtcNow - StartTime;
        var totalSeconds = duration.TotalSeconds;

        if (totalSeconds > 0)
        {
            AverageSendRate = BytesSent / totalSeconds;
            AverageReceiveRate = BytesReceived / totalSeconds;
        }
    }

    /// <summary>
    /// 获取统计信息摘要
    /// </summary>
    /// <returns>统计信息摘要</returns>
    public StatisticsSummary GetSummary()
    {
        lock (_lock)
        {
            return new StatisticsSummary
            {
                BytesSent = BytesSent,
                BytesReceived = BytesReceived,
                MessagesSent = MessagesSent,
                MessagesReceived = MessagesReceived,
                SendErrors = SendErrors,
                ReceiveErrors = ReceiveErrors,
                HeartbeatsSent = HeartbeatsSent,
                HeartbeatsReceived = HeartbeatsReceived,
                HeartbeatTimeouts = HeartbeatTimeouts,
                LastSentAt = LastSentAt,
                LastReceivedAt = LastReceivedAt,
                AverageSendRate = AverageSendRate,
                AverageReceiveRate = AverageReceiveRate,
                Duration = DateTime.UtcNow - StartTime
            };
        }
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void Reset()
    {
        lock (_lock)
        {
            BytesSent = 0;
            BytesReceived = 0;
            MessagesSent = 0;
            MessagesReceived = 0;
            SendErrors = 0;
            ReceiveErrors = 0;
            HeartbeatsSent = 0;
            HeartbeatsReceived = 0;
            HeartbeatTimeouts = 0;
            LastSentAt = null;
            LastReceivedAt = null;
            AverageSendRate = 0;
            AverageReceiveRate = 0;
        }
    }

    /// <summary>
    /// 获取错误率
    /// </summary>
    /// <returns>错误率（0-1之间）</returns>
    public double GetErrorRate()
    {
        lock (_lock)
        {
            var totalOperations = MessagesSent + MessagesReceived;
            var totalErrors = SendErrors + ReceiveErrors;

            return totalOperations > 0 ? (double)totalErrors / totalOperations : 0;
        }
    }

    /// <summary>
    /// 获取心跳成功率
    /// </summary>
    /// <returns>心跳成功率（0-1之间）</returns>
    public double GetHeartbeatSuccessRate()
    {
        lock (_lock)
        {
            var totalHeartbeats = HeartbeatsSent + HeartbeatsReceived;
            var successfulHeartbeats = totalHeartbeats - HeartbeatTimeouts;

            return totalHeartbeats > 0 ? (double)successfulHeartbeats / totalHeartbeats : 1.0;
        }
    }

    /// <summary>
    /// 重写ToString方法
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        var summary = GetSummary();
        return $"Statistics: Sent={summary.BytesSent}B/{summary.MessagesSent}M, " +
               $"Received={summary.BytesReceived}B/{summary.MessagesReceived}M, " +
               $"Errors={summary.SendErrors + summary.ReceiveErrors}, " +
               $"Duration={summary.Duration:hh\\:mm\\:ss}";
    }
}

/// <summary>
/// 统计信息摘要
/// </summary>
public class StatisticsSummary
{
    /// <summary>
    /// 发送的字节数
    /// </summary>
    public long BytesSent { get; set; }

    /// <summary>
    /// 接收的字节数
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 发送的消息数
    /// </summary>
    public long MessagesSent { get; set; }

    /// <summary>
    /// 接收的消息数
    /// </summary>
    public long MessagesReceived { get; set; }

    /// <summary>
    /// 发送错误次数
    /// </summary>
    public long SendErrors { get; set; }

    /// <summary>
    /// 接收错误次数
    /// </summary>
    public long ReceiveErrors { get; set; }

    /// <summary>
    /// 心跳发送次数
    /// </summary>
    public long HeartbeatsSent { get; set; }

    /// <summary>
    /// 心跳接收次数
    /// </summary>
    public long HeartbeatsReceived { get; set; }

    /// <summary>
    /// 心跳超时次数
    /// </summary>
    public long HeartbeatTimeouts { get; set; }

    /// <summary>
    /// 最后发送时间
    /// </summary>
    public DateTime? LastSentAt { get; set; }

    /// <summary>
    /// 最后接收时间
    /// </summary>
    public DateTime? LastReceivedAt { get; set; }

    /// <summary>
    /// 平均发送速度（字节/秒）
    /// </summary>
    public double AverageSendRate { get; set; }

    /// <summary>
    /// 平均接收速度（字节/秒）
    /// </summary>
    public double AverageReceiveRate { get; set; }

    /// <summary>
    /// 统计持续时间
    /// </summary>
    public TimeSpan Duration { get; set; }
}
