using FluentAssertions;
using Liam.TcpClient.Exceptions;
using System.Net.Sockets;
using Xunit;

namespace Liam.TcpClient.Tests.Exceptions;

/// <summary>
/// TCP客户端异常测试
/// </summary>
public class TcpClientExceptionTests
{
    [Fact]
    public void TcpClientException_ShouldCreateWithMessage()
    {
        // Arrange
        var message = "Test exception message";

        // Act
        var exception = new TcpClientException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().BeNull();
    }

    [Fact]
    public void TcpClientException_ShouldCreateWithMessageAndInnerException()
    {
        // Arrange
        var message = "Test exception message";
        var innerException = new InvalidOperationException("Inner exception");

        // Act
        var exception = new TcpClientException(message, innerException);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void ConnectionException_ShouldCreateWithMessage()
    {
        // Arrange
        var message = "Connection failed";

        // Act
        var exception = new ConnectionException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().BeNull();
    }

    [Fact]
    public void ConnectionException_ShouldCreateWithMessageAndInnerException()
    {
        // Arrange
        var message = "Connection failed";
        var innerException = new SocketException();

        // Act
        var exception = new ConnectionException(message, innerException);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void MessageException_ShouldCreateWithMessage()
    {
        // Arrange
        var message = "Message operation failed";

        // Act
        var exception = new MessageException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().BeNull();
    }

    [Fact]
    public void MessageException_ShouldCreateWithMessageAndInnerException()
    {
        // Arrange
        var message = "Message operation failed";
        var innerException = new TimeoutException();

        // Act
        var exception = new MessageException(message, innerException);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void ConnectionTimeoutException_ShouldCreateWithTimeout()
    {
        // Arrange
        var timeout = TimeSpan.FromSeconds(30);

        // Act
        var exception = new ConnectionTimeoutException(timeout);

        // Assert
        exception.Message.Should().Contain("连接超时");
        exception.Timeout.Should().Be(timeout);
    }

    [Fact]
    public void ConnectionTimeoutException_ShouldCreateWithTimeoutAndMessage()
    {
        // Arrange
        var timeout = TimeSpan.FromSeconds(30);
        var message = "Custom timeout message";

        // Act
        var exception = new ConnectionTimeoutException(timeout, message);

        // Assert
        exception.Message.Should().Be(message);
        exception.Timeout.Should().Be(timeout);
    }

    [Fact]
    public void SslException_ShouldCreateWithMessage()
    {
        // Arrange
        var message = "SSL handshake failed";

        // Act
        var exception = new SslException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().BeNull();
    }

    [Fact]
    public void SslException_ShouldCreateWithMessageAndInnerException()
    {
        // Arrange
        var message = "SSL handshake failed";
        var innerException = new AuthenticationException();

        // Act
        var exception = new SslException(message, innerException);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void HeartbeatTimeoutException_ShouldCreateWithTimeout()
    {
        // Arrange
        var timeout = TimeSpan.FromSeconds(30);

        // Act
        var exception = new HeartbeatTimeoutException(timeout);

        // Assert
        exception.Message.Should().Contain("心跳超时");
        exception.Timeout.Should().Be(timeout);
    }

    [Fact]
    public void HeartbeatTimeoutException_ShouldCreateWithTimeoutAndMessage()
    {
        // Arrange
        var timeout = TimeSpan.FromSeconds(30);
        var message = "Custom heartbeat timeout message";

        // Act
        var exception = new HeartbeatTimeoutException(timeout, message);

        // Assert
        exception.Message.Should().Be(message);
        exception.Timeout.Should().Be(timeout);
    }

    [Fact]
    public void AuthenticationException_ShouldCreateWithMessage()
    {
        // Arrange
        var message = "Authentication failed";

        // Act
        var exception = new AuthenticationException(message);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().BeNull();
    }

    [Fact]
    public void AuthenticationException_ShouldCreateWithMessageAndInnerException()
    {
        // Arrange
        var message = "Authentication failed";
        var innerException = new ConnectionException("Connection failed");

        // Act
        var exception = new AuthenticationException(message, innerException);

        // Assert
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void TcpClientException_ShouldInheritFromException()
    {
        // Act
        var exception = new TcpClientException("Test");

        // Assert
        exception.Should().BeAssignableTo<Exception>();
    }

    [Fact]
    public void ConnectionException_ShouldInheritFromTcpClientExceptionBase()
    {
        // Act
        var exception = new ConnectionException("Test");

        // Assert
        exception.Should().BeAssignableTo<TcpClientExceptionBase>();
        exception.Category.Should().Be(TcpClientExceptionCategory.Network);
    }

    [Fact]
    public void MessageException_ShouldInheritFromTcpClientException()
    {
        // Act
        var exception = new MessageException("Test");

        // Assert
        exception.Should().BeAssignableTo<TcpClientException>();
    }

    [Fact]
    public void AuthenticationException_ShouldInheritFromTcpClientExceptionBase()
    {
        // Act
        var exception = new AuthenticationException("Test");

        // Assert
        exception.Should().BeAssignableTo<TcpClientExceptionBase>();
        exception.Category.Should().Be(TcpClientExceptionCategory.Authentication);
    }

    [Fact]
    public void SslException_ShouldInheritFromTcpClientException()
    {
        // Act
        var exception = new SslException("Test");

        // Assert
        exception.Should().BeAssignableTo<TcpClientException>();
    }
}
