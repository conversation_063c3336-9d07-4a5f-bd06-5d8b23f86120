using FluentAssertions;
using Liam.SerialPort.Extensions;
using Liam.SerialPort.Models;
using Liam.SerialPort.Tests.TestHelpers;
using System.IO.Ports;
using System.Text;
using Xunit;

namespace Liam.SerialPort.Tests.Extensions;

/// <summary>
/// SerialPortExtensions 测试类
/// </summary>
public class SerialPortExtensionsTests : SerialPortTestBase
{
    [Theory]
    [InlineData("48656C6C6F", new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F })]
    [InlineData("48 65 6C 6C 6F", new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F })]
    [InlineData("48-65-6C-6C-6F", new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F })]
    [InlineData("48:65:6C:6C:6F", new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F })]
    [InlineData("48,65,6C,6C,6F", new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F })]
    [InlineData("", new byte[0])]
    [InlineData("00", new byte[] { 0x00 })]
    [InlineData("FF", new byte[] { 0xFF })]
    public void HexStringToBytes_ValidInput_ShouldReturnCorrectBytes(string hexString, byte[] expected)
    {
        // Act
        var result = SerialPortExtensions.HexStringToBytes(hexString);

        // Assert
        result.Should().BeEquivalentTo(expected);
    }

    [Theory]
    [InlineData("48656C6C6")]  // 奇数长度
    public void HexStringToBytes_OddLength_ShouldThrowArgumentException(string hexString)
    {
        // Act & Assert
        var act = () => SerialPortExtensions.HexStringToBytes(hexString);
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData("GG")]        // 无效字符
    [InlineData("4G")]        // 部分无效字符
    public void HexStringToBytes_InvalidCharacters_ShouldThrowFormatException(string hexString)
    {
        // Act & Assert
        var act = () => SerialPortExtensions.HexStringToBytes(hexString);
        act.Should().Throw<FormatException>();
    }

    [Fact]
    public void HexStringToBytes_NullOrWhiteSpace_ShouldReturnEmptyArray()
    {
        // Act & Assert
        SerialPortExtensions.HexStringToBytes(null!).Should().BeEmpty();
        SerialPortExtensions.HexStringToBytes("").Should().BeEmpty();
        SerialPortExtensions.HexStringToBytes("   ").Should().BeEmpty();
    }

    [Theory]
    [InlineData(new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F }, " ", "48 65 6C 6C 6F")]
    [InlineData(new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F }, "-", "48-65-6C-6C-6F")]
    [InlineData(new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F }, "", "48656C6C6F")]
    [InlineData(new byte[] { 0x00, 0xFF }, " ", "00 FF")]
    [InlineData(new byte[0], " ", "")]
    public void ToHexString_ShouldReturnCorrectString(byte[] bytes, string separator, string expected)
    {
        // Act
        var result = bytes.ToHexString(separator);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void ToHexString_NullArray_ShouldReturnEmptyString()
    {
        // Arrange
        byte[] bytes = null!;

        // Act
        var result = bytes.ToHexString();

        // Assert
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, new byte[] { 0x02, 0x03 }, true)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, new byte[] { 0x01, 0x02 }, true)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, new byte[] { 0x01, 0x02, 0x03 }, true)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, new byte[] { 0x02, 0x04 }, false)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, new byte[] { 0x01, 0x02, 0x03, 0x04 }, false)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, new byte[0], false)]
    public void ContainsPattern_ShouldReturnCorrectResult(byte[] data, byte[] pattern, bool expected)
    {
        // Act
        var result = data.ContainsPattern(pattern);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void ContainsPattern_NullInputs_ShouldReturnFalse()
    {
        // Arrange
        byte[] data = null!;
        byte[] pattern = null!;

        // Act & Assert
        data.ContainsPattern(new byte[] { 0x01 }).Should().BeFalse();
        new byte[] { 0x01 }.ContainsPattern(pattern).Should().BeFalse();
        data.ContainsPattern(pattern).Should().BeFalse();
    }

    [Theory]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, new byte[] { 0x02, 0x03 }, 1)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, new byte[] { 0x01, 0x02 }, 0)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, new byte[] { 0x01, 0x02, 0x03 }, 0)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, new byte[] { 0x02, 0x04 }, -1)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, new byte[] { 0x01, 0x02, 0x03, 0x04 }, -1)]
    public void FindPattern_ShouldReturnCorrectIndex(byte[] data, byte[] pattern, int expected)
    {
        // Act
        var result = data.FindPattern(pattern);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, ChecksumType.Sum, 6)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, ChecksumType.Xor, 0)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03 }, ChecksumType.TwosComplement, 250)]
    [InlineData(new byte[] { 0xFF, 0xFF }, ChecksumType.Sum, 254)]
    [InlineData(new byte[0], ChecksumType.Sum, 0)]
    public void CalculateChecksum_ShouldReturnCorrectValue(byte[] data, ChecksumType checksumType, byte expected)
    {
        // Act
        var result = data.CalculateChecksum(checksumType);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData(new byte[] { 0x01, 0x02, 0x03, 0x06 }, ChecksumType.Sum, true)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03, 0x00 }, ChecksumType.Xor, true)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03, 0xFA }, ChecksumType.TwosComplement, true)]
    [InlineData(new byte[] { 0x01, 0x02, 0x03, 0x07 }, ChecksumType.Sum, false)]
    [InlineData(new byte[] { 0x01 }, ChecksumType.Sum, false)] // 长度不足
    public void VerifyChecksum_ShouldReturnCorrectResult(byte[] data, ChecksumType checksumType, bool expected)
    {
        // Act
        var result = data.VerifyChecksum(checksumType);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void VerifyChecksum_NullOrShortArray_ShouldReturnFalse()
    {
        // Act & Assert
        ((byte[])null!).VerifyChecksum().Should().BeFalse();
        new byte[0].VerifyChecksum().Should().BeFalse();
        new byte[] { 0x01 }.VerifyChecksum().Should().BeFalse();
    }

    [Theory]
    [InlineData(9600, 8, StopBits.One, Parity.None)]
    [InlineData(115200, 7, StopBits.Two, Parity.Even)]
    [InlineData(38400, 6, StopBits.OnePointFive, Parity.Odd)]
    public void CreateSettings_ShouldReturnCorrectSettings(int baudRate, int dataBits, StopBits stopBits, Parity parity)
    {
        // Act
        var settings = SerialPortExtensions.CreateSettings(baudRate, dataBits, stopBits, parity);

        // Assert
        settings.Should().NotBeNull();
        settings.BaudRate.Should().Be(baudRate);
        settings.DataBits.Should().Be(dataBits);
        settings.StopBits.Should().Be(stopBits);
        settings.Parity.Should().Be(parity);
    }

    [Fact]
    public void CreateHighSpeedSettings_ShouldReturnCorrectSettings()
    {
        // Act
        var settings = SerialPortExtensions.CreateHighSpeedSettings();

        // Assert
        settings.Should().NotBeNull();
        settings.BaudRate.Should().Be(115200);
        settings.DataBits.Should().Be(8);
        settings.StopBits.Should().Be(StopBits.One);
        settings.Parity.Should().Be(Parity.None);
        settings.ReadTimeout.Should().Be(1000);
        settings.WriteTimeout.Should().Be(1000);
        settings.ReceiveBufferSize.Should().Be(8192);
        settings.SendBufferSize.Should().Be(4096);
    }

    [Fact]
    public void CreateLowSpeedSettings_ShouldReturnCorrectSettings()
    {
        // Act
        var settings = SerialPortExtensions.CreateLowSpeedSettings();

        // Assert
        settings.Should().NotBeNull();
        settings.BaudRate.Should().Be(9600);
        settings.DataBits.Should().Be(8);
        settings.StopBits.Should().Be(StopBits.One);
        settings.Parity.Should().Be(Parity.None);
        settings.ReadTimeout.Should().Be(5000);
        settings.WriteTimeout.Should().Be(5000);
        settings.ReceiveBufferSize.Should().Be(1024);
        settings.SendBufferSize.Should().Be(512);
    }

    [Theory]
    [InlineData(75, true)]      // 最小值
    [InlineData(9600, true)]    // 常用值
    [InlineData(115200, true)]  // 常用值
    [InlineData(4000000, true)] // 最大值
    [InlineData(74, false)]     // 小于最小值
    [InlineData(4000001, false)] // 大于最大值
    [InlineData(0, false)]      // 零值
    [InlineData(-1, false)]     // 负值
    public void IsValidBaudRate_ShouldReturnCorrectResult(int baudRate, bool expected)
    {
        // Act
        var result = SerialPortExtensions.IsValidBaudRate(baudRate);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void GetRecommendedBaudRates_ShouldReturnCommonBaudRates()
    {
        // Act
        var baudRates = SerialPortExtensions.GetRecommendedBaudRates();

        // Assert
        baudRates.Should().NotBeNull();
        baudRates.Should().Contain(9600);
        baudRates.Should().Contain(19200);
        baudRates.Should().Contain(38400);
        baudRates.Should().Contain(57600);
        baudRates.Should().Contain(115200);
    }

    [Fact]
    public void IsUsbDevice_ShouldReturnCorrectValue()
    {
        // Arrange
        var usbPort = CreateTestPortInfo();
        usbPort.VendorId = "1234";
        usbPort.ProductId = "5678";

        var nonUsbPort = CreateTestPortInfo();
        nonUsbPort.VendorId = "";
        nonUsbPort.ProductId = "";

        // Act & Assert
        usbPort.IsUsbDevice().Should().BeTrue();
        nonUsbPort.IsUsbDevice().Should().BeFalse();
    }

    [Fact]
    public void IsVirtualPort_ShouldReturnCorrectValue()
    {
        // Arrange
        var virtualPort = CreateTestPortInfo();
        virtualPort.Description = "Virtual COM Port";

        var physicalPort = CreateTestPortInfo();
        physicalPort.Description = "USB Serial Port";

        // Act & Assert
        virtualPort.IsVirtualPort().Should().BeTrue();
        physicalPort.IsVirtualPort().Should().BeFalse();
    }

    [Fact]
    public void GetDisplayName_ShouldReturnCorrectValue()
    {
        // Arrange
        var portInfo = CreateTestPortInfo("COM1", "Test Port");
        portInfo.FriendlyName = "Test Port (COM1)";

        // Act
        var displayName = portInfo.GetDisplayName();

        // Assert
        displayName.Should().Be("Test Port (COM1)");
    }
}
