using System.Text;

namespace Liam.TcpServer.Extensions;

/// <summary>
/// 字节数组扩展方法
/// </summary>
public static class ByteArrayExtensions
{
    /// <summary>
    /// 将字节数组转换为十六进制字符串
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="separator">分隔符</param>
    /// <returns>十六进制字符串</returns>
    public static string ToHexString(this byte[] bytes, string separator = "")
    {
        ArgumentNullException.ThrowIfNull(bytes);
        
        if (bytes.Length == 0)
        {
            return string.Empty;
        }

        return string.Join(separator, bytes.Select(b => b.ToString("X2")));
    }

    /// <summary>
    /// 将字节数组转换为Base64字符串
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <returns>Base64字符串</returns>
    public static string ToBase64String(this byte[] bytes)
    {
        ArgumentNullException.ThrowIfNull(bytes);
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// 将字节数组转换为UTF-8字符串
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <returns>UTF-8字符串</returns>
    public static string ToUtf8String(this byte[] bytes)
    {
        ArgumentNullException.ThrowIfNull(bytes);
        return Encoding.UTF8.GetString(bytes);
    }

    /// <summary>
    /// 将字节数组转换为指定编码的字符串
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="encoding">字符编码</param>
    /// <returns>字符串</returns>
    public static string ToString(this byte[] bytes, Encoding encoding)
    {
        ArgumentNullException.ThrowIfNull(bytes);
        ArgumentNullException.ThrowIfNull(encoding);
        return encoding.GetString(bytes);
    }

    /// <summary>
    /// 检查字节数组是否以指定的字节序列开头
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="prefix">前缀字节序列</param>
    /// <returns>是否以指定序列开头</returns>
    public static bool StartsWith(this byte[] bytes, byte[] prefix)
    {
        ArgumentNullException.ThrowIfNull(bytes);
        ArgumentNullException.ThrowIfNull(prefix);

        if (prefix.Length > bytes.Length)
        {
            return false;
        }

        for (int i = 0; i < prefix.Length; i++)
        {
            if (bytes[i] != prefix[i])
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 检查字节数组是否以指定的字节序列结尾
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="suffix">后缀字节序列</param>
    /// <returns>是否以指定序列结尾</returns>
    public static bool EndsWith(this byte[] bytes, byte[] suffix)
    {
        ArgumentNullException.ThrowIfNull(bytes);
        ArgumentNullException.ThrowIfNull(suffix);

        if (suffix.Length > bytes.Length)
        {
            return false;
        }

        int startIndex = bytes.Length - suffix.Length;
        for (int i = 0; i < suffix.Length; i++)
        {
            if (bytes[startIndex + i] != suffix[i])
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 在字节数组中查找指定字节序列的位置
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="pattern">要查找的字节序列</param>
    /// <param name="startIndex">开始查找的位置</param>
    /// <returns>找到的位置，未找到返回-1</returns>
    public static int IndexOf(this byte[] bytes, byte[] pattern, int startIndex = 0)
    {
        ArgumentNullException.ThrowIfNull(bytes);
        ArgumentNullException.ThrowIfNull(pattern);

        if (pattern.Length == 0)
        {
            return startIndex;
        }

        if (startIndex < 0 || startIndex >= bytes.Length)
        {
            return -1;
        }

        for (int i = startIndex; i <= bytes.Length - pattern.Length; i++)
        {
            bool found = true;
            for (int j = 0; j < pattern.Length; j++)
            {
                if (bytes[i + j] != pattern[j])
                {
                    found = false;
                    break;
                }
            }

            if (found)
            {
                return i;
            }
        }

        return -1;
    }

    /// <summary>
    /// 合并多个字节数组
    /// </summary>
    /// <param name="arrays">要合并的字节数组</param>
    /// <returns>合并后的字节数组</returns>
    public static byte[] Combine(params byte[][] arrays)
    {
        ArgumentNullException.ThrowIfNull(arrays);

        int totalLength = arrays.Sum(arr => arr?.Length ?? 0);
        byte[] result = new byte[totalLength];
        int offset = 0;

        foreach (var array in arrays)
        {
            if (array != null && array.Length > 0)
            {
                Array.Copy(array, 0, result, offset, array.Length);
                offset += array.Length;
            }
        }

        return result;
    }

    /// <summary>
    /// 分割字节数组
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="delimiter">分隔符</param>
    /// <returns>分割后的字节数组列表</returns>
    public static List<byte[]> Split(this byte[] bytes, byte[] delimiter)
    {
        ArgumentNullException.ThrowIfNull(bytes);
        ArgumentNullException.ThrowIfNull(delimiter);

        var result = new List<byte[]>();
        int startIndex = 0;
        int delimiterIndex;

        while ((delimiterIndex = bytes.IndexOf(delimiter, startIndex)) != -1)
        {
            int length = delimiterIndex - startIndex;
            if (length > 0)
            {
                byte[] segment = new byte[length];
                Array.Copy(bytes, startIndex, segment, 0, length);
                result.Add(segment);
            }
            else
            {
                result.Add(Array.Empty<byte>());
            }

            startIndex = delimiterIndex + delimiter.Length;
        }

        // 添加最后一段
        if (startIndex < bytes.Length)
        {
            int length = bytes.Length - startIndex;
            byte[] lastSegment = new byte[length];
            Array.Copy(bytes, startIndex, lastSegment, 0, length);
            result.Add(lastSegment);
        }
        else if (startIndex == bytes.Length)
        {
            result.Add(Array.Empty<byte>());
        }

        return result;
    }

    /// <summary>
    /// 获取字节数组的子数组
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="startIndex">开始位置</param>
    /// <param name="length">长度</param>
    /// <returns>子数组</returns>
    public static byte[] SubArray(this byte[] bytes, int startIndex, int length)
    {
        ArgumentNullException.ThrowIfNull(bytes);

        if (startIndex < 0 || startIndex >= bytes.Length)
        {
            throw new ArgumentOutOfRangeException(nameof(startIndex));
        }

        if (length < 0 || startIndex + length > bytes.Length)
        {
            throw new ArgumentOutOfRangeException(nameof(length));
        }

        byte[] result = new byte[length];
        Array.Copy(bytes, startIndex, result, 0, length);
        return result;
    }

    /// <summary>
    /// 计算字节数组的校验和
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <returns>校验和</returns>
    public static byte CalculateChecksum(this byte[] bytes)
    {
        ArgumentNullException.ThrowIfNull(bytes);

        byte checksum = 0;
        foreach (byte b in bytes)
        {
            checksum ^= b;
        }
        return checksum;
    }

    /// <summary>
    /// 计算字节数组的CRC32校验值
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <returns>CRC32校验值</returns>
    public static uint CalculateCrc32(this byte[] bytes)
    {
        ArgumentNullException.ThrowIfNull(bytes);

        const uint polynomial = 0xEDB88320;
        uint crc = 0xFFFFFFFF;

        foreach (byte b in bytes)
        {
            crc ^= b;
            for (int i = 0; i < 8; i++)
            {
                if ((crc & 1) != 0)
                {
                    crc = (crc >> 1) ^ polynomial;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }

        return ~crc;
    }

    /// <summary>
    /// 检查两个字节数组是否相等
    /// </summary>
    /// <param name="bytes1">第一个字节数组</param>
    /// <param name="bytes2">第二个字节数组</param>
    /// <returns>是否相等</returns>
    public static bool SequenceEqual(this byte[] bytes1, byte[] bytes2)
    {
        ArgumentNullException.ThrowIfNull(bytes1);
        ArgumentNullException.ThrowIfNull(bytes2);

        if (bytes1.Length != bytes2.Length)
        {
            return false;
        }

        for (int i = 0; i < bytes1.Length; i++)
        {
            if (bytes1[i] != bytes2[i])
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 压缩字节数组（使用GZip）
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <returns>压缩后的字节数组</returns>
    public static byte[] Compress(this byte[] bytes)
    {
        ArgumentNullException.ThrowIfNull(bytes);

        using var memoryStream = new MemoryStream();
        using (var gzipStream = new System.IO.Compression.GZipStream(memoryStream, System.IO.Compression.CompressionMode.Compress))
        {
            gzipStream.Write(bytes, 0, bytes.Length);
        }
        return memoryStream.ToArray();
    }

    /// <summary>
    /// 解压缩字节数组（使用GZip）
    /// </summary>
    /// <param name="compressedBytes">压缩的字节数组</param>
    /// <returns>解压缩后的字节数组</returns>
    public static byte[] Decompress(this byte[] compressedBytes)
    {
        ArgumentNullException.ThrowIfNull(compressedBytes);

        using var compressedStream = new MemoryStream(compressedBytes);
        using var gzipStream = new System.IO.Compression.GZipStream(compressedStream, System.IO.Compression.CompressionMode.Decompress);
        using var resultStream = new MemoryStream();
        
        gzipStream.CopyTo(resultStream);
        return resultStream.ToArray();
    }
}
