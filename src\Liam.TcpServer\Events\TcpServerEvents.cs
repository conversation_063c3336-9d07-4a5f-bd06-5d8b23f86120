using Liam.TcpServer.Models;

namespace Liam.TcpServer.Events;

/// <summary>
/// 客户端连接事件参数
/// </summary>
public class ClientConnectedEventArgs : EventArgs
{
    /// <summary>
    /// 客户端连接信息
    /// </summary>
    public ClientConnection Connection { get; }

    /// <summary>
    /// 连接时间
    /// </summary>
    public DateTime ConnectedAt { get; }

    /// <summary>
    /// 初始化客户端连接事件参数
    /// </summary>
    /// <param name="connection">客户端连接信息</param>
    public ClientConnectedEventArgs(ClientConnection connection)
    {
        Connection = connection ?? throw new ArgumentNullException(nameof(connection));
        ConnectedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// 客户端断开连接事件参数
/// </summary>
public class ClientDisconnectedEventArgs : EventArgs
{
    /// <summary>
    /// 客户端连接信息
    /// </summary>
    public ClientConnection Connection { get; }

    /// <summary>
    /// 断开连接时间
    /// </summary>
    public DateTime DisconnectedAt { get; }

    /// <summary>
    /// 断开连接原因
    /// </summary>
    public string? Reason { get; }

    /// <summary>
    /// 是否为异常断开
    /// </summary>
    public bool IsError { get; }

    /// <summary>
    /// 初始化客户端断开连接事件参数
    /// </summary>
    /// <param name="connection">客户端连接信息</param>
    /// <param name="reason">断开连接原因</param>
    /// <param name="isError">是否为异常断开</param>
    public ClientDisconnectedEventArgs(ClientConnection connection, string? reason = null, bool isError = false)
    {
        Connection = connection ?? throw new ArgumentNullException(nameof(connection));
        DisconnectedAt = DateTime.UtcNow;
        Reason = reason;
        IsError = isError;
    }
}

/// <summary>
/// 数据接收事件参数
/// </summary>
public class DataReceivedEventArgs : EventArgs
{
    /// <summary>
    /// 客户端连接信息
    /// </summary>
    public ClientConnection Connection { get; }

    /// <summary>
    /// 接收到的数据
    /// </summary>
    public byte[] Data { get; }

    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime ReceivedAt { get; }

    /// <summary>
    /// 初始化数据接收事件参数
    /// </summary>
    /// <param name="connection">客户端连接信息</param>
    /// <param name="data">接收到的数据</param>
    public DataReceivedEventArgs(ClientConnection connection, byte[] data)
    {
        Connection = connection ?? throw new ArgumentNullException(nameof(connection));
        Data = data ?? throw new ArgumentNullException(nameof(data));
        ReceivedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// 数据发送事件参数
/// </summary>
public class DataSentEventArgs : EventArgs
{
    /// <summary>
    /// 客户端连接信息
    /// </summary>
    public ClientConnection Connection { get; }

    /// <summary>
    /// 发送的数据
    /// </summary>
    public byte[] Data { get; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime SentAt { get; }

    /// <summary>
    /// 发送是否成功
    /// </summary>
    public bool IsSuccess { get; }

    /// <summary>
    /// 初始化数据发送事件参数
    /// </summary>
    /// <param name="connection">客户端连接信息</param>
    /// <param name="data">发送的数据</param>
    /// <param name="isSuccess">发送是否成功</param>
    public DataSentEventArgs(ClientConnection connection, byte[] data, bool isSuccess = true)
    {
        Connection = connection ?? throw new ArgumentNullException(nameof(connection));
        Data = data ?? throw new ArgumentNullException(nameof(data));
        SentAt = DateTime.UtcNow;
        IsSuccess = isSuccess;
    }
}

/// <summary>
/// TCP服务器错误事件参数
/// </summary>
public class TcpServerErrorEventArgs : EventArgs
{
    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception Exception { get; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; }

    /// <summary>
    /// 错误发生时间
    /// </summary>
    public DateTime OccurredAt { get; }

    /// <summary>
    /// 相关的客户端连接（可选）
    /// </summary>
    public ClientConnection? Connection { get; }

    /// <summary>
    /// 初始化错误事件参数
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <param name="connection">相关的客户端连接</param>
    public TcpServerErrorEventArgs(Exception exception, ClientConnection? connection = null)
    {
        Exception = exception ?? throw new ArgumentNullException(nameof(exception));
        Message = exception.Message;
        OccurredAt = DateTime.UtcNow;
        Connection = connection;
    }

    /// <summary>
    /// 初始化错误事件参数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="connection">相关的客户端连接</param>
    public TcpServerErrorEventArgs(string message, ClientConnection? connection = null)
    {
        Exception = new Exception(message);
        Message = message ?? throw new ArgumentNullException(nameof(message));
        OccurredAt = DateTime.UtcNow;
        Connection = connection;
    }
}

/// <summary>
/// 服务器状态变更事件参数
/// </summary>
public class ServerStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 旧状态
    /// </summary>
    public string OldStatus { get; }

    /// <summary>
    /// 新状态
    /// </summary>
    public string NewStatus { get; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime ChangedAt { get; }

    /// <summary>
    /// 初始化服务器状态变更事件参数
    /// </summary>
    /// <param name="oldStatus">旧状态</param>
    /// <param name="newStatus">新状态</param>
    public ServerStatusChangedEventArgs(string oldStatus, string newStatus)
    {
        OldStatus = oldStatus ?? throw new ArgumentNullException(nameof(oldStatus));
        NewStatus = newStatus ?? throw new ArgumentNullException(nameof(newStatus));
        ChangedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// 心跳事件参数
/// </summary>
public class HeartbeatEventArgs : EventArgs
{
    /// <summary>
    /// 客户端连接信息
    /// </summary>
    public ClientConnection Connection { get; }

    /// <summary>
    /// 心跳时间
    /// </summary>
    public DateTime HeartbeatAt { get; }

    /// <summary>
    /// 是否为心跳请求（true）或响应（false）
    /// </summary>
    public bool IsRequest { get; }

    /// <summary>
    /// 初始化心跳事件参数
    /// </summary>
    /// <param name="connection">客户端连接信息</param>
    /// <param name="isRequest">是否为心跳请求</param>
    public HeartbeatEventArgs(ClientConnection connection, bool isRequest = true)
    {
        Connection = connection ?? throw new ArgumentNullException(nameof(connection));
        HeartbeatAt = DateTime.UtcNow;
        IsRequest = isRequest;
    }
}
