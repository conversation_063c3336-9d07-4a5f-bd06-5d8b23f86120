using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Liam.SerialPort.Models;

namespace Liam.SerialPort.Tests.TestHelpers;

/// <summary>
/// 串口测试基类
/// </summary>
public abstract class SerialPortTestBase : IDisposable
{
    protected readonly Mock<ILogger> Mo<PERSON>Logger;
    protected readonly ILogger Logger;
    protected bool Disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    protected SerialPortTestBase()
    {
        MockLogger = new Mock<ILogger>();
        Logger = MockLogger.Object;
    }

    /// <summary>
    /// 创建测试用的串口信息
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="description">描述</param>
    /// <param name="isAvailable">是否可用</param>
    /// <returns>串口信息</returns>
    protected static SerialPortInfo CreateTestPortInfo(string portName = "COM1", string description = "Test Port", bool isAvailable = true)
    {
        return new SerialPortInfo
        {
            PortName = portName,
            Description = description,
            Manufacturer = "Test Manufacturer",
            ProductId = "1234",
            VendorId = "5678",
            SerialNumber = "TEST123",
            DeviceType = "USB Serial",
            IsAvailable = isAvailable,
            IsInUse = false,
            DevicePath = $"/dev/{portName.ToLower()}",
            FriendlyName = $"{description} ({portName})",
            DeviceInstanceId = $"USB\\VID_5678&PID_1234\\TEST123",
            HardwareIds = new List<string> { "USB\\VID_5678&PID_1234" },
            CompatibleIds = new List<string> { "USB\\Class_02" },
            Properties = new Dictionary<string, object>
            {
                { "DeviceClass", "Ports" },
                { "Service", "usbser" }
            },
            DiscoveredAt = DateTime.Now,
            LastChecked = DateTime.Now
        };
    }

    /// <summary>
    /// 创建测试用的串口设置
    /// </summary>
    /// <param name="baudRate">波特率</param>
    /// <param name="autoReconnect">是否自动重连</param>
    /// <returns>串口设置</returns>
    protected static SerialPortSettings CreateTestSettings(int baudRate = 9600, bool autoReconnect = false)
    {
        return new SerialPortSettings
        {
            BaudRate = baudRate,
            DataBits = 8,
            StopBits = System.IO.Ports.StopBits.One,
            Parity = System.IO.Ports.Parity.None,
            Handshake = System.IO.Ports.Handshake.None,
            ReadTimeout = 1000,
            WriteTimeout = 1000,
            ReceiveBufferSize = 1024,
            SendBufferSize = 512,
            DtrEnable = false,
            RtsEnable = false,
            NewLine = "\r\n",
            Encoding = System.Text.Encoding.UTF8,
            DiscardNull = false,
            RetryCount = 3,
            RetryInterval = 100,
            AutoReconnect = autoReconnect,
            AutoReconnectInterval = 1000,
            MaxAutoReconnectAttempts = 3,
            ConnectionTimeout = 5000,
            HeartbeatInterval = 0,
            HeartbeatData = Array.Empty<byte>(),
            EnableDataLogging = false,
            MaxLogSize = 1024 * 1024
        };
    }

    /// <summary>
    /// 创建测试用的日志记录器
    /// </summary>
    /// <typeparam name="T">日志类型</typeparam>
    /// <returns>日志记录器</returns>
    protected static ILogger<T> CreateTestLogger<T>()
    {
        return NullLogger<T>.Instance;
    }

    /// <summary>
    /// 创建模拟的日志记录器
    /// </summary>
    /// <typeparam name="T">日志类型</typeparam>
    /// <returns>模拟的日志记录器</returns>
    protected static Mock<ILogger<T>> CreateMockLogger<T>()
    {
        return new Mock<ILogger<T>>();
    }

    /// <summary>
    /// 等待异步操作完成
    /// </summary>
    /// <param name="task">任务</param>
    /// <param name="timeoutMs">超时时间（毫秒）</param>
    /// <returns>是否在超时前完成</returns>
    protected static async Task<bool> WaitForAsync(Task task, int timeoutMs = 5000)
    {
        var timeoutTask = Task.Delay(timeoutMs);
        var completedTask = await Task.WhenAny(task, timeoutTask);
        return completedTask == task;
    }

    /// <summary>
    /// 等待异步操作完成并返回结果
    /// </summary>
    /// <typeparam name="T">结果类型</typeparam>
    /// <param name="task">任务</param>
    /// <param name="timeoutMs">超时时间（毫秒）</param>
    /// <returns>结果或默认值</returns>
    protected static async Task<T?> WaitForAsync<T>(Task<T> task, int timeoutMs = 5000)
    {
        var timeoutTask = Task.Delay(timeoutMs);
        var completedTask = await Task.WhenAny(task, timeoutTask);
        
        if (completedTask == task)
        {
            return await task;
        }
        
        return default;
    }

    /// <summary>
    /// 等待条件满足
    /// </summary>
    /// <param name="condition">条件</param>
    /// <param name="timeoutMs">超时时间（毫秒）</param>
    /// <param name="intervalMs">检查间隔（毫秒）</param>
    /// <returns>是否满足条件</returns>
    protected static async Task<bool> WaitForConditionAsync(Func<bool> condition, int timeoutMs = 5000, int intervalMs = 100)
    {
        var endTime = DateTime.Now.AddMilliseconds(timeoutMs);
        
        while (DateTime.Now < endTime)
        {
            if (condition())
                return true;
                
            await Task.Delay(intervalMs);
        }
        
        return false;
    }

    /// <summary>
    /// 创建测试数据
    /// </summary>
    /// <param name="size">数据大小</param>
    /// <returns>测试数据</returns>
    protected static byte[] CreateTestData(int size = 10)
    {
        var data = new byte[size];
        for (int i = 0; i < size; i++)
        {
            data[i] = (byte)(i % 256);
        }
        return data;
    }

    /// <summary>
    /// 创建测试字符串
    /// </summary>
    /// <param name="prefix">前缀</param>
    /// <param name="length">长度</param>
    /// <returns>测试字符串</returns>
    protected static string CreateTestString(string prefix = "Test", int length = 10)
    {
        var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        var result = prefix;
        
        for (int i = prefix.Length; i < length; i++)
        {
            result += chars[random.Next(chars.Length)];
        }
        
        return result;
    }

    /// <summary>
    /// 验证日志是否被调用
    /// </summary>
    /// <param name="mockLogger">模拟日志记录器</param>
    /// <param name="logLevel">日志级别</param>
    /// <param name="message">消息</param>
    /// <param name="times">调用次数</param>
    protected static void VerifyLogCalled<T>(Mock<ILogger<T>> mockLogger, LogLevel logLevel, string message, Times? times = null)
    {
        mockLogger.Verify(
            x => x.Log(
                logLevel,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            times ?? Times.AtLeastOnce());
    }

    /// <summary>
    /// 验证日志是否包含特定消息
    /// </summary>
    /// <param name="mockLogger">模拟日志记录器</param>
    /// <param name="logLevel">日志级别</param>
    /// <param name="messagePattern">消息模式</param>
    protected static void VerifyLogContains<T>(Mock<ILogger<T>> mockLogger, LogLevel logLevel, string messagePattern)
    {
        mockLogger.Verify(
            x => x.Log(
                logLevel,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(messagePattern)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce());
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public virtual void Dispose()
    {
        if (Disposed)
            return;

        Disposed = true;
        GC.SuppressFinalize(this);
    }
}
