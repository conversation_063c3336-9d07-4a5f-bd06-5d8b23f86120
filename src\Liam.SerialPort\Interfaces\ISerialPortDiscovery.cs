using Liam.SerialPort.Events;
using <PERSON>.SerialPort.Models;

namespace Liam.SerialPort.Interfaces;

/// <summary>
/// 串口设备发现接口，提供串口设备的枚举和热插拔检测功能
/// </summary>
public interface ISerialPortDiscovery : IDisposable
{
    /// <summary>
    /// 设备变化事件
    /// </summary>
    event EventHandler<DeviceChangedEventArgs>? DeviceChanged;

    /// <summary>
    /// 获取是否正在监控设备变化
    /// </summary>
    bool IsMonitoring { get; }

    /// <summary>
    /// 获取所有可用的串口设备
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>串口设备信息列表</returns>
    Task<IEnumerable<SerialPortInfo>> GetAvailablePortsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定串口的详细信息
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>串口详细信息，如果不存在则返回null</returns>
    Task<SerialPortInfo?> GetPortInfoAsync(string portName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查指定串口是否可用
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>串口是否可用</returns>
    Task<bool> IsPortAvailableAsync(string portName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 开始监控设备变化
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task StartMonitoringAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止监控设备变化
    /// </summary>
    Task StopMonitoringAsync();

    /// <summary>
    /// 刷新设备列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task RefreshAsync(CancellationToken cancellationToken = default);
}
