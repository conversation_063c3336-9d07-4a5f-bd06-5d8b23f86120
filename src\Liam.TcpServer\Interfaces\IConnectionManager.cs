using Liam.TcpServer.Models;
using System.Net;

namespace Liam.TcpServer.Interfaces;

/// <summary>
/// 连接管理器接口
/// </summary>
public interface IConnectionManager : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 当前连接数
    /// </summary>
    int ConnectionCount { get; }

    /// <summary>
    /// 最大连接数
    /// </summary>
    int MaxConnections { get; }

    /// <summary>
    /// 是否已达到最大连接数
    /// </summary>
    bool IsMaxConnectionsReached { get; }

    /// <summary>
    /// 添加连接
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <returns>是否成功添加</returns>
    bool AddConnection(ClientConnection connection);

    /// <summary>
    /// 移除连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>是否成功移除</returns>
    bool RemoveConnection(string connectionId);

    /// <summary>
    /// 获取连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>客户端连接</returns>
    ClientConnection? GetConnection(string connectionId);

    /// <summary>
    /// 获取所有连接
    /// </summary>
    /// <returns>所有连接</returns>
    IReadOnlyList<ClientConnection> GetAllConnections();

    /// <summary>
    /// 获取活跃连接
    /// </summary>
    /// <returns>活跃连接</returns>
    IReadOnlyList<ClientConnection> GetActiveConnections();

    /// <summary>
    /// 获取指定IP的连接
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>指定IP的连接</returns>
    IReadOnlyList<ClientConnection> GetConnectionsByIp(IPAddress ipAddress);

    /// <summary>
    /// 检查连接是否存在
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>是否存在</returns>
    bool HasConnection(string connectionId);

    /// <summary>
    /// 检查连接是否活跃
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>是否活跃</returns>
    bool IsConnectionActive(string connectionId);

    /// <summary>
    /// 清理断开的连接
    /// </summary>
    /// <returns>清理的连接数</returns>
    Task<int> CleanupDisconnectedConnectionsAsync();

    /// <summary>
    /// 清理超时的连接
    /// </summary>
    /// <param name="timeoutSeconds">超时时间（秒）</param>
    /// <returns>清理的连接数</returns>
    Task<int> CleanupTimeoutConnectionsAsync(int timeoutSeconds);

    /// <summary>
    /// 清理空闲的连接
    /// </summary>
    /// <param name="idleTimeoutSeconds">空闲超时时间（秒）</param>
    /// <returns>清理的连接数</returns>
    Task<int> CleanupIdleConnectionsAsync(int idleTimeoutSeconds);

    /// <summary>
    /// 断开所有连接
    /// </summary>
    /// <param name="reason">断开原因</param>
    /// <returns>断开的连接数</returns>
    Task<int> DisconnectAllAsync(string? reason = null);

    /// <summary>
    /// 断开指定IP的所有连接
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="reason">断开原因</param>
    /// <returns>断开的连接数</returns>
    Task<int> DisconnectByIpAsync(IPAddress ipAddress, string? reason = null);

    /// <summary>
    /// 获取连接统计信息
    /// </summary>
    /// <returns>连接统计信息</returns>
    ConnectionManagerStatistics GetStatistics();

    /// <summary>
    /// 更新连接活动时间
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    void UpdateConnectionActivity(string connectionId);

    /// <summary>
    /// 更新连接心跳时间
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    void UpdateConnectionHeartbeat(string connectionId);

    /// <summary>
    /// 设置连接认证状态
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="isAuthenticated">是否已认证</param>
    /// <param name="user">认证用户</param>
    void SetConnectionAuthentication(string connectionId, bool isAuthenticated, string? user = null);

    /// <summary>
    /// 获取指定状态的连接
    /// </summary>
    /// <param name="status">连接状态</param>
    /// <returns>指定状态的连接</returns>
    IReadOnlyList<ClientConnection> GetConnectionsByStatus(string status);

    /// <summary>
    /// 获取已认证的连接
    /// </summary>
    /// <returns>已认证的连接</returns>
    IReadOnlyList<ClientConnection> GetAuthenticatedConnections();

    /// <summary>
    /// 获取未认证的连接
    /// </summary>
    /// <returns>未认证的连接</returns>
    IReadOnlyList<ClientConnection> GetUnauthenticatedConnections();
}

/// <summary>
/// 连接管理器统计信息
/// </summary>
public class ConnectionManagerStatistics
{
    /// <summary>
    /// 当前连接数
    /// </summary>
    public int CurrentConnections { get; set; }

    /// <summary>
    /// 最大连接数限制
    /// </summary>
    public int MaxConnections { get; set; }

    /// <summary>
    /// 历史最大并发连接数
    /// </summary>
    public int PeakConnections { get; set; }

    /// <summary>
    /// 总连接数
    /// </summary>
    public long TotalConnections { get; set; }

    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// 已认证连接数
    /// </summary>
    public int AuthenticatedConnections { get; set; }

    /// <summary>
    /// 未认证连接数
    /// </summary>
    public int UnauthenticatedConnections { get; set; }

    /// <summary>
    /// 平均连接持续时间
    /// </summary>
    public TimeSpan AverageConnectionDuration { get; set; }

    /// <summary>
    /// 连接成功率
    /// </summary>
    public double ConnectionSuccessRate { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; }

    /// <summary>
    /// 按IP地址分组的连接数
    /// </summary>
    public Dictionary<string, int> ConnectionsByIp { get; set; } = new();

    /// <summary>
    /// 按状态分组的连接数
    /// </summary>
    public Dictionary<string, int> ConnectionsByStatus { get; set; } = new();
}
