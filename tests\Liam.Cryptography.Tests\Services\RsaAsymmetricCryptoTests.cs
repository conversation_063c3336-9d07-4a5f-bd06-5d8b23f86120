using Liam.Cryptography.Services;
using Liam.Cryptography.Exceptions;
using Liam.Cryptography.Tests.Fixtures;
using Liam.Cryptography.Tests.TestHelpers;

namespace Liam.Cryptography.Tests.Services;

/// <summary>
/// RSA非对称加密服务测�?/// </summary>
[Collection("Crypto Tests")]
public class RsaAsymmetricCryptoTests
{
    private readonly CryptoTestFixture _fixture;
    private readonly RsaAsymmetricCrypto _rsaService;

    public RsaAsymmetricCryptoTests(CryptoTestFixture fixture)
    {
        _fixture = fixture;
        _rsaService = _fixture.RsaService;
    }

    #region 密钥对生成测�?
    [Theory]
    [InlineData(1024)]
    [InlineData(2048)]
    [InlineData(3072)]
    [InlineData(4096)]
    public void GenerateKeyPair_ValidKeySize_ShouldReturnValidKeyPair(int keySize)
    {
        // Act
        var keyPair = _rsaService.GenerateKeyPair(keySize);

        // Assert
        keyPair.Should().NotBeNull();
        keyPair.PublicKey.Should().NotBeNullOrEmpty();
        keyPair.PrivateKey.Should().NotBeNullOrEmpty();
        keyPair.KeySize.Should().Be(keySize);
        keyPair.IsValid().Should().BeTrue();
    }

    [Theory]
    [InlineData(512)]
    [InlineData(8192)]
    [InlineData(0)]
    [InlineData(-1)]
    public void GenerateKeyPair_InvalidKeySize_ShouldThrowException(int keySize)
    {
        // Act & Assert
        var action = () => _rsaService.GenerateKeyPair(keySize);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void GenerateKeyPair_MultipleCalls_ShouldReturnDifferentKeyPairs()
    {
        // Act
        var keyPair1 = _rsaService.GenerateKeyPair(2048);
        var keyPair2 = _rsaService.GenerateKeyPair(2048);

        // Assert
        keyPair1.PublicKey.Should().NotBe(keyPair2.PublicKey!);
        keyPair1.PrivateKey.Should().NotBe(keyPair2.PrivateKey!);
    }

    #endregion

    #region 公钥加密私钥解密测试

    [Fact]
    public void EncryptWithPublicKey_ValidInput_ShouldReturnEncryptedData()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = _rsaService.EncryptWithPublicKey(plainText, keyPair.PublicKey!);

        // Assert
        encrypted.Should().NotBeNull();
        encrypted.Length.Should().BeGreaterThan(0);
        encrypted.Should().NotEqual(System.Text.Encoding.UTF8.GetBytes(plainText));
    }

    [Fact]
    public void DecryptWithPrivateKey_ValidInput_ShouldReturnOriginalText()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = _rsaService.EncryptWithPublicKey(plainText, keyPair.PublicKey!);
        var decrypted = _rsaService.DecryptWithPrivateKey(encrypted, keyPair.PrivateKey!);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Theory]
    [InlineData("Hello World")]
    [InlineData("你好世界")]
    [InlineData("A")]
    [InlineData("123456")]
    public void EncryptDecryptWithPublicPrivateKey_DifferentTexts_ShouldPreserveOriginal(string plainText)
    {
        // Arrange
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = _rsaService.EncryptWithPublicKey(plainText, keyPair.PublicKey!);
        var decrypted = _rsaService.DecryptWithPrivateKey(encrypted, keyPair.PrivateKey!);

        // Assert
        decrypted.Should().Be(plainText);
    }

    #endregion

    #region 私钥加密公钥解密测试（数字签名场景）

    [Fact]
    public void EncryptWithPrivateKey_ValidInput_ShouldReturnEncryptedData()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = _rsaService.EncryptWithPrivateKey(plainText, keyPair.PrivateKey!);

        // Assert
        encrypted.Should().NotBeNull();
        encrypted.Length.Should().BeGreaterThan(0);
        encrypted.Should().NotEqual(System.Text.Encoding.UTF8.GetBytes(plainText));
    }

    [Fact]
    public void EncryptWithPrivateKey_ShouldWorkCorrectly()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = _rsaService.EncryptWithPrivateKey(plainText, keyPair.PrivateKey!);

        // Assert - 私钥加密主要用于数字签名，这里只验证不抛出异常
        encrypted.Should().NotBeNull();
        encrypted.Length.Should().BeGreaterThan(0);
    }

    #endregion

    #region 异步操作测试

    [Fact]
    public async Task EncryptWithPublicKeyAsync_ValidInput_ShouldReturnEncryptedData()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = await _rsaService.EncryptWithPublicKeyAsync(plainText, keyPair.PublicKey!);

        // Assert
        encrypted.Should().NotBeNull();
        encrypted.Length.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task DecryptWithPrivateKeyAsync_ValidInput_ShouldReturnOriginalText()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = await _rsaService.EncryptWithPublicKeyAsync(plainText, keyPair.PublicKey!);
        var decrypted = await _rsaService.DecryptWithPrivateKeyAsync(encrypted, keyPair.PrivateKey!);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Fact]
    public async Task EncryptDecryptAsync_WithCancellation_ShouldRespectCancellationToken()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _fixture.TestRsaKeyPair;
        using var cts = new CancellationTokenSource();

        // Act & Assert
        var encrypted = await _rsaService.EncryptWithPublicKeyAsync(plainText, keyPair.PublicKey, cts.Token);
        var decrypted = await _rsaService.DecryptWithPrivateKeyAsync(encrypted, keyPair.PrivateKey, cts.Token);

        decrypted.Should().Be(plainText);
    }

    #endregion

    #region 异常处理测试

    [Fact]
    public void EncryptWithPublicKey_NullPlainText_ShouldThrowArgumentException()
    {
        // Arrange
        var keyPair = _fixture.TestRsaKeyPair;

        // Act & Assert
        var action = () => _rsaService.EncryptWithPublicKey(null!, keyPair.PublicKey!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void EncryptWithPublicKey_NullPublicKey_ShouldThrowArgumentException()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;

        // Act & Assert
        var action = () => _rsaService.EncryptWithPublicKey(plainText, null!);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void EncryptWithPublicKey_InvalidPublicKey_ShouldThrowCryptographyException()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var invalidPublicKey = "invalid-public-key";

        // Act & Assert
        var action = () => _rsaService.EncryptWithPublicKey(plainText, invalidPublicKey);
        action.Should().Throw<CryptographyException>();
    }

    [Fact]
    public void DecryptWithPrivateKey_InvalidCipherData_ShouldThrowCryptographyException()
    {
        // Arrange
        var invalidCipherData = new byte[] { 1, 2, 3, 4, 5 };
        var keyPair = _fixture.TestRsaKeyPair;

        // Act & Assert
        var action = () => _rsaService.DecryptWithPrivateKey(invalidCipherData, keyPair.PrivateKey!);
        action.Should().Throw<CryptographyException>();
    }

    [Fact]
    public void DecryptWithPrivateKey_WrongPrivateKey_ShouldThrowCryptographyException()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var keyPair1 = _fixture.TestRsaKeyPair;
        var keyPair2 = _rsaService.GenerateKeyPair(2048);

        // Act
        var encrypted = _rsaService.EncryptWithPublicKey(plainText, keyPair1.PublicKey!);

        // Assert
        var action = () => _rsaService.DecryptWithPrivateKey(encrypted, keyPair2.PrivateKey!);
        action.Should().Throw<CryptographyException>();
    }

    [Fact]
    public void EncryptWithPublicKey_TextTooLong_ShouldThrowCryptographyException()
    {
        // Arrange
        var longText = new string('A', 1000); // 超过RSA加密限制的长文本
        var keyPair = _fixture.TestRsaKeyPair;

        // Act & Assert
        var action = () => _rsaService.EncryptWithPublicKey(longText, keyPair.PublicKey!);
        action.Should().Throw<CryptographyException>();
    }

    #endregion

    #region 边界条件测试

    [Fact]
    public void EncryptDecrypt_EmptyString_ShouldWorkCorrectly()
    {
        // Arrange
        var plainText = string.Empty;
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = _rsaService.EncryptWithPublicKey(plainText, keyPair.PublicKey!);
        var decrypted = _rsaService.DecryptWithPrivateKey(encrypted, keyPair.PrivateKey!);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Fact]
    public void EncryptDecrypt_SingleCharacter_ShouldWorkCorrectly()
    {
        // Arrange
        var plainText = "A";
        var keyPair = _fixture.TestRsaKeyPair;

        // Act
        var encrypted = _rsaService.EncryptWithPublicKey(plainText, keyPair.PublicKey!);
        var decrypted = _rsaService.DecryptWithPrivateKey(encrypted, keyPair.PrivateKey!);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Theory]
    [InlineData(1024)]
    [InlineData(2048)]
    [InlineData(3072)]
    public void EncryptDecrypt_DifferentKeySizes_ShouldWorkCorrectly(int keySize)
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var keyPair = _rsaService.GenerateKeyPair(keySize);

        // Act
        var encrypted = _rsaService.EncryptWithPublicKey(plainText, keyPair.PublicKey!);
        var decrypted = _rsaService.DecryptWithPrivateKey(encrypted, keyPair.PrivateKey!);

        // Assert
        decrypted.Should().Be(plainText);
    }

    #endregion

    #region 密钥格式测试

    [Fact]
    public void GenerateKeyPair_ShouldReturnPemFormattedKeys()
    {
        // Act
        var keyPair = _rsaService.GenerateKeyPair(2048);

        // Assert
        keyPair.PublicKey.Should().StartWith("-----BEGIN PUBLIC KEY-----");
        keyPair.PublicKey.Should().EndWith("-----END PUBLIC KEY-----");
        keyPair.PrivateKey.Should().StartWith("-----BEGIN PRIVATE KEY-----");
        keyPair.PrivateKey.Should().EndWith("-----END PRIVATE KEY-----");
    }

    #endregion
}
