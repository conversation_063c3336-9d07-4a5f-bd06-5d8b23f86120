using System.Buffers;
using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Constants;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;

namespace Liam.TcpClient.Services;

/// <summary>
/// 消息处理器实现
/// </summary>
public class MessageHandler : IMessageHandler
{
    private readonly ILogger<MessageHandler> _logger;
    private readonly SemaphoreSlim _sendSemaphore = new(1, 1);
    private readonly ConcurrentQueue<TaskCompletionSource<byte[]>> _receiveQueue = new();
    private readonly ConcurrentQueue<TaskCompletionSource<TcpMessage>> _messageQueue = new();
    private Stream? _stream;
    private CancellationTokenSource? _receiveCts;
    private Task? _receiveTask;
    private bool _isRunning;
    private bool _disposed;

    /// <summary>
    /// 是否正在运行
    /// </summary>
    public bool IsRunning => _isRunning;

    /// <summary>
    /// 消息接收事件
    /// </summary>
    public event EventHandler<TcpMessage>? MessageReceived;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    public event EventHandler<byte[]>? DataReceived;

    /// <summary>
    /// 错误事件
    /// </summary>
    public event EventHandler<Exception>? Error;

    /// <summary>
    /// 初始化消息处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public MessageHandler(ILogger<MessageHandler> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 启动消息处理
    /// </summary>
    /// <param name="stream">网络流</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public Task StartAsync(Stream stream, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(stream);

        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(MessageHandler));
        }

        if (_isRunning)
        {
            _logger.LogWarning("消息处理器已在运行");
            return Task.CompletedTask;
        }

        _stream = stream;
        _receiveCts = new CancellationTokenSource();
        _isRunning = true;

        // 启动接收任务 - 移除Task.Run反模式，直接使用异步方法
        _receiveTask = ReceiveLoopAsync(_receiveCts.Token);

        _logger.LogInformation("消息处理器已启动");
        return Task.CompletedTask;
    }

    /// <summary>
    /// 停止消息处理
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (!_isRunning)
        {
            return;
        }

        _isRunning = false;

        // 取消接收任务
        _receiveCts?.Cancel();

        // 等待接收任务完成
        if (_receiveTask != null)
        {
            try
            {
                await _receiveTask.ConfigureAwait(false);
            }
            catch (OperationCanceledException)
            {
                // 预期的取消异常
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "停止接收任务时发生错误");
            }
        }

        // 清理资源
        _receiveCts?.Dispose();
        _receiveCts = null;
        _receiveTask = null;
        _stream = null;

        // 完成所有等待的任务
        CompleteAllPendingTasks();

        _logger.LogInformation("消息处理器已停止");
    }

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (!_isRunning || _stream == null)
        {
            return false;
        }

        await _sendSemaphore.WaitAsync(cancellationToken).ConfigureAwait(false);
        try
        {
            await _stream.WriteAsync(data, cancellationToken).ConfigureAwait(false);
            await _stream.FlushAsync(cancellationToken).ConfigureAwait(false);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送数据失败，数据长度：{Length}", data.Length);
            Error?.Invoke(this, ex);
            return false;
        }
        finally
        {
            _sendSemaphore.Release();
        }
    }

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendMessageAsync(TcpMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);

        var serializedData = message.Serialize();
        var success = await SendAsync(serializedData, cancellationToken).ConfigureAwait(false);
        
        if (success)
        {
            message.SentAt = DateTime.UtcNow;
            _logger.LogDebug("发送消息：{Message}", message);
        }

        return success;
    }

    /// <summary>
    /// 等待接收数据
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收的数据</returns>
    public async Task<byte[]?> ReceiveAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        if (!_isRunning)
        {
            return null;
        }

        var tcs = new TaskCompletionSource<byte[]>();
        _receiveQueue.Enqueue(tcs);

        try
        {
            if (timeout.HasValue)
            {
                using var timeoutCts = new CancellationTokenSource(timeout.Value);
                using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);
                
                var timeoutTask = Task.Delay(timeout.Value, combinedCts.Token);
                var completedTask = await Task.WhenAny(tcs.Task, timeoutTask).ConfigureAwait(false);
                
                if (completedTask == timeoutTask)
                {
                    tcs.TrySetCanceled();
                    return null;
                }
            }

            return await tcs.Task.ConfigureAwait(false);
        }
        catch (OperationCanceledException)
        {
            return null;
        }
    }

    /// <summary>
    /// 等待接收消息
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收的消息</returns>
    public async Task<TcpMessage?> ReceiveMessageAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        if (!_isRunning)
        {
            return null;
        }

        var tcs = new TaskCompletionSource<TcpMessage>();
        _messageQueue.Enqueue(tcs);

        try
        {
            if (timeout.HasValue)
            {
                using var timeoutCts = new CancellationTokenSource(timeout.Value);
                using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);
                
                var timeoutTask = Task.Delay(timeout.Value, combinedCts.Token);
                var completedTask = await Task.WhenAny(tcs.Task, timeoutTask).ConfigureAwait(false);
                
                if (completedTask == timeoutTask)
                {
                    tcs.TrySetCanceled();
                    return null;
                }
            }

            return await tcs.Task.ConfigureAwait(false);
        }
        catch (OperationCanceledException)
        {
            return null;
        }
    }

    /// <summary>
    /// 接收循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ReceiveLoopAsync(CancellationToken cancellationToken)
    {
        var buffer = ArrayPool<byte>.Shared.Rent(TcpClientConstants.Defaults.ReceiveBufferSize);
        try
        {
            while (!cancellationToken.IsCancellationRequested && _stream != null)
            {
                try
                {
                    // 读取消息头
                    var headerBuffer = new byte[TcpClientConstants.Protocol.MessageHeaderSize];
                    var headerBytesRead = 0;
                    
                    while (headerBytesRead < headerBuffer.Length && !cancellationToken.IsCancellationRequested)
                    {
                        var bytesRead = await _stream.ReadAsync(
                            headerBuffer.AsMemory(headerBytesRead, headerBuffer.Length - headerBytesRead),
                            cancellationToken).ConfigureAwait(false);
                        
                        if (bytesRead == 0)
                        {
                            _logger.LogWarning("连接已关闭");
                            return;
                        }
                        
                        headerBytesRead += bytesRead;
                    }

                    if (cancellationToken.IsCancellationRequested)
                    {
                        break;
                    }

                    // 解析消息头
                    var messageType = headerBuffer[0];
                    var messageLength = BitConverter.ToInt32(headerBuffer, 1);

                    // 验证消息长度
                    if (messageLength < 0 || messageLength > TcpClientConstants.Defaults.MaxMessageLength)
                    {
                        _logger.LogError("无效的消息长度：{Length}", messageLength);
                        Error?.Invoke(this, new InvalidOperationException($"无效的消息长度：{messageLength}"));
                        continue;
                    }

                    // 读取消息体 - 使用ArrayPool优化内存分配
                    byte[] messageData;
                    byte[]? rentedBuffer = null;

                    if (messageLength > 0)
                    {
                        // 从ArrayPool租用缓冲区
                        rentedBuffer = ArrayPool<byte>.Shared.Rent(messageLength);
                        var dataBytesRead = 0;

                        while (dataBytesRead < messageLength && !cancellationToken.IsCancellationRequested)
                        {
                            var bytesRead = await _stream.ReadAsync(
                                rentedBuffer.AsMemory(dataBytesRead, messageLength - dataBytesRead),
                                cancellationToken).ConfigureAwait(false);

                            if (bytesRead == 0)
                            {
                                _logger.LogWarning("连接在读取消息体时关闭");
                                if (rentedBuffer != null)
                                {
                                    ArrayPool<byte>.Shared.Return(rentedBuffer);
                                }
                                return;
                            }

                            dataBytesRead += bytesRead;
                        }

                        // 使用Span<T>高效复制实际数据到精确大小的数组
                        messageData = new byte[messageLength];
                        rentedBuffer.AsSpan(0, messageLength).CopyTo(messageData.AsSpan());

                        // 归还缓冲区
                        ArrayPool<byte>.Shared.Return(rentedBuffer);
                        rentedBuffer = null;
                    }
                    else
                    {
                        messageData = Array.Empty<byte>();
                    }

                    if (cancellationToken.IsCancellationRequested)
                    {
                        break;
                    }

                    // 创建消息对象
                    var message = new TcpMessage(messageType, messageData)
                    {
                        ReceivedAt = DateTime.UtcNow
                    };

                    _logger.LogDebug("接收到消息：{Message}", message);

                    // 处理消息
                    await ProcessReceivedMessageAsync(message).ConfigureAwait(false);

                    // 处理原始数据 - 使用ArrayPool优化内存分配
                    var fullDataLength = TcpClientConstants.Protocol.MessageHeaderSize + messageLength;
                    var fullDataBuffer = ArrayPool<byte>.Shared.Rent(fullDataLength);
                    try
                    {
                        var fullDataSpan = fullDataBuffer.AsSpan(0, fullDataLength);

                        // 使用Span<T>进行高效的内存复制
                        headerBuffer.AsSpan().CopyTo(fullDataSpan);
                        if (messageLength > 0)
                        {
                            messageData.AsSpan().CopyTo(fullDataSpan.Slice(TcpClientConstants.Protocol.MessageHeaderSize));
                        }

                        // 创建精确大小的数组用于事件处理
                        var fullData = new byte[fullDataLength];
                        fullDataSpan.CopyTo(fullData.AsSpan());

                        await ProcessReceivedDataAsync(fullData).ConfigureAwait(false);
                    }
                    finally
                    {
                        ArrayPool<byte>.Shared.Return(fullDataBuffer);
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "接收消息时发生错误");
                    Error?.Invoke(this, ex);
                    
                    // 短暂延迟后继续
                    await Task.Delay(1000, cancellationToken).ConfigureAwait(false);
                }
            }
        }
        finally
        {
            ArrayPool<byte>.Shared.Return(buffer);
        }
    }

    /// <summary>
    /// 处理接收到的消息
    /// </summary>
    /// <param name="message">消息</param>
    private Task ProcessReceivedMessageAsync(TcpMessage message)
    {
        // 触发消息接收事件
        MessageReceived?.Invoke(this, message);

        // 完成等待的消息任务
        if (_messageQueue.TryDequeue(out var tcs))
        {
            tcs.TrySetResult(message);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 处理接收到的数据
    /// </summary>
    /// <param name="data">数据</param>
    private Task ProcessReceivedDataAsync(byte[] data)
    {
        // 触发数据接收事件
        DataReceived?.Invoke(this, data);

        // 完成等待的数据任务
        if (_receiveQueue.TryDequeue(out var tcs))
        {
            tcs.TrySetResult(data);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 完成所有等待的任务
    /// </summary>
    private void CompleteAllPendingTasks()
    {
        // 完成所有等待的数据任务
        while (_receiveQueue.TryDequeue(out var dataTcs))
        {
            dataTcs.TrySetCanceled();
        }

        // 完成所有等待的消息任务
        while (_messageQueue.TryDequeue(out var messageTcs))
        {
            messageTcs.TrySetCanceled();
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        try
        {
            StopAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "释放消息处理器时发生错误");
        }

        _sendSemaphore.Dispose();
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        try
        {
            await StopAsync().ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "异步释放消息处理器时发生错误");
        }

        _sendSemaphore.Dispose();
        GC.SuppressFinalize(this);
    }
}
