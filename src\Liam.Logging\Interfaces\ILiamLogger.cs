using Liam.Logging.Constants;
using Liam.Logging.Models;
using System.Runtime.CompilerServices;

namespace Liam.Logging.Interfaces;

/// <summary>
/// Liam日志记录器接口，扩展了标准的日志功能
/// </summary>
public interface ILiamLogger
{
    /// <summary>
    /// 日志类别名称
    /// </summary>
    string CategoryName { get; }

    /// <summary>
    /// 最小日志级别
    /// </summary>
    LogLevel MinimumLevel { get; set; }

    /// <summary>
    /// 判断指定级别的日志是否启用
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <returns>是否启用</returns>
    bool IsEnabled(LogLevel level);

    /// <summary>
    /// 记录日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    void Log(LogLevel level, string message, Exception? exception = null,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0);

    /// <summary>
    /// 记录结构化日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="messageTemplate">消息模板</param>
    /// <param name="parameters">参数</param>
    void LogStructured(LogLevel level, string messageTemplate, params object[] parameters);

    /// <summary>
    /// 记录日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    void LogEvent(LogEvent logEvent);

    /// <summary>
    /// 异步记录日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    /// <returns>异步任务</returns>
    Task LogAsync(LogLevel level, string message, Exception? exception = null,
        CancellationToken cancellationToken = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0);

    /// <summary>
    /// 异步记录结构化日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="messageTemplate">消息模板</param>
    /// <param name="parameters">参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    Task LogStructuredAsync(LogLevel level, string messageTemplate, object[] parameters,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 异步记录日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    Task LogEventAsync(LogEvent logEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// 创建日志作用域
    /// </summary>
    /// <typeparam name="TState">状态类型</typeparam>
    /// <param name="state">状态对象</param>
    /// <returns>作用域释放器</returns>
    IDisposable BeginScope<TState>(TState state) where TState : notnull;

    /// <summary>
    /// 记录跟踪日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    void LogTrace(string message,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0);

    /// <summary>
    /// 记录调试日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    void LogDebug(string message,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0);

    /// <summary>
    /// 记录信息日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    void LogInformation(string message,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0);

    /// <summary>
    /// 记录警告日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    void LogWarning(string message, Exception? exception = null,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0);

    /// <summary>
    /// 记录错误日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    void LogError(string message, Exception? exception = null,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0);

    /// <summary>
    /// 记录严重错误日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    void LogCritical(string message, Exception? exception = null,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0);
}
