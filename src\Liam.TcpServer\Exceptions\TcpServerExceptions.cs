namespace Liam.TcpServer.Exceptions;

/// <summary>
/// TCP服务器基础异常类
/// </summary>
public abstract class TcpServerException : Exception
{
    /// <summary>
    /// 初始化TCP服务器异常
    /// </summary>
    /// <param name="message">异常消息</param>
    protected TcpServerException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化TCP服务器异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    protected TcpServerException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// 服务器启动异常
/// </summary>
public class ServerStartupException : TcpServerException
{
    /// <summary>
    /// 初始化服务器启动异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public ServerStartupException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化服务器启动异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public ServerStartupException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// 连接管理异常
/// </summary>
public class ConnectionManagementException : TcpServerException
{
    /// <summary>
    /// 连接ID
    /// </summary>
    public string? ConnectionId { get; }

    /// <summary>
    /// 初始化连接管理异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public ConnectionManagementException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化连接管理异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="connectionId">连接ID</param>
    public ConnectionManagementException(string message, string connectionId) : base(message)
    {
        ConnectionId = connectionId;
    }

    /// <summary>
    /// 初始化连接管理异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public ConnectionManagementException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// 初始化连接管理异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="connectionId">连接ID</param>
    /// <param name="innerException">内部异常</param>
    public ConnectionManagementException(string message, string connectionId, Exception innerException) : base(message, innerException)
    {
        ConnectionId = connectionId;
    }
}

/// <summary>
/// 消息处理异常
/// </summary>
public class MessageProcessingException : TcpServerException
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string? MessageId { get; }

    /// <summary>
    /// 初始化消息处理异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public MessageProcessingException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化消息处理异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="messageId">消息ID</param>
    public MessageProcessingException(string message, string messageId) : base(message)
    {
        MessageId = messageId;
    }

    /// <summary>
    /// 初始化消息处理异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public MessageProcessingException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// 初始化消息处理异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="innerException">内部异常</param>
    public MessageProcessingException(string message, string messageId, Exception innerException) : base(message, innerException)
    {
        MessageId = messageId;
    }
}

/// <summary>
/// 安全验证异常
/// </summary>
public class SecurityValidationException : TcpServerException
{
    /// <summary>
    /// 客户端IP地址
    /// </summary>
    public string? ClientIpAddress { get; }

    /// <summary>
    /// 初始化安全验证异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public SecurityValidationException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化安全验证异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="clientIpAddress">客户端IP地址</param>
    public SecurityValidationException(string message, string clientIpAddress) : base(message)
    {
        ClientIpAddress = clientIpAddress;
    }

    /// <summary>
    /// 初始化安全验证异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public SecurityValidationException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// 初始化安全验证异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="clientIpAddress">客户端IP地址</param>
    /// <param name="innerException">内部异常</param>
    public SecurityValidationException(string message, string clientIpAddress, Exception innerException) : base(message, innerException)
    {
        ClientIpAddress = clientIpAddress;
    }
}

/// <summary>
/// 配置验证异常
/// </summary>
public class ConfigurationValidationException : TcpServerException
{
    /// <summary>
    /// 配置项名称
    /// </summary>
    public string? ConfigurationKey { get; }

    /// <summary>
    /// 初始化配置验证异常
    /// </summary>
    /// <param name="message">异常消息</param>
    public ConfigurationValidationException(string message) : base(message)
    {
    }

    /// <summary>
    /// 初始化配置验证异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="configurationKey">配置项名称</param>
    public ConfigurationValidationException(string message, string configurationKey) : base(message)
    {
        ConfigurationKey = configurationKey;
    }

    /// <summary>
    /// 初始化配置验证异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public ConfigurationValidationException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// 初始化配置验证异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="configurationKey">配置项名称</param>
    /// <param name="innerException">内部异常</param>
    public ConfigurationValidationException(string message, string configurationKey, Exception innerException) : base(message, innerException)
    {
        ConfigurationKey = configurationKey;
    }
}
