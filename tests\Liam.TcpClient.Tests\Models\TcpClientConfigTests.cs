using FluentAssertions;
using Liam.TcpClient.Models;
using Xunit;

namespace Liam.TcpClient.Tests.Models;

public class TcpClientConfigTests
{
    [Fact]
    public void CreateDefault_ShouldReturnValidConfig()
    {
        // Arrange & Act
        var config = TcpClientConfig.CreateDefault("localhost", 8080);

        // Assert
        config.Should().NotBeNull();
        config.Host.Should().Be("localhost");
        config.Port.Should().Be(8080);
        config.EnableHeartbeat.Should().BeTrue();
        config.EnableAutoReconnect.Should().BeTrue();
        config.EnableSsl.Should().BeFalse();
    }

    [Fact]
    public void CreateSslConfig_ShouldReturnValidSslConfig()
    {
        // Arrange & Act
        var config = TcpClientConfig.CreateSslConfig("secure.example.com", 443, "secure.example.com");

        // Assert
        config.Should().NotBeNull();
        config.Host.Should().Be("secure.example.com");
        config.Port.Should().Be(443);
        config.EnableSsl.Should().BeTrue();
        config.SslConfig.Should().NotBeNull();
        config.SslConfig!.ServerName.Should().Be("secure.example.com");
    }

    [Fact]
    public void Validate_WithValidConfig_ShouldReturnValid()
    {
        // Arrange
        var config = new TcpClientConfig
        {
            Host = "localhost",
            Port = 8080,
            ConnectionTimeoutSeconds = 30,
            ReceiveBufferSize = 8192,
            SendBufferSize = 8192,
            HeartbeatIntervalSeconds = 60,
            HeartbeatTimeoutSeconds = 10,
            ReconnectIntervalSeconds = 5,
            MaxReconnectAttempts = 10,
            MaxMessageLength = 1024 * 1024
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Theory]
    [InlineData("", 8080, "Host不能为空")]
    [InlineData("localhost", 0, "Port必须在1-65535范围内")]
    [InlineData("localhost", 65536, "Port必须在1-65535范围内")]
    [InlineData("localhost", 8080, "ConnectionTimeoutSeconds必须大于0")]
    public void Validate_WithInvalidConfig_ShouldReturnInvalid(string host, int port, string expectedError)
    {
        // Arrange
        var config = new TcpClientConfig
        {
            Host = host,
            Port = port,
            ConnectionTimeoutSeconds = port == 8080 ? -1 : 30
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        var errorKeyword = expectedError.Split('必')[0];
        result.Errors.Should().Contain(e => e.Contains(errorKeyword));
    }

    [Fact]
    public void Validate_WithInvalidSslConfig_ShouldReturnInvalid()
    {
        // Arrange
        var config = new TcpClientConfig
        {
            Host = "localhost",
            Port = 8080,
            EnableSsl = true,
            SslConfig = new SslConfig
            {
                HandshakeTimeoutSeconds = -1
            }
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("SSL配置错误"));
    }

    [Fact]
    public void Validate_WithInvalidConnectionPoolConfig_ShouldReturnInvalid()
    {
        // Arrange
        var config = new TcpClientConfig
        {
            Host = "localhost",
            Port = 8080,
            ConnectionPoolConfig = new ConnectionPoolConfig
            {
                PoolSize = -1
            }
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("连接池配置错误"));
    }
}

public class SslConfigTests
{
    [Fact]
    public void Validate_WithValidConfig_ShouldReturnValid()
    {
        // Arrange
        var config = new SslConfig
        {
            ServerName = "example.com",
            HandshakeTimeoutSeconds = 30,
            CheckCertificateRevocation = true
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void Validate_WithInvalidHandshakeTimeout_ShouldReturnInvalid()
    {
        // Arrange
        var config = new SslConfig
        {
            HandshakeTimeoutSeconds = 0
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("HandshakeTimeoutSeconds必须大于0");
    }
}

public class ConnectionPoolConfigTests
{
    [Fact]
    public void Validate_WithValidConfig_ShouldReturnValid()
    {
        // Arrange
        var config = new ConnectionPoolConfig
        {
            Enabled = true,
            PoolSize = 10,
            IdleTimeoutSeconds = 300,
            MaxLifetimeSeconds = 3600
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Theory]
    [InlineData(-1, 300, 3600, "PoolSize必须大于0")]
    [InlineData(10, -1, 3600, "IdleTimeoutSeconds必须大于0")]
    [InlineData(10, 300, -1, "MaxLifetimeSeconds必须大于0")]
    public void Validate_WithInvalidConfig_ShouldReturnInvalid(int poolSize, int idleTimeout, int maxLifetime, string expectedError)
    {
        // Arrange
        var config = new ConnectionPoolConfig
        {
            PoolSize = poolSize,
            IdleTimeoutSeconds = idleTimeout,
            MaxLifetimeSeconds = maxLifetime
        };

        // Act
        var result = config.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(expectedError);
    }
}
