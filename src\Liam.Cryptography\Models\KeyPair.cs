namespace Liam.Cryptography.Models;

/// <summary>
/// 密钥对模型
/// </summary>
public class KeyPair
{
    /// <summary>
    /// 公钥
    /// </summary>
    public string? PublicKey { get; set; }

    /// <summary>
    /// 私钥
    /// </summary>
    public string? PrivateKey { get; set; }

    /// <summary>
    /// 密钥长度（位）
    /// </summary>
    public int KeySize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 密钥算法类型
    /// </summary>
    public string Algorithm { get; set; } = "RSA";

    /// <summary>
    /// 构造函数
    /// </summary>
    public KeyPair()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="publicKey">公钥</param>
    /// <param name="privateKey">私钥</param>
    /// <param name="keySize">密钥长度</param>
    /// <param name="algorithm">算法类型</param>
    public KeyPair(string publicKey, string privateKey, int keySize, string algorithm = "RSA")
    {
        PublicKey = publicKey;
        PrivateKey = privateKey;
        KeySize = keySize;
        Algorithm = algorithm;
    }

    /// <summary>
    /// 验证密钥对是否有效
    /// </summary>
    /// <returns>验证结果</returns>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(PublicKey) &&
               !string.IsNullOrWhiteSpace(PrivateKey) &&
               KeySize > 0;
    }

    /// <summary>
    /// 清除私钥（安全考虑）
    /// </summary>
    public void ClearPrivateKey()
    {
        PrivateKey = null!;
    }

    /// <summary>
    /// 获取公钥信息
    /// </summary>
    /// <returns>仅包含公钥的密钥对</returns>
    public KeyPair GetPublicKeyOnly()
    {
        return new KeyPair
        {
            PublicKey = PublicKey,
            PrivateKey = null!,
            KeySize = KeySize,
            Algorithm = Algorithm,
            CreatedAt = CreatedAt
        };
    }
}
